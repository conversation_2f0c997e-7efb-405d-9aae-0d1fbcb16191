<template>
  <view>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <UvUpload
      :class="{ ['single-box']: !props.multiple && state.urlList.length, ['hidden-add-btn']: props.onlyView }"
      :fileList="state.fileList"
      @afterRead="afterRead"
      @delete="deletePic"
      :multiple="props.multiple"
      :maxCount="10"
      v-bind="attrs"
    ></UvUpload>
  </view>
</template>

<script setup>
  import { ref, reactive, watch, useAttrs } from 'vue'
  import { useUserStore } from '@/store/modules/user'
  import UvUpload from './uv-upload/components/uv-upload/uv-upload.vue'

  const attrs = useAttrs()
  const userStore = useUserStore()
  const props = defineProps({
    multiple: {
      type: Boolean,
      default: true
    },
    onlyView: {
      type: Boolean,
      default: false
    },
    urls: {},
    folderName: { default: '' }
  })

  const state = reactive({
    fileList: [],
    urlList: []
  })
  const uNotifyRef = ref()

  const emits = defineEmits(['update:urls'])

  watch(
    () => props.urls,
    (newVal, oldVal) => {
      if (props.multiple) {
        state.urlList = newVal.map(el => ({
          name: el,
          url: el
        }))
      } else {
        state.urlList = newVal
          ? [
              {
                name: newVal.split(`${process.env.VUE_APP_MINIO_BUCKET}/`)[1],
                url: newVal
              }
            ]
          : []
      }
      state.fileList = JSON.parse(JSON.stringify(state.urlList))
    },
    { deep: true }
  )

  // 删除图片
  const deletePic = event => {
    state.fileList.splice(event.index, 1)
    state.urlList.splice(event.index, 1)

    if (props.multiple) {
      emits(
        'update:urls',
        state.urlList.map(el => el.url)
      )
    } else {
      emits('update:urls', state.urlList?.[0] || [])
    }
  }

  // 新增图片
  const afterRead = async event => {
    let lists = [].concat(event.file).map(el => {
      let arr = el.url.split('/')
      return { ...el, name: arr[arr.length - 1] }
    })

    let fileListLen = state.fileList.length

    lists.map(item => {
      state.fileList.push({
        ...item,
        status: 'uploading',
        message: '上传中'
      })
    })

    for (let i = 0; i < lists.length; i++) {
      const result = await uploadFilePromise(lists[i])

      let item = state.fileList[fileListLen]
      state.fileList.splice(fileListLen, 1, {
        ...item,
        status: 'success',
        message: '',
        url: result
      })
      fileListLen++

      state.urlList.push({ url: result })

      console.log('urls', state.urlList)

      emits(
        'update:urls',
        state.urlList.map(el => el.url)
      )
    }
  }

  const uploadFilePromise = item => {
    return new Promise((resolve, reject) => {
      let a = uni.uploadFile({
        url: `${import.meta.env.VITE_BASE_API}/external/minio/upload`,
        filePath: item.url,
        name: 'file',
        header: {
          token: userStore.token
        },
        formData: {
          filePath: props.folderName ? `${props.folderName}/${item.name}` : item.name
        },
        success: res => {
          setTimeout(() => {
            resolve(JSON.parse(res.data).data)
          }, 50)
        }
      })
    })
  }
</script>

<style lang="scss" scoped>
  :deep(.single-box) {
    .uv-upload__button {
      display: none;
    }
  }

  :deep(.hidden-add-btn) {
    .uv-upload__button {
      display: none;
    }
  }

  :deep(.uv-upload__deletable) {
    height: 32rpx;
    width: 32rpx;
    .uvicon-close {
      font-size: 24rpx !important;
    }
  }
</style>
