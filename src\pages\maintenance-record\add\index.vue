<template>
  <view class="page-nav-top-common">
    <NavBar title="新增" :background="{ backgroundColor: '#fff' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y>
      <u-form :model="state.form" ref="formRef" label-width="150" :error-type="['border-bottom', 'toast']">
        <view class="com-card">
          <u-form-item label="维养单位">
            <text>{{ state.loginOrgName }}</text>
          </u-form-item>
          <u-form-item class="part-form" label="维养部位" prop="objectCategoryCode" required>
            <u-radio-group v-model="state.form.objectCategoryCode">
              <u-radio @change="changeType" v-for="(item, index) in typeOptions" :key="index" :name="item.value">
                <text class="radio-text" :class="{ activeRadio: item.value == state.form.objectCategoryCode }">
                  {{ item.name }}
                </text>
              </u-radio>
            </u-radio-group>
          </u-form-item>
        </view>
        <view class="com-card">
          <u-form-item label="维养范围" prop="objectIds" required>
            <u-input
              v-model="state.select.current1.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'scope'
                  state.select.list = state.scopeOptions
                }
              "
              placeholder="请选择维养范围"
            />
          </u-form-item>
          <u-form-item label="维养时间" prop="maintenanceDate" required>
            <u-input
              v-model="state.form.maintenanceDate"
              type="select"
              @click="state.showTimePicker = true"
              placeholder="请选择维养时间"
            />
          </u-form-item>
          <u-form-item label="维养状态" prop="status" required>
            <u-input
              v-model="state.statusName"
              type="select"
              @click="state.showStatus = true"
              placeholder="请选择维养状态"
            />
          </u-form-item>
          <u-form-item label="维养人员" prop="userIds" required>
            <u-input
              v-model="state.select.current2.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'user'
                  state.select.list = state.userOptions
                }
              "
              placeholder="请选择维养人员"
            />
          </u-form-item>
          <u-form-item label="维养内容" prop="content" required>
            <u-input class="textarea" v-model="state.form.content" placeholder="请输入" />
          </u-form-item>
          <u-form-item label="备注">
            <u-input class="textarea remark" v-model="state.form.remark" placeholder="请输入" />
          </u-form-item>
        </view>
      </u-form>
    </scroll-view>

    <u-button
      class="btn-primary"
      :disabled="
        state.form.objectIds?.length == 0 &&
        !state.form.maintenanceDate &&
        !state.form.status &&
        !state.form.userIds &&
        !state.form.content
      "
      type="primary"
      @click="submitBtn"
    >
      提交
    </u-button>

    <MultiSelect
      v-model="state.select.show"
      :selectOptions="state.select.list"
      @onMultipleChoice="onMultipleChoice"
      @close="
        () => {
          state.select.show = false
        }
      "
    />

    <u-picker
      v-model="state.showTimePicker"
      :default-time="state.defaultTime"
      mode="time"
      :params="{ year: true, month: true, day: true }"
      @confirm="onTimeConfirm"
    ></u-picker>

    <u-select
      v-model="state.showStatus"
      mode="single-column"
      :list="state.statusOptions"
      @cancel="state.showStatus = false"
      @confirm="onSelectConfirm"
    ></u-select>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { addMaintenance } from '../services'
  import { getOptions, getComUserList, getProjectTree, getRiverSystemList, getBaseSite } from '@/api/common'
  import MultiSelect from '@/components/MultiSelect/index.vue'
  import dayjs from 'dayjs'

  const typeOptions = [
    { name: '水利工程', value: 'HP' },
    { name: '水系', value: 'RL' },
    { name: '监测站点', value: 'MS' },
  ]

  const router = useRouter()

  const uNotifyRef = ref()
  const formRef = ref(null)

  const state = reactive({
    loginOrgName: JSON.parse(uni.getStorageSync('user'))?.loginOrg?.loginOrgName,

    showTimePicker: false,
    defaultTime: dayjs().format('YYYY-MM-DD'),

    statusName: '',

    statusOptions: [],
    userOptions: [],
    select: {
      show: false,
      field: undefined,
      current1: { label: undefined, value: undefined },
      current2: { label: undefined, value: undefined },
      list: [],
    },

    form: {
      content: '',
      maintenanceDate: '',
      objectCategoryCode: 'HP',
      objectIds: [],
      positionAttaches: [],
      remark: '',
      status: '',
      userIds: '',
    },
    rules: {
      objectCategoryCode: [{ required: true, message: '维养部位不能为空' }],
      objectIds: [{ required: true, message: '维养范围不能为空' }],
      maintenanceDate: [{ required: true, message: '维养时间不能为空' }],
      status: [{ required: true, message: '维养状态不能为空' }],
      userIds: [{ required: true, message: '维养人员不能为空' }],
      content: [{ required: true, message: '维养内容不能为空' }],
    },
  })

  onMounted(() => {
    formRef.value.setRules(state.rules)
    getWaterConservancy()
    getOptions('maintenanceStatus').then(res => {
      state.statusOptions = (res?.data || []).map(el => ({ label: el.value, value: el.key }))
    })
    getComUserList({ isOnlyOrg: true }).then(res => {
      state.userOptions = res?.data?.map(el => ({ label: el.name, value: el.userId }))
    })
  })

  //水利工程
  const getWaterConservancy = () => {
    getProjectTree({ objectCategoryId: '3' }).then(res => {
      state.scopeOptions = res.data.map(e => {
        return {
          value: e.projectId,
          label: e.projectName,
        }
      })
    })
  }
  //水系
  const getRiverSystem = () => {
    getRiverSystemList().then(res => {
      state.scopeOptions = res.data.map(e => {
        return {
          value: e.riverSystemId,
          label: e.riverSystemName,
        }
      })
    })
  }

  //监测站点
  const getMonitoringStations = () => {
    getBaseSite().then(res => {
      state.scopeOptions = res.data.map(e => {
        return {
          value: e.siteId,
          label: e.siteName,
        }
      })
    })
  }
  const changeType = e => {
    if (e == 'HP') {
      getWaterConservancy()
    } else if (e == 'RL') {
      getRiverSystem()
    } else if (e == 'MS') {
      getMonitoringStations()
    }
  }

  const onMultipleChoice = (ids, names) => {
    state.select.show = false
    switch (state.select.field) {
      case 'scope':
        state.select.current1.label = names
        state.form.objectIds = ids.split(',')
        return
      case 'user':
        state.select.current2.label = names
        state.form.userIds = ids
        return
      default:
        return
    }
  }

  const onTimeConfirm = p => {
    state.form.maintenanceDate = dayjs(`${p.year}-${p.month}-${p.day}`).format('YYYY-MM-DD')
  }

  const onSelectConfirm = arr => {
    state.form.status = arr[0].value
    state.statusName = arr[0].label
  }
  const submitBtn = () => {
    formRef.value.validate(valid => {
      if (valid) {
        addMaintenance(state.form).then(res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '成功',
            duration: 800,
          })
          setTimeout(() => {
            router.push({
              path: '/pages/maintenance-record/index',
            })
          }, 800)
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    :deep(.u-radio__icon-wrap) {
      visibility: hidden;
    }
    :deep(.u-form-item--right) {
      line-height: 36rpx;
    }
    .radio-text {
      height: 64rpx;
      border-radius: 8rpx;
      color: #3d3d3d;
      font-size: 30rpx;
      padding: 4rpx 18rpx;
      background: #f2f3f5;
      margin-left: -42rpx;
    }
    .activeRadio {
      color: #fff;
      background: #165dff;
    }
    .part-form {
      :deep(.u-form-item__body) {
        display: block;
      }
    }
    .textarea {
      height: 268rpx;
      border-radius: 8rpx;
      background: #f2f3f5;
      border: 1px solid #e5e6eb;
    }
    .remark {
      height: 120rpx;
    }
  }
  .btn-primary {
    width: 90%;
  }
</style>
