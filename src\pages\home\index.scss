.home-content {
  width: 100vw;
  height: 100vh;

  background: url('@/static/images/home-bg.png') no-repeat;
  background-size: 100%;

  .header {
    width: 100%;
    padding: 120rpx 0 280rpx;
    display: flex;
    justify-content: space-between;
    .title-group {
      flex: 1;
      display: flex;
      align-items: center;
      padding-left: 32rpx;
      .logo {
        width: 70rpx;
        height: 66rpx;
      }
      .title {
        line-height: 52rpx;
        font-size: 48rpx;
        font-family: DingTalk JinBuTi-Regular, DingTalk JinBuTi;
        color: #1d2129;
        font-weight: 500;
      }
    }
    .user-name {
      flex: 1;
      text-align: right;
      padding-right: 30rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64rpx;
        height: 64rpx;
        color: #ffffff;
        font-size: 24rpx;
        background: #4080ff;
        border-radius: 50%;
        box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(55, 114, 255, 0.24);
        border: 4rpx solid #ffffff;
      }
      .user-name-text {
        font-size: 32rpx;
        color: #1d2129;
        line-height: 54rpx;
        margin-left: 10rpx;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .section {
    padding: 32rpx 10rpx;
    display: flex;
    flex-wrap: wrap;
    .app {
      width: 25%;
      .img {
        width: 110rpx;
        height: 110rpx;
        margin-bottom: 20rpx;
      }
      .label {
        line-height: 33rpx;
        font-size: 24rpx;
        font-weight: 300;
        color: #1d2129;
        font-family: PingFang SC-Regular, PingFang SC;
      }
      &:nth-child(4n) {
        margin-right: 0;
      }
    }
  }
}
