<template>
  <view class="page-nav-top-common">
    <NavBar title="新增" :background="{ backgroundColor: '#fff' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y>
      <u-form :model="state.form" ref="formRef" label-width="150" :error-type="['border-bottom', 'toast']">
        <view class="com-card">
          <u-form-item label="提报单位">
            <text>{{ state.loginOrgName }}</text>
          </u-form-item>
          <u-form-item class="part-form" label="应急部位" prop="objectCategoryCode" required>
            <u-radio-group v-model="state.form.objectCategoryCode">
              <u-radio @change="changeType" v-for="(item, index) in typeOptions" :key="index" :name="item.value">
                <text class="radio-text" :class="{ activeRadio: item.value == state.form.objectCategoryCode }">
                  {{ item.name }}
                </text>
              </u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item class="part-form" label="应急类型" prop="emergencyType" required>
            <u-radio-group v-model="state.form.emergencyType">
              <u-radio
                @change="changeType"
                v-for="(item, index) in emergencyTypeOptions"
                :key="index"
                :name="item.value"
              >
                <text class="radio-text" :class="{ activeRadio: item.value == state.form.emergencyType }">
                  {{ item.name }}
                </text>
              </u-radio>
            </u-radio-group>
          </u-form-item>
        </view>
        <view class="com-card">
          <u-form-item class="text-form" label="所属工程/水系/站点" prop="objectIds" required>
            <u-input
              v-model="state.select.current1.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'scope'
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="问题描述" prop="content" required>
            <u-input class="textarea" v-model="state.form.content" placeholder="请输入" />
          </u-form-item>
          <u-form-item label="备注">
            <u-input class="textarea remark" v-model="state.form.remark" placeholder="请输入" />
          </u-form-item>
          <u-form-item label="附件">
            <MyUpload
              :urls="state.form.positionAttaches"
              @update:urls="
                urls => {
                  state.form.positionAttaches = urls
                }
              "
              folderName="emergency-repair"
            ></MyUpload>
          </u-form-item>
        </view>
      </u-form>
    </scroll-view>

    <u-button
      class="btn-primary"
      :disabled="state.form.objectIds?.length == 0 && !state.form.content"
      type="primary"
      @click="submitBtn"
    >
      提交
    </u-button>

    <MultiSelect
      v-show="state.select.list?.length"
      v-model="state.select.show"
      :selectOptions="state.select.list"
      @onMultipleChoice="onMultipleChoice"
      @close="
        () => {
          state.select.show = false
        }
      "
    />
  </view>
</template>

<script setup>
  import { reactive, ref, onBeforeMount, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { addEmergencyRepair } from '../services'
  import { getTaskAbnormal } from '@/pages/patrol/patrol-detail/services'
  import { getOptions, getComUserList, getProjectTree, getRiverSystemList, getBaseSite } from '@/api/common'
  import MultiSelect from '@/components/MultiSelect/index.vue'
  import MyUpload from '@/components/MyUpload/index.vue'

  const typeOptions = [
    { name: '水利工程', value: 'HP' },
    { name: '水系', value: 'RL' },
    { name: '监测站点', value: 'MS' },
  ]
  const emergencyTypeOptions = [
    { name: '异常情况', value: 2 },
    { name: '隐患问题', value: 1 },
  ]

  const router = useRouter()
  const props = defineProps(['taskId'])

  const uNotifyRef = ref()
  const formRef = ref(null)

  const state = reactive({
    loginOrgName: JSON.parse(uni.getStorageSync('user'))?.loginOrg?.loginOrgName,

    statusName: '',
    loading: false,
    statusOptions: [],
    userOptions: [],
    select: {
      show: false,
      field: undefined,
      current1: { label: undefined, value: undefined },
      list: [],
    },

    form: {
      content: '',
      emergencyType: 2,
      objectCategoryCode: 'HP',
      objectIds: [],
      positionAttaches: [],
      remark: '',
    },
    rules: {
      objectCategoryCode: [{ required: true, message: '应急部位不能为空' }],
      emergencyType: [{ required: true, message: '应急类型不能为空' }],
      objectIds: [{ required: true, message: '所属工程/水系/站点不能为空' }],

      content: [{ required: true, message: '问题描述不能为空' }],
    },
  })
  onBeforeMount(() => {
    getWaterConservancy()
  })
  onMounted(() => {
    formRef.value.setRules(state.rules)
    getOptions('maintenanceStatus').then(res => {
      state.statusOptions = (res?.data || []).map(el => ({ label: el.value, value: el.key }))
    })
    getComUserList({ isOnlyOrg: true }).then(res => {
      state.userOptions = res?.data?.map(el => ({ label: el.name, value: el.userId }))
    })
  })
  //巡检工单中所有出现异常结果
  const getPatrol = () => {
    if (props?.taskId == undefined) return
    const params = {
      taskId: props?.taskId,
      objectType:
        state.form.objectCategoryCode == 'HP'
          ? 1
          : state.form.objectCategoryCode == 'RL'
          ? 3
          : state.form.objectCategoryCode == 'MS'
          ? 2
          : undefined,
    }
    getTaskAbnormal(params).then(res => {
      if (res?.data?.objectRelIds?.length) {
        state.form.objectIds = res?.data?.objectRelIds
        const nameArray = res?.data?.objectRelIds.map(id => {
          const foundItem = state.select.list?.find(item => item.value == id)
          return foundItem ? foundItem.label : ''
        })
        console.log(nameArray)
        state.select.current1.label = nameArray?.join(',')
      }
    })
  }

  //水利工程
  const getWaterConservancy = () => {
    getProjectTree({ objectCategoryId: '3' }).then(res => {
      state.select.list = res.data.map(e => {
        return {
          value: e.projectId + '',
          label: e.projectName,
        }
      })
      getPatrol()
    })
  }
  //水系
  const getRiverSystem = () => {
    getRiverSystemList().then(res => {
      state.select.list = res.data.map(e => {
        return {
          value: e.riverSystemId + '',
          label: e.riverSystemName,
        }
      })
    })
    getPatrol()
  }

  //监测站点
  const getMonitoringStations = () => {
    getBaseSite().then(res => {
      state.select.list = res.data.map(e => {
        return {
          value: e.siteId + '',
          label: e.siteName,
        }
      })
      getPatrol()
    })
  }
  const changeType = e => {
    state.select.current1.label = ''
    state.form.objectIds = []
    if (e == 'HP') {
      getWaterConservancy()
    } else if (e == 'RL') {
      getRiverSystem()
    } else if (e == 'MS') {
      getMonitoringStations()
    }
  }

  const onMultipleChoice = (ids, names) => {
    state.select.show = false
    switch (state.select.field) {
      case 'scope':
        state.select.current1.label = names
        state.form.objectIds = ids.split(',')
        return
      default:
        return
    }
  }

  const submitBtn = () => {
    formRef.value.validate(valid => {
      if (valid) {
        addEmergencyRepair(state.form).then(res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '成功',
            duration: 800,
          })
          setTimeout(() => {
            router.push({
              path: '/pages/emergency-repair/index',
            })
          }, 800)
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .select-loading {
    position: fixed;
    left: 50%;
    top: 30%;
    z-index: 999;
  }
  .container {
    :deep(.u-radio__icon-wrap) {
      visibility: hidden;
    }
    :deep(.u-form-item--right) {
      line-height: 36rpx;
    }
    .text-form {
      :deep(.u-form-item--left) {
        flex: 1 !important;
      }
    }
    .radio-text {
      height: 64rpx;
      border-radius: 8rpx;
      color: #3d3d3d;
      font-size: 30rpx;
      padding: 4rpx 18rpx;
      background: #f2f3f5;
      margin-left: -42rpx;
    }
    .activeRadio {
      color: #fff;
      background: #165dff;
    }
    .part-form {
      :deep(.u-form-item__body) {
        display: block;
      }
    }
    .textarea {
      height: 268rpx;
      border-radius: 8rpx;
      background: #f2f3f5;
      border: 1px solid #e5e6eb;
    }
    .remark {
      height: 120rpx;
    }
  }
  .btn-primary {
    width: 90%;
  }
</style>
