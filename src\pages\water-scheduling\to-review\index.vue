<template>
  <view class="page-nav-top-common-bg2">
    <NavBar :title="navTitle" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    <view class="com-card">
      <u-form
        :model="state.form"
        ref="formRef"
        :rules="state.rules"
        label-position="top"
        :error-type="['border-bottom', 'toast']"
      >
        <u-form-item label="开启高度" prop="highValue" required :border-bottom="true" v-if="state.source == 'start'">
          <u-input type="number" v-model="state.form.highValue" placeholder="请输入"></u-input>
          m
        </u-form-item>
        <u-form-item
          label="调度开始时间"
          prop="startDate"
          required
          :border-bottom="true"
          v-if="state.source == 'start'"
        >
          <u-input type="input" v-model="state.form.startDate" placeholder="请选择" disabled></u-input>
          <u-icon class="clock-btn" name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('start')"></u-icon>
        </u-form-item>

        <u-form-item label="水库放水量" prop="waterFlow" required :border-bottom="true" v-if="state.source == 'end'">
          <u-input v-model="state.form.waterFlow" @blur="onInputFlow" placeholder="请输入"></u-input>
          万m³
        </u-form-item>
        <u-form-item label="调度结束时间" prop="endDate" required :border-bottom="true" v-if="state.source == 'end'">
          <u-input type="input" v-model="state.form.endDate" placeholder="请选择" disabled></u-input>
          <u-icon class="clock-btn" name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('end')"></u-icon>
        </u-form-item>

        <u-form-item label="复核单位" :border-bottom="true" v-if="state.source == 'review'">
          <u-input type="input" v-model="state.reportingUnit" placeholder="请输入" disabled></u-input>
        </u-form-item>
        <u-form-item
          label="田间用水量"
          prop="fieldWaterFlow"
          required
          :border-bottom="true"
          v-if="state.source == 'review'"
        >
          <u-input v-model="state.form.fieldWaterFlow" @blur="onInputField" placeholder="请输入"></u-input>
          万m³
        </u-form-item>
      </u-form>
      <u-picker
        v-model="state.showPickerTime"
        mode="time"
        :default-time="dayjs().format('YYYY-MM-DD HH:mm:ss')"
        :params="state.timeParams"
        @cancel="state.showPickerTime = false"
        @confirm="onTimeConfirm"
      ></u-picker>
    </view>
    <u-button class="btn-primary" type="primary" @click="submitBtn">提交</u-button>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, computed, nextTick } from 'vue'
  import { useRouter, useRoute } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import * as _ from 'lodash'
  import { reviewWaterLedger, startUpdateWaterLedger, endUpdateWaterLedger } from '../services'
  import dayjs from 'dayjs'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const formRef = ref(null)

  const props = defineProps(['source', 'consumptionId', 'startDate'])
  const navTitle = computed(() => {
    if (props.source == 'start') return '开始调度'
    if (props.source == 'end') return '结束调度'
    if (props.source == 'review') return '水量复核'
  })

  const state = reactive({
    navTitle: '',
    source: '',
    showPickerTime: false,
    pickType: '',

    reportingUnit: JSON.parse(uni.getStorageSync('user'))?.loginOrg?.loginOrgName,
    startDate: props.startDate,

    timeParams: {
      year: true,
      month: true,
      day: true,
      hour: true,
      minute: true,
      second: true,
    },

    form: {
      consumptionId: props?.consumptionId,
      waterFlow: undefined,
      startDate: undefined,
      endDate: undefined,
      waterFlow: undefined,

      fieldWaterFlow: undefined,
    },
    rules: {
      startDate: [{ required: true, message: '调度开始时间不能为空', trigger: 'change, blur' }],
      highValue: [{ required: true, message: '开启高度不能为空', trigger: 'change, blur' }],

      endDate: [{ required: true, message: '结束调度时间不能为空', trigger: 'change, blur' }],
      waterFlow: [{ type: 'number', required: true, min: 0, message: '水库放水量不能为空', trigger: 'change, blur' }],

      fieldWaterFlow: [
        { type: 'number', required: true, min: 0, message: '田间用水量不能为空', trigger: 'change, blur' },
      ],
    },
  })

  onMounted(() => {
    formRef.value.setRules(state.rules)
    state.source = router.route.value.query?.source
  })

  const onInputFlow = val => {
    if (val < 0) {
      nextTick(() => {
        state.form.waterFlow = undefined
        return
      })
    }
  }
  //
  const onInputField = val => {
    if (val < 0) {
      nextTick(() => {
        state.form.fieldWaterFlow = undefined
        return
      })
    }
  }
  const onOpenTimePicker = type => {
    state.pickType = type
    state.showPickerTime = true
  }

  const onTimeConfirm = p => {
    if (state.pickType == 'start') {
      state.form.startDate = `${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}:${p.second}`
    }
    if (state.pickType == 'end') {
      state.form.endDate = `${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}:${p.second}`
    }
  }
  //提交
  const submitBtn = () => {
    formRef.value.validate(valid => {
      if (valid) {
        let submitApi = undefined
        if (state.source == 'start') {
          submitApi = startUpdateWaterLedger
        } else if (state.source == 'end') {
          submitApi = endUpdateWaterLedger
          if (state.form.endDate < state.startDate) {
            uNotifyRef.value.show({
              type: 'warning',
              title: '结束时间不能小于开始时间',
              duration: 800,
            })
            return
          }
        } else if (state.source == 'review') {
          submitApi = reviewWaterLedger
        }
        submitApi(state.form).then(res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '成功',
            duration: 800,
          })
          setTimeout(() => {
            router.push({
              path: '/pages/water-scheduling/index',
            })
          }, 800)
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .btn-primary {
    width: 90%;
  }
</style>
