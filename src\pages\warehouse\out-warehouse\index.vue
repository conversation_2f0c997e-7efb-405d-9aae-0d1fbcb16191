<template>
  <view class="page-nav-top-common">
    <NavBar title="出库管理" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
  </view>

  <view class="relative">
    <u-dropdown
      ref="dropdownRef"
      height="60"
      title-size="28"
      menu-icon="arrow-down-fill"
      menu-icon-size="20"
      style="margin: 20rpx 0"
      @open="onDropdownOpen"
    >
      <u-dropdown-item
        height="600"
        v-model="state.warehouseVal.value"
        :title="state.warehouseVal.title"
        :options="state.warehouseOptions"
        @change="onWarehouseValChange"
      ></u-dropdown-item>

      <u-dropdown-item
        height="600"
        v-model="state.manVal.value"
        :title="state.manVal.title"
        :options="state.manList"
        @change="onManValChange"
      ></u-dropdown-item>

      <u-dropdown-item :title="state.timePicker.title"></u-dropdown-item>
    </u-dropdown>
  </view>

  <ZPaging ref="pagingRef" style="margin-top: calc(44px + 160rpx); z-index: 10" v-model="state.list" @query="getList">
    <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
      <view class="header">
        <text class="header-name">出库单号: {{ el.outboundNo || '-' }}</text>
      </view>

      <view class="content">
        <view class="content-title">
          <view class="title">
            {{ el.warehouseName || '-' }}
          </view>
        </view>

        <view class="indicate-item">出库时间: {{ el.outboundTime || '-' }}</view>

        <view class="mt-[10rpx]">
          <view class="flex item-center justify-between">
            <text class="text-[13px] text-[#86909C]">出库人&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ el.stockmanName || '-' }}</text>
          </view>
          <view class="flex item-center justify-between mt-[10rpx]">
            <text class="text-[13px] text-[#86909C]">申请人&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ el.proposerName || '-' }}</text>
          </view>
          <view class="flex item-center justify-between mt-[10rpx]">
            <text class="text-[13px] text-[#86909C]">出库类型&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">
              {{ outboundTypeOptions.find(ele => ele.value == el.outboundType)?.label || '-' }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </ZPaging>

  <u-picker
    v-model="state.timePicker.showPicker"
    :default-time="state.timePicker.defaultTime"
    mode="time"
    :params="{ year: true, month: true, day: true, hour: false, minute: false, second: false }"
    cancel-text="重置"
    @confirm="onTimeConfirm"
    @cancel="onTimeCancel"
    @close="onTimeClose"
  ></u-picker>

  <LimeFab :offset="{ x: -1, y: 400 }" heightGap="60">
    <view class="add-btn" @click="onAddClick">
      <u-icon name="plus" color="#ffffff" size="36"></u-icon>
    </view>
  </LimeFab>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { outPage, getConfigPage, getOutManList } from '../services'
  import LimeFab from '@/components/lime-fab/index.vue'
  import dayjs from 'dayjs'
  import { outboundTypeOptions } from './config.ts'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)

  const state = reactive({
    warehouseOptions: [],
    warehouseVal: { value: undefined, title: '所属仓库', preVal: undefined },
    manList: [],
    manVal: { value: undefined, title: '出库人', preVal: undefined },
    timePicker: {
      showPicker: false,
      defaultTime: dayjs().format('YYYY-MM-DD'),
      title: '出库日期',
      startTime: undefined,
      endTime: undefined,
    },
    list: [],
  })

  onShow(() => {
    getList(1)
  })

  getConfigPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.warehouseOptions = (res.data?.data || []).map(el => ({
      ...el,
      label: el.warehouseName,
      value: el.warehouseId,
    }))
  })

  getOutManList().then(res => {
    state.manList = (res.data || []).map(el => ({ ...el, label: el.checkManName, value: el.checkManId }))
  })

  const getList = (pageNo, pageSize) => {
    outPage({
      warehouseId: state.warehouseVal.value,
      proposerId: state.manVal.value,
      startTime: state.timePicker.startTime,
      endTime: state.timePicker.endTime,
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  function onWarehouseValChange(value) {
    if (value === state.warehouseVal.preVal) {
      state.warehouseVal = { title: '所属仓库', value: undefined, preVal: undefined }
    } else {
      state.warehouseVal = {
        title: state.warehouseOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    pagingRef.value.reload()
  }

  function onManValChange(value) {
    if (value === state.manVal.preVal) {
      state.manVal = { title: '出库人', value: undefined, preVal: undefined }
    } else {
      state.manVal = {
        title: state.manList.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }
    pagingRef.value.reload()
  }

  const onDropdownOpen = index => {
    if (index === 2) {
      nextTick(() => {
        state.timePicker.showPicker = true
      })
    }
  }
  const onTimeConfirm = p => {
    state.timePicker = {
      ...state.timePicker,
      startTime: dayjs(`${p.year}-${p.month}-${p.day}`).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(`${p.year}-${p.month}-${p.day}`).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      title: `${p.year}-${p.month}-${p.day}`,
    }

    pagingRef.value.reload()
    dropdownRef.value.close()
  }
  const onTimeCancel = () => {
    state.timePicker = {
      ...state.timePicker,
      startTime: undefined,
      endTime: undefined,
      title: '出库日期',
    }
    pagingRef.value.reload()
    dropdownRef.value.close()
  }
  const onTimeClose = () => {
    dropdownRef.value.close()
  }

  const onItemClick = el => {
    router.push({ path: '/pages/warehouse/out-warehouse/detail/index', query: { outboundId: el.outboundId } })
  }

  const onAddClick = () => {
    router.push({ path: '/pages/warehouse/out-warehouse/add/index' })
  }
</script>

<style lang="scss" scoped>
  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 32rpx;
      background: #e8f3ff;

      .header-name {
        font-size: 28rpx;
        color: #4e5969;
        line-height: 28rpx;
      }
    }

    .content {
      padding: 0 32rpx 32rpx;
      .content-title {
        font-size: 32rpx;
        color: #1d2129;
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        .title {
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .indicate-item {
        font-size: 28rpx;
        color: #86909c;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }

  .add-btn {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: linear-gradient(44deg, #3772ff 13%, #5ddcf5 82%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
