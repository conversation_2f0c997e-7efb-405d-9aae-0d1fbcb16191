<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="入库详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <u-cell-group :border="false">
          <u-cell-item
            title="入库单号"
            :arrow="false"
            :value="state.detail.inboundNo || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="所属仓库"
            :arrow="false"
            :value="state.detail.warehouseName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="入库来源"
            :arrow="false"
            :value="inboundTypeOptions.find(el => el.value == state.detail.inboundType)?.label || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="入库日期"
            :arrow="false"
            :value="state.detail.inboundTime || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="验收人"
            :arrow="false"
            :value="state.detail.stockmanName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="采购人"
            :arrow="false"
            :value="state.detail.purchaserName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="供应商"
            :arrow="false"
            :value="state.detail.supplier || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="发票号"
            :arrow="false"
            :value="state.detail.invoiceNo || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="入库摘要"
            :arrow="false"
            :value="state.detail.remark || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
        </u-cell-group>
      </view>

      <view class="list-content">
        <u-section title="库存信息" class="mt-[30rpx]" font-size="34" line-color="#3772FF" :right="false"></u-section>

        <Table :tableData="state.detail.inRecords" :column="tableColumns">
          <template v-if="state.detail.inRecords.length" #totalRow>
            <u-tr>
              <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
                <text v-if="ele.title === '序号'">合计</text>
                <text v-else-if="ele.dataIndex === 'inboundQty'">
                  {{ _.sum(state.detail.inRecords.map(el => el.inboundQty)) }}
                </text>
                <text v-else-if="ele.dataIndex === 'price'">
                  {{ _.sum(state.detail.inRecords.map(el => el.price)) }}
                </text>

                <text v-else>　</text>
              </u-td>
            </u-tr>
          </template>

          <template #action="{ record }">
            <view class="text-[#165DFF]" @click.stop="onDetailClick(record)">查看</view>
          </template>
        </Table>
      </view>
    </scroll-view>
  </view>

  <u-popup
    v-model="state.isShowDetail"
    mode="bottom"
    class="confirm-popup"
    closeable
    @close="state.isShowDetail = false"
    border-radius="32"
  >
    <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">物料详情</view>
    <u-line color="#F2F3F5" />

    <view class="px-[40rpx] py-[32rpx]">
      <u-cell-group :border="false">
        <u-cell-item
          title="备件编码"
          :arrow="false"
          :value="state.currentRow.goodsCode || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件名称"
          :arrow="false"
          :value="state.currentRow.goodsName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件规格"
          :arrow="false"
          :value="state.currentRow.goodsSpec || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件型号"
          :arrow="false"
          :value="state.currentRow.goodsModel || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件批号"
          :arrow="false"
          :value="state.currentRow.batchNo || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>

      <view class="grid grid-cols-2 gap-[20rpx] mt-[40rpx]">
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">入库数量</text>
          <text class="text-[15px]">{{ state.currentRow.inboundQty || '-' }}</text>
        </view>
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">单价(元)</text>
          <text class="text-[15px]">{{ state.currentRow.unitPrice || '-' }}</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { inGetDetail } from '../../services'
  import Table from '@/components/MyTable/index.vue'
  import * as _ from 'lodash'
  import { inboundTypeOptions } from '../config.ts'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '备件编码', dataIndex: 'goodsCode', width: 140 },
    { title: '备件名称', dataIndex: 'goodsName', width: 200 },
    { title: '入库数量', dataIndex: 'inboundQty', width: 120 },
    { title: '金额(元)', dataIndex: 'price', width: 120 },
    { title: '备件详情', slotName: 'action', width: 140 }
  ]

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)

  const props = defineProps(['inboundId'])

  const state = reactive({
    detail: { inRecords: [] },
    isShowDetail: false,
    currentRow: {}
  })

  const getList = (pageNo, pageSize) => {
    inGetDetail({
      inboundId: props.inboundId
    }).then(res => {
      state.detail = {
        ...res.data,
        inRecords: (res.data.inRecords || []).map(el => ({ ...el, price: Math.round(el.inboundQty * el.unitPrice) }))
      }
    })
  }
  getList()

  const onDetailClick = record => {
    state.currentRow = record
    state.isShowDetail = true
  }
</script>

<style lang="scss" scoped>
  .list-content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: calc(100vh - 960rpx);
  }

  .container {
    max-height: calc(100vh - 44px);
  }
  :deep(.u-cell) {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
</style>
