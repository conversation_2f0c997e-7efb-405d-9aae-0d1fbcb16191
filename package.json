{"name": "uni-preset-vue", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3081220230817001", "@dcloudio/uni-app-plus": "3.0.0-3081220230817001", "@dcloudio/uni-components": "3.0.0-3081220230817001", "@dcloudio/uni-h5": "3.0.0-3081220230817001", "@dcloudio/uni-mp-alipay": "3.0.0-3081220230817001", "@dcloudio/uni-mp-baidu": "3.0.0-3081220230817001", "@dcloudio/uni-mp-jd": "3.0.0-3081220230817001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3081220230817001", "@dcloudio/uni-mp-lark": "3.0.0-3081220230817001", "@dcloudio/uni-mp-qq": "3.0.0-3081220230817001", "@dcloudio/uni-mp-toutiao": "3.0.0-3081220230817001", "@dcloudio/uni-mp-weixin": "3.0.0-3081220230817001", "@dcloudio/uni-quickapp-webview": "3.0.0-3081220230817001", "@turf/turf": "^6.5.0", "axios": "^1.11.0", "dayjs": "^1.11.7", "echarts": "^5.4.3", "gcoord": "^1.0.5", "lodash": "^4.17.21", "mapbox-gl": "^2.15.0", "md5": "^2.3.0", "pinia": "2.0.33", "query-string": "^8.1.0", "qweather-icons": "^1.6.0", "uni-axios-adapter": "^0.0.4", "uni-mini-router": "^0.1.5", "uni-parse-pages": "^0.0.1", "uniapp-axios-adapter": "^0.3.2", "vk-uview-ui": "^1.5.2", "vue": "3.2.45", "z-paging": "^2.6.3"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-3081220230817001", "@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/uni-stacktracey": "3.0.0-3081220230817001", "@dcloudio/vite-plugin-uni": "3.0.0-3081220230817001", "@esbuild/darwin-arm64": "^0.19.9", "@esbuild/darwin-x64": "0.16.17", "@uni-helper/vite-plugin-uni-tailwind": "^0.13.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/runtime-core": "^3.3.11", "@vue/tsconfig": "^0.1.3", "pinia-plugin-unistorage": "^0.0.17", "postcss-preset-env": "^9.3.0", "sass": "^1.59.3", "sass-loader": "^10.5.0", "tailwindcss": "^3.3.6", "typescript": "^4.9.4", "vite": "4.1.4", "vite-plugin-require-transform": "^1.0.18", "vue-tsc": "^1.8.25"}}