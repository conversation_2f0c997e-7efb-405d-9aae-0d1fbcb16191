<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="任务详情" :background="{ background: 'transparent' }"></NavBar>

    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="parent" style="position: relative">
      <l-resize @resize="handleResize">
        <view class="task-card" style="padding-top: 3rpx">
          <view class="header">
            <text>任务编号: {{ detail.taskCode }}</text>
            <view :class="`tag tag${detail.taskStatus}`">
              {{ tabOptions.find(ele => ele.value == detail.taskStatus)?.name }}
            </view>
          </view>

          <view class="card-content">
            <view class="title">
              <image class="task-icon" :src="detail.isTemp ? tempIcon : planIcon" />
              {{ detail.taskName }}
            </view>

            <view class="time-line">
              <view class="time">计划开始时间: {{ detail.planStartTime || '-' }}</view>
              <view class="time">计划结束时间: {{ detail.planEndTime || '-' }}</view>
            </view>

            <view class="bottom">
              <view class="flex flex-1">
                <text class="label">{{ store.patrolType == 1 ? '巡检线路' : '巡检范围' }}:</text>
                {{ detail.lineName || '-' }}
              </view>
              <view class="flex flex-1">
                <text class="label">巡检人:</text>
                {{ detail.patrolUserName || '-' }}
              </view>
            </view>
          </view>

          <view :class="['other-info', state.isExpand ? 'expand-other-info' : '']">
            <view class="indicator">
              <view class="flex flex-1">
                <text class="label">巡检班次:</text>
                {{ detail.shiftName || '-' }}
              </view>
              <view class="flex flex-1">
                <text class="label">巡检班组:</text>
                {{ detail.groupName || '-' }}
              </view>
              <view class="flex flex-1">
                <text class="label">任务耗时:</text>
                {{ detail.patrolDuration || '-' }}
              </view>
              <view class="flex flex-1">
                <text class="label">轨迹长度:</text>
                {{ store.patrolType == 2 ? detail.patrolMileage?.toFixed(2) || '-' : '-' }}
              </view>
            </view>
            <view class="time-line">
              <view class="time">实际开始时间: {{ detail.taskStartTime || '-' }}</view>
              <view class="time">实际结束时间: {{ detail.taskEndTime || '-' }}</view>
            </view>
          </view>

          <view class="action" @click="state.isExpand = !state.isExpand">{{ state.isExpand ? '收起' : '更多' }}</view>
        </view>
      </l-resize>
    </view>

    <view class="gap" style="height: 16rpx"></view>

    <FloatPanel
      v-if="anchors.length"
      ref="floatingPanelRef"
      id="float-panel"
      :contentDraggable="false"
      v-model:height="height"
      :anchors="anchors"
      :defaultAnchor="0"
      @height-change="onFloatHeightChange"
    >
      <u-section class="px-[25rpx]" title="巡检对象" font-size="34" line-color="#3772FF" :right="false">
        <template #right>
          <image
            v-if="store.patrolType == 2"
            :src="state.isMap ? listIcon : mapIcon"
            class="right-icon"
            @click="state.isMap = !state.isMap"
          />
        </template>
      </u-section>

      <view class="list-info">
        <view class="item">
          <text class="label">总数</text>
          <text class="value">{{ detail.objectTotalCount }}</text>
        </view>
        <text class="gap-line">|</text>
        <view class="item">
          <text class="label">完成</text>
          <text class="value text-[#165DFF]">{{ detail.checkedCount }}</text>
        </view>
        <text class="gap-line">|</text>
        <view class="item">
          <text class="label">异常</text>
          <text class="value text-[#F76560]">{{ detail.abnormalCount }}</text>
        </view>
        <text class="gap-line">|</text>
        <view class="item">
          <text class="label">漏检</text>
          <text class="value text-[#FF9A2E]">{{ detail.incompleteCount }}</text>
        </view>
        <text class="gap-line">|</text>
        <view class="item">
          <text class="label">未检</text>
          <text class="value text-[#62686E]">{{ detail.objectTotalCount - detail.checkedCount }}</text>
        </view>
      </view>

      <view
        class="float-container"
        :style="{ height: `${state.contentHeight}px`, maxHeight: `${state.contentHeight}px` }"
      >
        <scroll-view v-if="!state.isMap && state.list?.length" class="container" scroll-y show-scrollbar>
          <view class="project-item" v-for="(item, index) in state.list" :key="index">
            <view class="project-title">
              <text>{{ item.objectName }}</text>
              <text>
                <text class="text-[#165DFF]">{{ item.children?.filter(el => el.isChecked).length }}</text>
                <text>/{{ item.children?.length }}</text>
              </text>
            </view>
            <view
              class="object-item"
              v-for="(el, idx) in item.children || []"
              :key="idx"
              @click="onObjectItemClick(el, item)"
            >
              <view class="object-name">
                <text class="name">{{ el.objectName }}</text>
                <view>
                  <image v-if="el.isAbnormal == 1" class="object-status-icon" :src="errorIcon" />
                  <image v-if="el.isIncomplete == 1" class="object-status-icon" :src="lackIcon" />
                  <image
                    v-if="el.isChecked == 1 && el.isIncomplete == 0 && el.isAbnormal == 0"
                    class="object-status-icon"
                    :src="finishIcon"
                  />
                </view>
              </view>
              <view class="object-code">{{ el.objectCode }}</view>
            </view>
          </view>
        </scroll-view>

        <view v-show="state.isMap" style="width: 100%; height: 100%">
          <MapPanel
            v-if="store.patrolType == 2"
            :taskId="detail.taskId"
            :taskStatus="detail.taskStatus"
            :contentHeight="state.contentHeight"
            :isMap="state.isMap"
          />
        </view>
      </view>
    </FloatPanel>

    <view
      class="bottom-button"
      v-if="
        (detail.taskStatus == 1 && dayjs().valueOf() < dayjs(detail.planEndTime).valueOf()) || detail.taskStatus == 2
      "
    >
      <u-button type="primary" size="default" v-if="detail.taskStatus == 1" @click="state.showConfirmStart = true">
        开始巡检
      </u-button>
      <u-button
        type="primary"
        size="default"
        v-if="detail.taskStatus == 2"
        @click.stop="() => (state.showConfirmEnd = true)"
      >
        结束巡检
      </u-button>
    </view>

    <ConfirmPopup
      v-if="detail.taskStatus == 1"
      :show="state.showConfirmStart"
      @update:show="state.showConfirmStart = val"
      @onConfirm="onConfirm('start')"
      popupTitle="开始巡检"
      title="确认是否开始巡检"
      description="确认后将领取巡检任务并开始任务计时"
    />

    <ConfirmPopup
      v-if="detail.taskStatus == 2 && !detail.abnormalCount"
      :show="state.showConfirmEnd"
      @update:show="state.showConfirmEnd = val"
      @onConfirm="onConfirm('end')"
      popupTitle="结束巡检"
      title="确认是否结束巡检"
      description="确认后将结束任务计时并生成巡检轨迹"
      type="warning"
    />
    <ConfirmPopupFinish
      v-if="detail.taskStatus == 2 && !!detail.abnormalCount"
      :show="state.showConfirmEnd"
      @update:show="state.showConfirmEnd = val"
      @onConfirm="onConfirm('end', true)"
      popupTitle="结束巡检"
      title="确认是否结束巡检"
      description="确认后将结束任务计时并生成巡检轨迹"
      type="primary"
    />
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, nextTick, watch } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import dayjs from 'dayjs'
  import { useUserStore } from '@/store/modules/user'
  import { usePatrolStore } from '@/store/modules/patrol.ts'
  import { onLoad } from '@dcloudio/uni-app'
  import { tabOptions, taskTypeOptions } from '../patrol-list/configs.ts'
  import { getTaskProfile, getMyTaskObjectList, patrolStart, patrolEnd, getTaskAbnormal } from './services.ts'
  import FloatPanel from '@/components/lime-floating-panel/components/l-floating-panel/l-floating-panel.vue'
  import LResize from '@/components/lime-resize/components/l-resize/l-resize.vue'
  import * as _ from 'lodash'
  import MapPanel from './components/map.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import ConfirmPopupFinish from './components/ConfirmPopupFinish/index.vue'

  import tempIcon from '@/static/icons/icon-temp.svg'
  import planIcon from '@/static/icons/icon-plan.svg'
  import mapIcon from '@/static/icons/icon-map.svg'
  import listIcon from '@/static/icons/icon-list.svg'
  import finishIcon from '@/static/icons/icon-finish.svg'
  import lackIcon from '@/static/icons/icon-lack.svg'
  import errorIcon from '@/static/icons/icon-error.svg'

  const userStore = useUserStore()
  const store = usePatrolStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const floatingPanelRef = ref(null)

  const props = defineProps({
    taskId: { require: true },
  })

  const detail = ref({})
  const anchors = ref([])
  const height = ref(0)

  const state = reactive({
    isExpand: false,
    isMap: false,
    list: [],
    contentHeight: 0,
    showConfirmStart: false,
    showConfirmEnd: false,
  })

  onLoad(() => {})

  watch(
    () => router.route.value,
    newVal => {
      if (newVal.path === '/pages/patrol/patrol-detail/index') {
        init()
      }
    },
  )

  const init = () => {
    getTaskProfile({ taskId: props.taskId }).then(res => {
      detail.value = res.data
    })

    getMyTaskObjectList({ taskId: props.taskId }).then(res => {
      state.list = res.data || []
    })
  }

  const handleResize = _.throttle(() => {
    nextTick(() => {
      const view = uni.createSelectorQuery().select('.gap')
      const { windowHeight } = uni.getSystemInfoSync()
      view
        .boundingClientRect(data => {
          anchors.value = [windowHeight - data.top - 10, windowHeight - 80]
          height.value = windowHeight - data.top - 10

          state.contentHeight = [1, 2].includes(detail.value.taskStatus)
            ? windowHeight - data.top - 10 - 140
            : windowHeight - data.top - 10 - 70

          nextTick(() => {
            floatingPanelRef.value?.toAnchor(0)
          })
        })
        .exec()
    })
  }, 10)
  const onFloatHeightChange = ({ height }) => {
    state.contentHeight = [1, 2].includes(detail.value.taskStatus) ? height - 140 : height - 70
  }

  const onObjectItemClick = (el, item) => {
    router.push({
      path: '/pages/patrol/patrol-content/index',
      query: { taskName: detail.value.taskName, taskId: detail.value.taskId, objectId: el.objectId },
    })
  }

  // 巡检
  const onConfirm = (type, isReport) => {
    if (type === 'start') {
      patrolStart({ taskId: detail.value.taskId }).then(res => {
        state.showConfirmStart = false
        init()

        store.setPatrolStart(detail.value.taskId, true, res => {})
      })
    }

    if (type === 'end') {
      store.setPatrolStart(detail.value.taskId, false, resp => {
        state.showConfirmEnd = false

        patrolEnd({ taskId: detail.value.taskId }).then(res => {
          store.finishPatrolInterval()

          uNotifyRef.value.show({
            type: 'success',
            title: '已结束巡检',
            duration: 800,
          })
          setTimeout(() => {
            if (isReport) {
              // 跳转到应急抢修新增
              router.push({
                path: '/pages/emergency-repair/add/index',
                query: {
                  taskId: detail.value.taskId,
                },
              })
              return
            }
            router.back(-1)
          }, 850)
        })
      })
    }
  }
</script>

<style lang="scss" scoped>
  @import './index.scss';
</style>
