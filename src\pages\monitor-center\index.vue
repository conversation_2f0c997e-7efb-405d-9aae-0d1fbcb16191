<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="实时监测" :background="{ background: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <u-tabs
      class="ml-[16rpx] relative bottom-[-10rpx]"
      :list="state.siteOptions"
      bg-color="transparent"
      active-color="#165DFF"
      v-model="state.currentTab"
      bar-width="120"
      @change="onTabChange"
    ></u-tabs>
  </view>

  <scroll-view class="container" :scroll-y="true" :show-scrollbar="true">
    <template v-if="state.list?.length">
      <view class="com-card item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
        <image :src="el.icon" class="icon" />
        <view class="right">
          <view class="name">{{ el.objectCategoryName }}</view>
          <view class="remark">{{ el.remark || '-' }}</view>
        </view>
      </view>
    </template>
    <u-empty v-else margin-top="120" text="暂无数据" mode="data"></u-empty>
  </scroll-view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'

  import { siteIcons } from './config'
  import { getTreeByCode } from './services.ts'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const state = reactive({
    siteOptions: [],
    currentTab: 0,
    list: [],
  })

  getTreeByCode({ objectCategoryCode: 'MS' }).then(res => {
    state.siteOptions = res.data[0].children.map(el => ({
      name: el.objectCategoryName,
      value: el.objectCategoryCode,
    }))

    onTabChange(0)
  })

  const onTabChange = index => {
    getTreeByCode({ objectCategoryCode: state.siteOptions[index].value }).then(res => {
      state.list = (res.data?.[0]?.children || []).map(el => ({
        ...el,
        icon: siteIcons[el.objectCategoryCode],
      }))
      console.log('state.list', state.list)
    })
  }

  const onItemClick = el => {
    router.push({
      path: '/pages/monitor-center/monitor-list/index',
      query: {
        siteCategoryId: el.objectCategoryId,
        title: el.objectCategoryName,
        objectCategoryCode: el.objectCategoryCode,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    max-height: calc(100vh - 44px - 140rpx);
    overflow: auto;
  }

  .item {
    display: flex;
    align-items: center;
    .icon {
      width: 96rpx;
      height: 96rpx;
      border-radius: 24rpx;
      margin-right: 32rpx;
    }
    .right {
      .name {
        color: #414b5b;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 5rpx;
      }
      .remark {
        font-size: 28rpx;
        color: #4e5969;
      }
    }
  }

  :deep(.u-tab-item) {
    padding: 0 16rpx !important;
  }
</style>
