<template>
  <scroll-view class="container" scroll-y show-scrollbar>
    <u-section
      title="基本信息"
      class="mt-[30rpx] ml-[32rpx]"
      font-size="34"
      line-color="#3772FF"
      :right="false"
    ></u-section>
    <view class="com-card">
      <u-cell-group :border="false">
        <u-cell-item
          title="所属仓库"
          :arrow="false"
          :value="props.baseInfo.warehouseName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="入库来源"
          :arrow="false"
          :value="inboundTypeOptions.find(el => el.value === props.baseInfo.inboundType)?.label || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="入库日期"
          :arrow="false"
          :value="props.baseInfo.inboundTime || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="验收人"
          :arrow="false"
          :value="props.baseInfo.stockmanName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="采购人"
          :arrow="false"
          :value="props.baseInfo.purchaserName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="供应商"
          :arrow="false"
          :value="props.baseInfo.supplier || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="发票号"
          :arrow="false"
          :value="props.baseInfo.invoiceNo || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="入库摘要"
          :arrow="false"
          :value="props.baseInfo.remark || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>
    </view>

    <view class="list-content">
      <u-section
        title="库存信息"
        class="mt-[30rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>

      <Table :tableData="props.goodsList" :column="tableColumns">
        <template v-if="props.goodsList?.length" #totalRow>
          <u-tr>
            <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
              <text v-if="ele.title === '序号'">合计</text>
              <text v-else-if="ele.dataIndex === 'inboundQty'">
                {{ _.sum(props.goodsList.map(el => el.inboundQty)) }}
              </text>
              <text v-else-if="ele.dataIndex === 'price'">{{ _.sum(props.goodsList.map(el => el.price)) }}</text>
              <text v-else>　</text>
            </u-td>
          </u-tr>
        </template>

        <template #action="{ record }">
          <view class="text-[#165DFF]" @click.stop="onDetailClick(record)">查看</view>
        </template>
      </Table>
    </view>
  </scroll-view>

  <view class="bottom-box px-[32rpx] py-[16rpx]">
    <view></view>
    <view class="flex">
      <u-button
        :hair-line="false"
        style="
          height: 80rpx;
          width: 200rpx;
          padding: 0;
          margin-right: 16rpx;
          border-color: transparent;
          background-color: #f2f3f5;
        "
        @click="onPrevStep"
      >
        上一步
      </u-button>
      <u-button style="height: 80rpx; width: 200rpx; padding: 0; margin-right: 0" type="primary" @click="onSubmit">
        提交
      </u-button>
    </view>
  </view>

  <ConfirmPopup
    :show="state.showConfirmSubmit"
    @update:show="state.showConfirmSubmit = val"
    @onConfirm="onConfirm"
    popupTitle="提交入库"
    title="确认提交入库"
    description="确认后,将不能修改填写内容"
  />

  <u-popup
    v-model="state.isShowDetail"
    mode="bottom"
    class="confirm-popup"
    closeable
    @close="state.isShowDetail = false"
    border-radius="32"
  >
    <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">物料详情</view>
    <u-line color="#F2F3F5" />

    <view class="px-[40rpx] py-[32rpx]">
      <u-cell-group :border="false">
        <u-cell-item
          title="备件编码"
          :arrow="false"
          :value="state.currentRow.goodsCode || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件名称"
          :arrow="false"
          :value="state.currentRow.goodsName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件规格"
          :arrow="false"
          :value="state.currentRow.goodsSpec || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件型号"
          :arrow="false"
          :value="state.currentRow.goodsModel || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="供应商"
          :arrow="false"
          :value="state.currentRow.supplier || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>

      <view class="grid grid-cols-2 gap-[20rpx] mt-[40rpx]">
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">最低库存</text>
          <text class="text-[15px]">{{ state.currentRow.minStock || '-' }}</text>
        </view>
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">最高库存</text>
          <text class="text-[15px]">{{ state.currentRow.maxStock || '-' }}</text>
        </view>
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">单价</text>
          <text class="text-[15px]">
            {{ state.currentRow.unitPrice }}
          </text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs, computed } from 'vue'
  import * as _ from 'lodash'
  import { useRouter } from 'uni-mini-router'
  import Table from '@/components/MyTable/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import { inAdd } from '../../services.ts'
  import { inboundTypeOptions } from '../config.ts'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '备件编码', dataIndex: 'goodsCode', width: 140 },
    { title: '备件名称', dataIndex: 'goodsName', width: 200 },
    { title: '入库数量', dataIndex: 'inboundQty', width: 140 },
    { title: '金额(元)', dataIndex: 'price', width: 140 },
    { title: '备件详情', slotName: 'action', width: 140 }
  ]

  const router = useRouter()
  const props = defineProps(['baseInfo', 'goodsList'])
  const emits = defineEmits(['update:currentStep'])

  const state = reactive({
    showConfirmSubmit: false,
    isShowDetail: false,
    currentRow: {}
  })

  const onDetailClick = record => {
    state.currentRow = record
    state.isShowDetail = true
  }

  const onPrevStep = () => {
    emits('update:currentStep', 1)
  }
  const onSubmit = () => {
    state.showConfirmSubmit = true
  }

  const onConfirm = () => {
    inAdd({ ...props.baseInfo, inRecords: props.goodsList })
      .then(res => {
        state.showConfirmSubmit = false
        router.push({
          path: '/pages/middle-page/success/index',
          query: {
            title: '入库完成',
            desc: `新增入库成功，入库单号为:${res.data}`,
            backText: '后返回入库管理列表',
            btnText: '立即返回',
            url: 2
          }
        })
      })
      .catch(err => {
        state.showConfirmSubmit = false
        router.push({
          path: '/pages/middle-page/failure/index',
          query: {
            title: '提交失败',
            desc: '入库失败,失败原因为:',
            errText: err,
            btnText: '重新新增入库',
            url: 1,
            otherJumpText: '回到入库管理列表,',
            otherUrl: 2
          }
        })
      })
  }
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 32rpx;
    max-height: calc(100vh - 44px - 360rpx);
  }

  .list-content {
    background-color: #fff;
    padding-top: 10rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: 410rpx;
  }
  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
