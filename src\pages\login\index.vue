<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue'
  import Md5 from 'md5'
  import { login, getMobileCaptchaLogin, captchaLogin } from './services'
  import { useUserStore } from '@/store/modules/user'
  import { useRouter } from 'uni-mini-router'
  import uuid4 from '@/utils/guid'

  const userStore = useUserStore()
  const router = useRouter()

  const rememberJson = uni.getStorageSync('remember')

  const remember = rememberJson ? JSON.parse(rememberJson) : null

  const state = reactive({
    loginType: 1,
    pwdForm: {
      username: remember?.username || '',
      password: remember?.password || '',
    },

    codeForm: {
      captcha: '',
      mobile: '',
    },

    checked1: remember?.checked || ['isAutoLogin', 'isRemember'],
    checked2: remember?.checked || ['isAutoLogin'],

    checkAutoLogin: (remember?.checked || ['isAutoLogin', 'isRemember']).includes('isAutoLogin'),
    checkRemember: (remember?.checked || ['isAutoLogin', 'isRemember']).includes('isRemember'),

    seconds: 60,
    isShowTime: false,
    timer: null,

    formRules: {
      username: {
        type: 'string',
        required: false,
        message: '请输入账号',
        trigger: ['blur'],
      },
      password: {
        type: 'string',
        required: false,
        message: '请输入密码',
        trigger: ['blur'], //, 'change'
      },
    },
  })

  const uNotifyRef = ref()
  const formRef = ref()
  const codeFormRef = ref()

  // #ifdef APP-PLUS
  const version = plus.runtime.version
  // #endif

  onMounted(() => {
    userStore.setStatusBarHeight()
  })

  function changeLoginType(num: number) {
    state.loginType = num
  }
  function onSubmit() {
    if (state.pwdForm.username?.trim() == '') {
      uNotifyRef.value.show({ type: 'warning', title: '请输入账号' })
      return
    }
    if (state.pwdForm.password?.trim() == '') {
      uNotifyRef.value.show({ type: 'warning', title: '请输入密码' })
      return
    }

    userStore.removeToken()
    const params: any = {
      username: state.pwdForm.username,
      password: Md5(state.pwdForm.password),
      appId: 'baseline-app',
      fingerprint: uuid4(),
    }

    login(params).then((res: any) => {
      userStore.setUserInfo(res.data.token, res.data.user)
      userStore.setLoginOrg({ loginOrgId: res?.data?.loginOrgId, loginOrgName: res?.data?.loginOrgName })

      uni.setStorageSync(
        'remember',
        JSON.stringify({
          username: state.checkRemember ? state.pwdForm.username : '',
          password: state.checkRemember ? state.pwdForm.password : '',
          checked: state.checked1,
        }),
      )

      uNotifyRef.value.show({
        type: 'success',
        title: '登录成功',
        duration: 800,
      })
      setTimeout(() => {
        router.replaceAll('/pages/home/<USER>')
      }, 800)
    })
  }
  //验证码
  function getCode() {
    if (!state.codeForm.mobile) {
      uNotifyRef.value.show({ type: 'warning', title: '手机号不能为空' })
      return
    }
    let reg = /^1[0-9]{10}$/
    if (!reg.test(state.codeForm.mobile)) {
      uNotifyRef.value.show({ type: 'warning', title: '手机号格式错误' })
      return
    }
    uni.showLoading({ title: '' })
    setTimeout(() => {
      state.isShowTime = true
    }, 500)
    state.timer = setInterval(() => {
      if (state.seconds > 0 && state.seconds <= 60) {
        state.seconds--
      } else {
        state.isShowTime = false
        clearInterval(state.timer)
        state.timer = null
      }
    }, 1000)
    getMobileCaptchaLogin(state.codeForm.mobile).then(res => {
      uni.hideLoading()
      uni.showToast({
        title: '验证码已发送',
        duration: 2000,
      })
    })
  }
  function onCodeSubmit() {
    if (state.codeForm.mobile?.trim() == '') {
      uNotifyRef.value.show({ type: 'warning', title: '请输入手机号' })
      return
    }
    if (state.codeForm.captcha?.trim() == '') {
      uNotifyRef.value.show({ type: 'warning', title: '请输入验证码' })
      return
    }

    userStore.removeToken()
    const params: any = {
      mobile: state.codeForm.mobile,
      captcha: state.codeForm.captcha,
      appId: 'baseline-app',
      fingerprint: uuid4(),
    }

    uni.setStorageSync(
      'remember',
      JSON.stringify({
        checked: state.checked2,
      }),
    )

    captchaLogin(params).then((res: any) => {
      userStore.setUserInfo(res.data.token, res.data.user)
      uNotifyRef.value.show({
        type: 'success',
        title: '登录成功',
        duration: 800,
      })
      setTimeout(() => {
        router.replaceAll('/pages/home/<USER>')
      }, 800)
    })
  }
</script>

<template>
  <view class="login-page">
    <view class="com-header header"></view>
    <view class="container">
      <view class="login-content">
        <view class="login-tabs">
          <text class="text" :class="{ active: state.loginType === 1 }" @click="changeLoginType(1)">密码登录</text>
          <text class="text" :class="{ active: state.loginType === 2 }" @click="changeLoginType(2)">验证码登录</text>
        </view>
      </view>

      <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

      <u-form labelPosition="left" :model="state.pwdForm" ref="formRef" v-if="state.loginType === 1">
        <u-form-item prop="username" :border-bottom="false">
          <u-input v-model="state.pwdForm.username" clearable :border="false" placeholder="请输入账号"></u-input>
        </u-form-item>

        <u-form-item prop="password" :border-bottom="false">
          <u-input
            v-model="state.pwdForm.password"
            :border="false"
            clearable
            type="password"
            password-icon
            placeholder="密码"
          ></u-input>
        </u-form-item>

        <u-checkbox-group v-modal="state.checked1" class="checkbox" active-color="#2979ff">
          <u-checkbox v-model="state.checkAutoLogin" name="isAutoLogin" class="mr-[10rpx]">自动登录</u-checkbox>
          <u-checkbox v-model="state.checkRemember" name="isRemember">记住密码</u-checkbox>
        </u-checkbox-group>

        <u-button
          class="submit-btn mt-[70rpx]"
          type="primary"
          :disabled="!(state.pwdForm.username && state.pwdForm.password)"
          size="default"
          @click="onSubmit"
        >
          登录
        </u-button>
      </u-form>

      <u-form labelPosition="left" :model="state.codeForm" ref="codeFormRef" v-if="state.loginType === 2">
        <u-form-item prop="mobile" :border-bottom="false">
          <u-input
            v-model="state.codeForm.mobile"
            clearable
            :border="false"
            type="tel"
            placeholder="请输入手机号"
          ></u-input>
        </u-form-item>

        <u-form-item prop="captcha" :border-bottom="false">
          <u-input
            class="code-input"
            v-model="state.codeForm.captcha"
            clearable
            :border="false"
            placeholder="请输入验证码"
          ></u-input>

          <template #right>
            <text class="code-text time" v-if="state.isShowTime">倒计时{{ state.seconds }}s</text>
            <text class="code-text" v-else @click="getCode">获取验证码</text>
          </template>
        </u-form-item>

        <u-checkbox-group v-modal="state.checked2" class="checkbox" active-color="#2979ff">
          <u-checkbox v-model="state.checkAutoLogin" name="isAutoLogin">自动登录</u-checkbox>
        </u-checkbox-group>

        <u-button
          class="submit-btn mt-[86rpx]"
          type="primary"
          :disabled="!(state.codeForm.mobile && state.codeForm.captcha)"
          size="default"
          @click="onCodeSubmit"
        >
          登录
        </u-button>
      </u-form>
    </view>

    <view class="text-xs text-gray-400 fixed bottom-12 left-1/2 -translate-x-1/2">当前版本 {{ version }}</view>
  </view>
</template>

<style scoped lang="scss">
  @import './index.scss';
</style>
