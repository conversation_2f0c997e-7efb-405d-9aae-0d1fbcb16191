<template>
  <u-subsection
    active-color="#165DFF"
    :list="timeRangesOptions"
    v-model="state.subVal"
    style="width: 440rpx; margin: 0 auto"
    @change="onSubChange"
  ></u-subsection>

  <u-picker
    v-model="state.showPicker"
    :default-time="state.defaultTime"
    mode="time"
    :params="state.timeParams"
    @confirm="onTimeConfirm"
  ></u-picker>

  <view class="time-bar">
    <view class="time-item" @click="onOpenTimePicker('start')">
      {{ state.startTime }}
      <SvgIcon name="icon-time" class="ml-[12rpx]" />
    </view>
    <view class="text-[14px] text-[#86909C] mx-[8rpx]">至</view>
    <view class="time-item" @click="onOpenTimePicker('end')">
      {{ state.endTime }}
      <SvgIcon name="icon-time" class="ml-[12rpx]" />
    </view>
  </view>

  <u-section title="水位(m)" class="mt-[30rpx]" font-size="34" line-color="#3772FF" :right="false"></u-section>
  <LineChart :key="state.subVal + Math.random()" :dataSource="waterData" :yAxisConfig="yAxisConfig" />

  <u-section
    :title="`${timeRangesOptions[state.subVal].name}水位记录`"
    class="mt-[30rpx] mb-[20rpx]"
    font-size="34"
    line-color="#3772FF"
    :right="false"
  ></u-section>
  <Table :tableData="state.list" :column="tableColumn" />
</template>

<script setup lang="ts">
  import { timeRangesOptions } from '../../../config'
  import { reactive, ref, watch, nextTick } from 'vue'
  import dayjs from 'dayjs'
  import LineChart from '@/components/LineChart/index.vue'
  import Table from '@/components/MyTable/index.vue'
  import { rainWaterList } from '../../../services'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber'

  const tableColumn = [
    { title: '序号', width: 66 },
    { title: '时间', dataIndex: 'dateTime' },
    { title: '水位m', dataIndex: 'waterLevel', width: 120 }
  ]

  interface stateType {
    subVal: number
    showPicker: boolean
    pickType: string
    defaultTime: string
    timeParams: any
    startTime: string
    endTime: string
    loading: boolean
    list: any[]
  }

  interface Props {
    siteId: string
    siteTerminalData: any[]
    indexCode: string
  }
  const props = withDefaults(defineProps<Props>(), {
    siteId: undefined,
    siteTerminalData: undefined,
    indexCode: undefined
  })

  const state: stateType = reactive({
    subVal: 0,
    showPicker: false,
    pickType: '',
    timeParams: { year: true, month: true, day: true, hour: true, minute: false, second: false },
    defaultTime: '',
    startTime: dayjs().format('YYYY-MM-DD 08:00'),
    endTime: dayjs().format('YYYY-MM-DD HH:00'),
    loading: false,
    list: []
  })

  const waterData: any = ref(null)
  const yAxisConfig: any = ref(null)

  watch(
    () => props.siteTerminalData,
    () => {
      nextTick(() => {
        getList()
      })
    },
    { immediate: true }
  )

  function resetTime() {
    const eightTime = dayjs(`${dayjs().format('YYYY-MM-DD')} 08:00`).valueOf()
    if (dayjs().valueOf() > eightTime) {
      state.startTime = dayjs().format('YYYY-MM-DD 08:00')
    } else {
      state.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:00')
    }
  }
  resetTime()

  const onSubChange = (index: number) => {
    if (index == 2) {
      state.timeParams = { ...state.timeParams, hour: false }
      state.startTime = dayjs().subtract(1, 'months').format('YYYY-MM-DD')
      state.endTime = dayjs().format('YYYY-MM-DD')
    } else {
      state.timeParams = { ...state.timeParams, hour: true }
      state.startTime = dayjs().format('YYYY-MM-DD 08:00')
      state.endTime = dayjs().format('YYYY-MM-DD HH:00')
    }
    nextTick(() => {
      getList()
    })
  }

  const onOpenTimePicker = (type: string) => {
    state.pickType = type
    if (type === 'start') {
      state.defaultTime = state.startTime
      state.showPicker = true
    }
    if (type === 'end') {
      state.defaultTime = state.endTime
      state.showPicker = true
    }
  }
  const onTimeConfirm = (p: any) => {
    if (state.pickType == 'start') {
      state.startTime = `${p.year}-${p.month}-${p.day} ${p.hour}:00`
    }
    if (state.pickType == 'end') {
      state.endTime = `${p.year}-${p.month}-${p.day} ${p.hour}:00`
    }
    getList()
  }

  const getList = () => {
    state.loading = true

    const params = {
      siteId: props.siteId,
      startTime: dayjs(state.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(state.endTime).format('YYYY-MM-DD HH:mm:ss'),
      indexCodes: [props.indexCode],
      type: timeRangesOptions[state.subVal].value,
      terminalId: props.siteTerminalData.find(el => el.indexCode == props.indexCode)?.terminals?.[0].terminalId
    }
    // 逐日
    if (state.subVal == 2) {
      params.startTime = dayjs(state.startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss')
      params.endTime = dayjs(state.endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')
    }
    rainWaterList(params).then((res: any) => {
      state.list = (res.data || []).map((el: any) => {
        return {
          ...el,
          waterLevel: getFixedNum(el.waterLevel, 2)
        }
      })

      state.loading = false

      // 检查是否所有的 waterLevel 都为 null
      const allValuesNull = state.list.every(item => item.waterLevel === null)

      // 如果所有值都为 null，设置自定义 y 轴配置
      if (allValuesNull) {
        yAxisConfig.value = {
          max: 3,
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#dddddd'
            }
          }
        }
      } else {
        yAxisConfig.value = null
      }

      waterData.value = [
        {
          name: '水位',
          data: state.list.map(el => [el.dateTime, el.waterLevel])
        }
      ]
    })
  }
</script>

<style lang="scss" scoped>
  .time-bar {
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .time-item {
      padding: 18rpx 28rpx;
      border-radius: 60rpx;
      border: 2rpx solid #f2f3f5;

      font-size: 24rpx;
      color: #4e5969;
      display: flex;
      align-items: center;
    }
  }
</style>
