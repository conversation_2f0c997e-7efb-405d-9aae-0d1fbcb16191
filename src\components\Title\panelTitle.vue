<script setup lang="ts">
  interface Props {
    title: string
  }

  const props = withDefaults(defineProps<Props>(), {
    title: ''
  })
</script>

<template>
  <view class="title-content">
    <view class="left">
      <view class="vertical-line"></view>
      <text class="title">{{ props.title }}</text>

      <slot name="middle"></slot>
    </view>

    <slot name="right"></slot>
  </view>
</template>

<style lang="scss" scoped>
  .title-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 30rpx;
    margin-right: 30rpx;
    height: 60rpx;
    .left {
      display: flex;
      align-items: center;
    }

    .vertical-line {
      width: 3px;
      height: 14px;
      background: $uni-color-primary;
      border-radius: 10px;
      margin-right: 10rpx;
      margin-top: 3rpx;
    }
    .title {
      position: relative;
      font-size: 34rpx;
      color: #1d2129;
      font-family: PingFang SC-Medium;
      font-weight: 600;
    }
  }
</style>
