<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="视频中心" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="relative">
      <u-dropdown
        ref="dropdownRef"
        height="60"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin-top: 20rpx"
        @open="onDropdownOpen"
      >
        <u-dropdown-item :title="state.objectCategory.title">
          <scroll-view class="drop-content" scroll-y show-scrollbar>
            <DaTree
              ref="objectCategoryTreeRef"
              :data="state.objectCategoryTree"
              labelField="objectCategoryName"
              valueField="objectCategoryId"
              @change="handleObjectCategoryTreeChange"
            ></DaTree>
          </scroll-view>
        </u-dropdown-item>

        <u-dropdown-item :title="state.district.title">
          <scroll-view class="drop-content" scroll-y show-scrollbar>
            <DaTree
              ref="districtTreeRef"
              :data="state.districtTree"
              labelField="districtName"
              valueField="districtCode"
              defaultExpandAll
              @change="handleDistrictTreeChange"
            ></DaTree>
          </scroll-view>
        </u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <view
    :style="{
      height: `calc(100vh - 44px - 100rpx - ${userStore.statusBarHeight}px)`,
      background: '#ffffff',
      paddingTop: '30rpx',
      display: 'flex',
    }"
  >
    <scroll-view class="left" scroll-y>
      <view style="background: #ffffff">
        <view
          v-for="(el, idx) in state.objectClassify"
          :key="idx"
          class="left-item text-overflow1"
          :class="{
            active: state.activeItem.objectCategoryId === el.objectCategoryId,
            'active-pre': idx === state.activeItem.activeIdx - 1,
            'active-next': idx === state.activeItem.activeIdx + 1,
          }"
          @click="onLeftItemClick(el, idx)"
        >
          {{ el.objectCategoryName }}
        </view>
      </view>
    </scroll-view>

    <scroll-view class="right" scroll-y show-scrollbar>
      <u-loading
        mode="flower"
        :show="state.listLoading"
        size="40"
        style="position: absolute; top: 50rpx; left: 50%; transform: translate(-50%)"
      ></u-loading>
      <view v-if="!state.listLoading" style="background: #ffffff; text-align: left">
        <view v-for="(el, idx) in state.list" :key="idx" class="right-item" @click="onRightItemClick(el)">
          <view class="right-title">
            <text class="text-overflow1-webkit" style="flex: 1">
              {{ el.objectName }}
            </text>
            <view class="right-title-icon"></view>
          </view>

          <view style="font-size: 24rpx; color: #86909c; margin-top: 10rpx">
            {{ el.districtName || '-' }}
            <!-- <text style="margin: 0 4rpx">|</text>
            某某水系 -->
          </view>

          <view
            style="
              color: #4e5969;
              margin-top: 16rpx;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            视频监控在线/离线
            <text>
              <text style="color: #00b42a; font-size: 32rpx; font-weight: bold">
                {{ el.cameras.filter(el => el.onLine === 1).length }}
              </text>
              /
              <text style="color: #f53f3f; font-size: 32rpx; font-weight: bold; margin-right: 6rpx">
                {{ el.cameras.filter(el => el.onLine === 0).length }}
              </text>
            </text>
          </view>
        </view>
      </view>
      <u-empty v-if="!state.listLoading && state.list.length === 0" text="暂无数据" mode="data"></u-empty>
    </scroll-view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import { getOptions } from '@/api/common.ts'
  import DaTree from '@/components/da-tree/index.vue'

  import { getObjectCategoryTree, getDistrictTree, getCameraList } from './services'
  import dayjs from 'dayjs'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const dropdownRef = ref(null)
  const objectCategoryTreeRef = ref(null)
  const districtTreeRef = ref(null)

  const state = reactive({
    objectCategoryTree: [],
    objectCategory: { title: '水利对象', value: undefined },
    objectCategoryTreeExpands: [],
    objectClassify: [],

    districtTree: [],
    district: { title: '行政区划', value: undefined },

    activeItem: null,

    listLoading: false,
    list: [],
  })

  onMounted(() => {
    getObjectCategoryTree().then(res => {
      state.objectCategoryTree = res.data?.[0]?.children || []

      objectCategoryTreeRef.value?.setExpandedKeys(
        state.objectCategoryTree.map(el => el.objectCategoryId),
        false,
      )

      state.objectCategory = {
        title: state.objectCategoryTree[0].objectCategoryName,
        value: state.objectCategoryTree[0].objectCategoryId,
      }

      state.objectClassify = [
        { ...state.objectCategoryTree[0], objectCategoryName: '全部' },
        ...state.objectCategoryTree[0].children,
      ]

      state.activeItem = { ...state.objectCategoryTree[0], objectCategoryName: '全部', activeIdx: 0 }

      onLeftItemClick({ ...state.objectCategoryTree[0], objectCategoryName: '全部' }, 0)
    })

    getDistrictTree().then(res => {
      state.districtTree = res.data?.[0]?.children || []
    })
  })

  const onDropdownOpen = index => {
    if (index === 0) {
      nextTick(() => {
        objectCategoryTreeRef.value?.setCheckedKeys(state.objectCategory.value, true)
      })
    }
    if (index === 1) {
      nextTick(() => {
        districtTreeRef.value?.setCheckedKeys(state.district.value, true)
      })
    }
  }

  const handleObjectCategoryTreeChange = (allSelectedKeys, currentItem) => {
    state.objectCategory = { title: currentItem.label, value: currentItem.key }

    state.objectClassify = [
      { ...currentItem.originItem, objectCategoryName: '全部' },
      ...currentItem.children?.map(el => el.originItem),
    ]

    onLeftItemClick({ ...currentItem.originItem, objectCategoryName: '全部' }, 0)

    dropdownRef.value.close()
  }

  function handleDistrictTreeChange(allSelectedKeys, currentItem) {
    if (state.district.value === currentItem.key) {
      districtTreeRef.value?.setCheckedKeys(currentItem.key, false)
      state.district = { title: '行政区划', value: undefined }
    } else {
      state.district = { title: currentItem.label, value: currentItem.key }
    }

    getList()

    dropdownRef.value.close()
  }

  const onLeftItemClick = (item, idx) => {
    state.activeItem = { ...item, activeIdx: idx }

    getList()
  }

  const getList = () => {
    state.listLoading = true
    getCameraList({
      districtCode: state.district.value,
      objectType: state.activeItem.objectCategoryCode.slice(0, 2),
      treeNodeId: state.activeItem.objectCategoryId,
      treeNodeType: 'category',
    }).then(res => {
      state.list = res.data || []
      state.listLoading = false
    })
  }

  const onRightItemClick = item => {
    router.push({
      path: '/pages/video-center/item-video/index',
      query: {
        objectId: item.objectId,
        objectName: item.objectName,
        objectType: state.activeItem.objectCategoryCode.slice(0, 2),
      },
    })
  }
</script>

<style lang="scss" scoped>
  :deep(.u-dropdown__menu) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 32rpx;
    .u-dropdown__menu__item {
      flex: unset;
      margin-left: 30rpx;
      margin-right: 30rpx;
    }
  }
  .drop-content {
    background: #fff;
    max-height: calc(100vh - 300rpx);
    padding: 10rpx 28rpx;
  }

  .left {
    width: 218rpx;
    height: 100%;
    max-height: 100%;
    .left-item {
      background-color: #f2f3f5;
      font-size: 30rpx;
      line-height: 30rpx;
      padding: 28rpx 26rpx 28rpx 30rpx;
      overflow: hidden;
    }
    .active {
      color: #165dff;
      background: #ffffff;
    }
    .active-pre {
      border-radius: 0 0 28rpx 0;
    }
    .active-next {
      border-radius: 0 28rpx 0 0;
    }
  }

  .right {
    flex: 1;
    max-height: 100%;
    padding: 0 30rpx;
    position: relative;

    .right-item {
      padding: 30rpx 0 20rpx;
      border-bottom: 1px solid #e5e6eb;

      .right-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 32rpx;

        .right-title-icon {
          background: url('@/static/icons/arrow-right.svg') no-repeat center / 100% 100%;
          width: 18rpx;
          height: 18rpx;
          margin-left: 16rpx;
        }
      }
    }
  }
</style>
