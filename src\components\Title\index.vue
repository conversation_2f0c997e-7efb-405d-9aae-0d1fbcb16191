<script setup lang="ts">
  // import SvgIcon from '@/components/SvgIcon/index.vue'

  interface Props {
    title: string
    hasLine?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '',
    hasLine: true
  })
</script>

<template>
  <view class="title-content" :style="{ borderBottom: props.hasLine ? '2rpx solid #e9f0fd' : 'none' }">
    <view class="title-icon"></view>
    <text class="title">{{ props.title }}</text>
  </view>
</template>

<style lang="scss" scoped>
  .title-content {
    display: flex;
    align-items: center;
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    line-height: 50rpx;
    margin: 30rpx 12rpx 5rpx;
    height: 60rpx;
    // border-bottom: 2rpx solid #e9f0fd;

    .title-icon {
      position: relative;
      left: -5rpx;
      width: 20rpx;
      height: 30rpx;
      background: url('@/static/icons/icon-title.svg') no-repeat;
      background-size: 100% 100%;
    }
    .title {
      position: relative;
      /* left: -5rpx; */
      font-size: 34rpx;
    }
  }
</style>
