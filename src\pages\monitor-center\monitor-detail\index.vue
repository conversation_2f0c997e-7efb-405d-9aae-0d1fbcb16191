<template>
  <view class="page-nav-top-common">
    <NavBar :title="props.title" :background="{ backgroundColor: '#EDEFF1' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <u-tabs
      bg-color="transparent"
      active-color="#165DFF"
      bar-width="60"
      :list="state.siteTerminalData"
      v-model="state.tabVal"
      @change="onTabChange"
    ></u-tabs>
    <view class="container">
      <All
        v-if="state.siteTerminalData[state.tabVal]?.indexCode == 'all'"
        :siteId="props.siteId"
        :siteTerminalData="state.siteTerminalData"
      />
      <Water
        v-if="state.siteTerminalData[state.tabVal]?.indexCode == 'waterLevel'"
        :siteId="props.siteId"
        :indexCode="state.siteTerminalData[state.tabVal]?.indexCode"
        :siteTerminalData="state.siteTerminalData"
      />
      <Rain
        v-if="state.siteTerminalData[state.tabVal]?.indexCode == 'rainfall'"
        :siteId="props.siteId"
        :indexCode="state.siteTerminalData[state.tabVal]?.indexCode"
        :siteTerminalData="state.siteTerminalData"
      />
      <Flow
        v-if="state.siteTerminalData[state.tabVal]?.indexCode == 'flow'"
        :siteId="props.siteId"
        :indexCode="state.siteTerminalData[state.tabVal]?.indexCode"
        :siteTerminalData="state.siteTerminalData"
      />
      <Indicator
        v-if="state.indicatorOption && !isOneOfRequiredCodes(state.siteTerminalData?.[state.tabVal]?.indexCode)"
        :siteId="props.siteId"
        :timeRangesOptions="state.timeRangesOptions"
        :indexCode="state.siteTerminalData[state.tabVal]?.indexCode"
        :siteTerminalData="state.siteTerminalData"
        :indicatorOption="state.indicatorOption"
      />
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { siteTerminal } from '../services'
  // import { terminals } from '../config'
  import { chartColors, timeRangesOptions, timeRangesOptions2 } from '../config'
  import All from './modules/all/index.vue'
  import Water from './modules/water/index.vue'
  import Rain from './modules/rain/index.vue'
  import Flow from './modules/flow/index.vue'
  import Indicator from './modules/indicator/index.vue'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const attrs = useAttrs()
  const props = defineProps(['title', 'objectCategoryCode', 'siteId'])
  const state = reactive({
    timeRangesOptions: [],
    indexCodeOptions: [],
    tabVal: 0,
    siteTerminalData: [],
    indicatorOption: {},
    terminals: undefined,
  })

  getOptions('monitoringIndex').then(res => {
    state.indexCodeOptions = (res.data || []).map(el => ({ label: el.value, value: el.key, unit: el.option1 }))
    state.terminals = (res.data || []).map(el => ({
      label: el.value,
      value: el.key,
      unit: el.option1,
      option1: el.option1,
    }))
    nextTick(() => {
      setTimeout(() => {
        siteTerminal({ siteId: props.siteId }).then(res => {
          let arr = res.data
            .filter(el => state.terminals.some(ele => ele.value == el.indexCode))
            .map(el => {
              let obj = state.terminals.find(ele => ele.value == el.indexCode)
              return { ...el, name: obj.label }
            })

          // if (arr?.length > 1) {
          //   arr.unshift({
          //     name: '水雨情',
          //     indexCode: 'all',
          //   })
          // }
          if (hasAtLeastOnePair(arr)) {
            arr.unshift({
              name: '水雨情',
              indexCode: 'all',
            })
          }

          state.siteTerminalData = arr
          // console.log('get siteTerminalData', state.siteTerminalData, state.terminals)
        })

        state.indicatorOption = mergedObj(state.terminals)

        state.timeRangesOptions = props.title == '安全监测' ? timeRangesOptions2 : timeRangesOptions
      }, 300)
    })
  })

  const hasAtLeastOnePair = list => {
    const requiredPairs = [
      ['waterLevel', 'rainfall'],
      ['rainfall', 'flow'],
    ]
    const hasPair = requiredPairs.some(pair => {
      return pair.every(code => list.some(item => item.indexCode === code))
    })
    return hasPair
  }
  //判断是否是 雨量和水位和流量 之外的指标，不是的话，调用综合监测接口
  const isOneOfRequiredCodes = siteTerminalVal => {
    const requiredCodes = ['all', 'waterLevel', 'rainfall', 'flow']
    return requiredCodes.includes(siteTerminalVal)
  }
  function mergedObj(list) {
    const obj = {}
    // 遍历List数组
    list?.forEach((item, index) => {
      // 使用模运算符来循环获取颜色（如果chartColors长度小于List长度）
      const colorIndex = index % chartColors.length
      const color = chartColors[colorIndex]
      // 从item中提取所需的值
      const { label, value, option1 } = item
      // const label = value // 假设label和value相同，或者你可以根据需要修改
      const unit = option1 ? `(${option1})` : '' // 如果option1存在，则作为单位，否则为空字符串

      // 将数据添加到obj中
      obj[value] = {
        label,
        value, // 假设你希望value是key（根据你的数据结构可能有所不同）
        unit,
        unit2: option1,
        color,
      }
    })
    return obj
  }
  const onTabChange = index => {}
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 32rpx;
    padding: 32rpx;
    border-radius: 32rpx 32rpx 0 0;
    background-color: #fff;
    width: 100vw;
  }
</style>
