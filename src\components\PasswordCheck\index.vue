<template>
  <view class="input_span">
    <text class="text" ref="weak" :style="{ background: type === 'level1' ? 'red' : '#c9cdd4' }">弱</text>
    <text class="text" ref="mezzo" :style="{ background: type === 'level2' ? 'orange' : '#c9cdd4' }">中</text>
    <text class="text" ref="strong" :style="{ background: type === 'level3' ? '#00D1B2' : '#c9cdd4' }">
      强
    </text>
  </view>
</template>

<script setup>
  import { watch, ref } from 'vue'
  import { getOptions } from '@/api/common'

  const props = defineProps({
    newPassword: { require: true },
  })

  const type = ref('')
  const strengthRegex = ref([])

  getOptions('passwordStrengthRegpex').then(res => {
    strengthRegex.value = (res.data || []).reverse().map(el => ({ ...el, regex: new RegExp(el.value) }))
  })

  function checkStrong(sValue) {
    let obj = strengthRegex.value.find(el => el.regex.test(sValue))
    switch (obj?.key) {
      case 'L1':
        return 'level1'
      case 'L2':
        return 'level2'
      case 'L3':
        return 'level3'
    }
  }

  watch(
    () => props.newPassword,
    newVal => {
      const msgText = checkStrong(newVal)
      type.value = msgText
    },
  )
</script>

<style lang="scss" scoped>
  .input_span {
    .text {
      display: inline-block;
      margin-right: 8rpx;
      text-align: center;
      color: #ffffff;
      font-size: 22rpx;

      width: 64rpx;
      height: 32rpx;
      line-height: 32rpx;
      background: #c9cdd4;
      border-radius: 8rpx;
    }
  }
</style>
