<script setup>
  import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
  import checkUpdate from '@/components/uni-upgrade-center-app/utils/check-update.js'
  // import callCheckVersion from '@/uni_modules/uni-upgrade-center-app/utils/call-check-version.js'

  onLaunch(() => {
    console.log('App Launch')

    checkUpdate().then(res => {
      onBackRun()
    })
  })
  onShow(() => {
    console.log('App Show')
  })
  onHide(() => {
    console.log('App Hide')
  })

  // 后台运行插件
  const onBackRun = () => {
    const keepAlive = uni.requireNativePlugin && uni.requireNativePlugin('Ba-KeepAliveSuit')

    const onKeep = () => {
      //通用保活
      keepAlive.onKeep(
        {
          //channelId: "Ba-KeepAlive",
          //channelName: "Ba-KeepAlive",
          title: '后台常驻',
          content: '',
          isRogue: true,
        },
        res => {
          console.log(res)
          if (res.ok) {
          } else {
            uni.showToast({
              title: '开启保活失败',
              icon: 'none',
              duration: 3000,
            })
          }
        },
      )
    }

    const onAutoStart = () => {
      //去设置自启动、后台运行
      keepAlive.onAutoStart(res => {
        console.log(res)
        // uni.showToast({
        //   title: res.msg,
        //   icon: 'none',
        //   duration: 3000
        // })
      })
    }
    // keepAlive && onAutoStart()
    keepAlive && onKeep()

    const requestIgnoreBattery = () => {
      //申请加入电池优化白名单
      keepAlive.requestIgnoreBattery(res => {
        console.log(res)
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 3000,
        })
      })
    }
    const goIgnoreBattery = () => {
      //跳转到电池优化设置页
      keepAlive.goIgnoreBattery(res => {
        console.log(res)
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 3000,
        })
      })
    }
    const isIgnoringBattery = () => {
      //是否加入电池优化白名单
      keepAlive.isIgnoringBattery(res => {
        console.log(res)
        // if (res.data) {
        //  this.msgList.unshift(JSON.stringify(res.data))
        //  this.msgList.unshift(dateUtil.now())
        // }
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 3000,
        })
      })
    }
    const onShowNotify = () => {
      //常驻通知保活
      keepAlive.onShowNotify(
        {
          //channelId: "Ba-KeepAlive",
          //channelName: "Ba-KeepAlive",
          //ID:99
          //title: "测试",
          //content: "常驻通知描述",
        },
        res => {
          console.log(res)
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        },
      )
    }
    const onCancelNotify = () => {
      //取消常驻通知保活
      keepAlive.onCancelNotify(
        {
          //channelId: "Ba-KeepAlive",
          //channelName: "Ba-KeepAlive",
          //ID:99
          //title: "测试",
          //content: "常驻通知描述",
        },
        res => {
          console.log(res)
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        },
      )
    }
  }
</script>

<style lang="scss">
  /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
  @import 'vk-uview-ui/index.scss';
</style>
