<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="入库详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <u-cell-group :border="false">
          <u-cell-item
            title="调拨单号"
            :arrow="false"
            :value="state.detail.allotNo || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="调出仓库"
            :arrow="false"
            :value="state.detail.outboundWarehouseName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="调入仓库"
            :arrow="false"
            :value="state.detail.inboundWarehouseName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>

          <u-cell-item
            title="调拨时间"
            :arrow="false"
            :value="state.detail.allotTime || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="调拨人"
            :arrow="false"
            :value="state.detail.assignerName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="调拨摘要"
            :arrow="false"
            :value="state.detail.remark || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
        </u-cell-group>
      </view>

      <view class="list-content">
        <u-section title="库存信息" class="mt-[30rpx]" font-size="34" line-color="#3772FF" :right="false"></u-section>

        <Table :tableData="state.detail.allotRecords" :column="tableColumns">
          <template v-if="state.detail.allotRecords.length" #totalRow>
            <u-tr>
              <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
                <text v-if="ele.title === '序号'">合计</text>
                <text v-else-if="ele.dataIndex === 'allotQty'">
                  {{ _.sum(state.detail.allotRecords.map(el => el.allotQty)) }}
                </text>
                <text v-else-if="ele.dataIndex === 'restStockQty'">
                  {{ _.sum(state.detail.allotRecords.map(el => el.restStockQty)) }}
                </text>

                <text v-else>　</text>
              </u-td>
            </u-tr>
          </template>

          <template #action="{ record }">
            <view class="text-[#165DFF]" @click.stop="onDetailClick(record)">查看</view>
          </template>
        </Table>
      </view>
    </scroll-view>
  </view>

  <u-popup
    v-model="state.isShowDetail"
    mode="bottom"
    class="confirm-popup"
    closeable
    @close="state.isShowDetail = false"
    border-radius="32"
  >
    <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">物料详情</view>
    <u-line color="#F2F3F5" />

    <view class="px-[40rpx] py-[32rpx]">
      <u-cell-group :border="false">
        <u-cell-item
          title="备件编码"
          :arrow="false"
          :value="state.currentRow.goodsCode || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件名称"
          :arrow="false"
          :value="state.currentRow.goodsName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件规格"
          :arrow="false"
          :value="state.currentRow.goodsSpec || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件型号"
          :arrow="false"
          :value="state.currentRow.goodsModel || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="供应商"
          :arrow="false"
          :value="state.currentRow.supplier || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>

      <view class="grid grid-cols-2 gap-[20rpx] mt-[40rpx]">
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">调拨数量</text>
          <text class="text-[15px]">{{ state.currentRow.allotQty || '-' }}</text>
        </view>
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">单价(元)</text>
          <text class="text-[15px]">{{ state.currentRow.unitPrice || '-' }}</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { getAllotDetail } from '../../services'
  import Table from '@/components/MyTable/index.vue'
  import * as _ from 'lodash'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '备件编码', dataIndex: 'goodsCode', width: 140 },
    { title: '备件名称', dataIndex: 'goodsName', width: 200 },
    { title: '调拨数量', dataIndex: 'allotQty', width: 120 },
    { title: '调拨后库存', dataIndex: 'restStockQty', width: 140 },
    { title: '备件详情', slotName: 'action', width: 140 }
  ]

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)

  const props = defineProps(['allotId'])

  const state = reactive({
    detail: { allotRecords: [] },
    isShowDetail: false,
    currentRow: {}
  })

  const getList = (pageNo, pageSize) => {
    getAllotDetail({
      allotId: props.allotId
    }).then(res => {
      state.detail = {
        ...res.data,
        allotRecords: (res.data.allotRecords || []).map(el => ({ ...el, restStockQty: el.stockQty - el.allotQty }))
      }
    })
  }
  getList()

  const onDetailClick = record => {
    state.currentRow = record
    state.isShowDetail = true
  }
</script>

<style lang="scss" scoped>
  .list-content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: calc(100vh - 800rpx);
  }

  .container {
    max-height: calc(100vh - 44px);
  }

  :deep(.u-cell) {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
</style>
