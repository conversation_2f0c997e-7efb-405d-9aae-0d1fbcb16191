import { request } from '@/utils/request'

// 仓库设置-列表分页查询
export function getConfigPage(data: any) {
  return request({
    url: '/wms/config/page',
    method: 'post',
    data
  })
}

// 库存查询-列表分页查询
export function getStockPage(data: any) {
  return request({
    url: '/wms/stock/page',
    method: 'post',
    data
  })
}

// 库存盘点-列表分页查询
export function getCheckPage(data: any) {
  return request({
    url: '/wms/check/page',
    method: 'post',
    data
  })
}

// 库存盘点-盘点人列表
export function getCheckManList(data: any) {
  return request({
    url: '/wms/check/getCheckManList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 库存盘点-详情
export function checkDetail(data: any) {
  return request({
    url: '/wms/check/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 库存盘点-新增
export function addCheck(data: any) {
  return request({
    url: '/wms/check/add',
    method: 'post',
    data
  })
}

// 入库管理-入库人列表
export function getInManList(data: any) {
  return request({
    url: '/wms/in/getManList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 入库管理-列表分页查询
export function inPage(data: any) {
  return request({
    url: '/wms/in/page',
    method: 'post',
    data
  })
}

// 入库管理-详情
export function inGetDetail(data: any) {
  return request({
    url: '/wms/in/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 入库管理-新增
export function inAdd(data: any) {
  return request({
    url: '/wms/in/add',
    method: 'post',
    data
  })
}

// 物料设置-供应商列表
export function getSupplierList(data: any) {
  return request({
    url: '/wms/goods/getSupplierList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 出库管理-出库人列表
export function getOutManList(data: any) {
  return request({
    url: '/wms/out/getManList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 出库管理-列表分页查询
export function outPage(data: any) {
  return request({
    url: '/wms/out/page',
    method: 'post',
    data
  })
}

// 出库管理-详情
export function outGetDetail(data: any) {
  return request({
    url: '/wms/out/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 出库管理-新增
export function outAdd(data: any) {
  return request({
    url: '/wms/out/add',
    method: 'post',
    data
  })
}

// 调拨管理-列表分页查询-移动端
export function allotAppPage(data: any) {
  return request({
    url: '/wms/allot/appPage',
    method: 'post',
    data
  })
}

// 调拨管理-列表分页查询
export function allotPage(data: any) {
  return request({
    url: '/wms/allot/page',
    method: 'post',
    data
  })
}

// 调拨管理-调拨人列表
export function getAllotManList(data: any) {
  return request({
    url: '/wms/allot/getManList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 调拨管理-详情
export function getAllotDetail(data: any) {
  return request({
    url: '/wms/allot/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 调拨管理-新增
export function allotAdd(data: any) {
  return request({
    url: '/wms/allot/add',
    method: 'post',
    data
  })
}
