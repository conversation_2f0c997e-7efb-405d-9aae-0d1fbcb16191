import { request } from '@/utils/request'

// 水利对象分类-获取树
export function getObjectCategoryTree(data: { taskId: string }) {
  return request({
    url: '/base/objectCategory/getTree',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 行政区划-获取树
export function getDistrictTree(data: any) {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 手机端视频
export function getCameraList(data: any) {
  return request({
    url: '/base/camera/getCameraList',
    method: 'post',
    data,
  })
}

// 视频监控-根据水利对象查询视频点信息
export function getObjectCameraList(data: any) {
  return request({
    url: '/base/camera/object/getCameraList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 获取视频播放地址
export function getPlayOnUrl(params: any) {
  return request({
    url: '/external/easyCvr/getPlayOnUrl',
    method: 'get',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
