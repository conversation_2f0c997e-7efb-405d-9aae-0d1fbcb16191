<template>
  <view class="have-bg-page">
    <view class="details-bg"></view>
    <NavBar title="详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <image
          class="status-img"
          v-if="state.maintenanceDetails?.status === 1"
          src="~@/static/images/maintenance-wait.png"
        ></image>
        <image
          class="status-img"
          v-else-if="state.maintenanceDetails?.status === 2"
          src="~@/static/images/maintenance-in-progress.png"
        ></image>
        <image
          class="status-img"
          v-else-if="state.maintenanceDetails?.status === 3"
          src="~@/static/images/repair-finish.png"
        ></image>
        <view class="row first">
          <text class="label">维养单号</text>
          <text class="value">{{ state.maintenanceDetails?.serialNumber }}</text>
        </view>
        <view class="row">
          <text class="label">维养单位</text>
          <text class="value">{{ state.maintenanceDetails?.deptName }}</text>
        </view>
        <view class="separation-line">
          <text class="circle"></text>
          <text class="line"></text>
          <text class="circle"></text>
        </view>
        <view class="row">
          <text class="label">维养部位</text>
          <text class="value">{{ props?.objectNames }}</text>
        </view>

        <view class="row">
          <text class="label">维养时间</text>
          <text class="value">{{ state.maintenanceDetails?.maintenanceDate || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">维养状态</text>
          <text class="value">
            {{ state.statusOptions.find(el => el.value == state.maintenanceDetails?.status)?.label || '--' }}
          </text>
        </view>
        <view class="row">
          <text class="label">维养人员</text>
          <text class="value">{{ props?.userNames || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">维养内容</text>
          <text class="value">{{ state.maintenanceDetails?.content || '--' }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { getMaintenanceDetails } from '../services'
  import { getOptions } from '@/api/common'
  import * as _ from 'lodash'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const props = defineProps(['maintenanceId', 'userNames', 'objectNames'])

  const state = reactive({
    maintenanceDetails: {},
    statusOptions: [],
  })

  onMounted(() => {
    getDetails()
    getOptions('maintenanceStatus').then(res => {
      state.statusOptions = (res?.data || []).map(el => ({ label: el.value, value: el.key }))
    })
  })

  const getDetails = () => {
    getMaintenanceDetails({
      id: props.maintenanceId,
    }).then(res => {
      state.maintenanceDetails = res?.data
    })
  }
</script>

<style lang="scss" scoped>
  .have-bg-page {
    margin: -34rpx;
    background: #cdefff url('@/static/images/details-nav-bar-bg.png') no-repeat;
    background-size: 100%;
    height: calc(100vh - 40rpx);
    padding: 40rpx;
    position: relative;
    .details-bg {
      position: absolute;
      top: 230rpx;
      left: 36rpx;
      width: 90%;
      height: 83.9%;
      border-radius: 16rpx;
      transform: rotate(-3deg);
      background: rgba(255, 255, 255, 0.3);
    }

    .separation-line {
      height: 30rpx;
      display: flex;
      margin: 0 -36rpx;
      margin-bottom: 30rpx;
      .circle {
        width: 30rpx;
        height: 30rpx;
        border-radius: 30rpx;
        background: #cdefff;
      }
      .line {
        flex: 1;
        margin: 16rpx 10rpx;
        border-top: 1px dashed #e5e6eb;
      }
    }
  }
  .com-card {
    position: relative;
    padding-top: 16rpx;
    .status-img {
      position: absolute;
      right: 16rpx;
      width: 160rpx;
      height: 160rpx;
    }
    .first {
      padding-top: 30rpx;
    }
  }
  .row {
    margin-bottom: 26rpx;
    display: flex;
    .label {
      font-weight: 400;
      font-size: 28rpx;
      color: #4e5969;
      width: 80px;
    }
    .value {
      font-weight: 400;
      font-size: 30rpx;
      color: #1d2129;
      flex: 1;
    }
  }
</style>
