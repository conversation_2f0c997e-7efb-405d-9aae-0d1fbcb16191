<template>
  <view class="page-nav-top-common-bg1">
    <NavBar title="运行指令" :background="{ backgroundColor: 'transparent' }">
      <template #right>
        <text
          v-if="hasAddPermission"
          class="list-add-btn"
          style="font-size: 28rpx;border-radius: 10rpx;background-color: #007AFF;color: #fff;padding: 10rpx 20rpx;"
          @click="add"
        >新增+</text>
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    <view class="list-search">
      <u-input
        class="search-input"
        v-model="state.keyword"
        placeholder="请输入工作票编号/工程名称"
        @input="handleSearch"
        @confirm="handleSearch"
      ></u-input>
      <text class="search-icon" @click="handleSearch"></text>
      <text class="search-close" @click="handleClose"></text>
    </view>

    <ZPaging
      ref="pagingRef"
      :style="{ marginTop: `calc(44px + ${userStore.statusBarHeight}px + 120rpx)`, paddingTop: '28rpx', zIndex: 10 }"
      v-model="state.list"
      @query="getList"
    >
      <view class="com-card" @click="handleRunDetails(el)" v-for="(el, idx) in state.list" :key="idx">
        <view class="card-header" style="margin-bottom: 24rpx">
          <view class="card-title" style="margin-bottom: 0">{{ el.cmdName }}</view>
          <view class="status-tag">{{ getStatusText(el.statusCode) }}</view>
        </view>
        <view class="grey-card">
          <view class="card-row">
            <text class="label">工作票编号</text>
            <text class="value">{{ el.cmdCode }}</text>
          </view>
          <view class="card-row">
            <text class="label">调度方案</text>
            <text class="value">{{ el.dispatchCode }}</text>
          </view>
          <view class="card-row">
            <text class="label">工作负责人</text>
            <text class="value">{{ el.wardUserName }}</text>
          </view>
          <!-- <view class="card-row">
            <text class="label">工作人员</text>
            <text class="value">{{ el.workUserName }}</text>
          </view> -->
          <view class="card-row">
            <text class="label">工作开始时间</text>
            <text class="value">{{ el.planStartDate }}</text>
          </view>
          <view class="card-row">
            <text class="label">工作结束时间</text>
            <text class="value">{{ el.planEndDate }}</text>
          </view>
        </view>
        <view class="card-btn-group">
          <u-button class="card-default-btn" @click="handleDelete(el)">删除</u-button>
          <u-button class="card-default-btn" @click="handleCopy(el)">复制</u-button>
          <u-button v-if="el.statusCode === 0 || el.statusCode === 2" class="card-default-btn" @click="handleEdit(el)">修改</u-button>
          <u-button v-if="el.statusCode === 0" class="card-default-btn" @click="handleApprove(el)">审批</u-button>
          <u-button v-if="el.statusCode === 1" class="card-default-btn" @click="handleReceive(el)">接收</u-button>
          <u-button class="card-active-btn" @click="handleOperationTicket(el)">操作票</u-button>
        </view>
      </view>
    </ZPaging>
    <ConfirmPopup
      :show="state.showConfirmDel"
      @update:show="
        () => {
          state.showConfirmDel = val
        }
      "
      @onConfirm="onConfirmDelete"  
      popupTitle="确认删除"
      title="确认是否删除该条数据"
      description=""
      type="warning"
    />
  </view>
</template>
<script setup>
  import { reactive, ref, onMounted, nextTick, computed } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import * as _ from 'lodash'
  import { getRunInstruction, deleteRunInstruction } from './services'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'

  const router = useRouter()
  const userStore = useUserStore()

  const pagingRef = ref(null)
  const uNotifyRef = ref(null)

  // 检查用户是否有新增权限
  const hasAddPermission = computed(() => {
    const roleIds = userStore.user.roleIds || []
    return roleIds.includes(10000) || roleIds.includes(10039)
  })

  const state = reactive({
    keyword: '',
    list: [],
    showConfirmDel: false,
    runCmdId: undefined,
  })

  const handleSearch = _.debounce(val => {
    nextTick(() => {
      getList(1)
    })
  }, 500)
  const handleClose = () => {
    state.keyword = ''
    getList(1)
  }

  onMounted(() => {
    getList(1)
  })
  const getList = async (pageNo, pageSize) => {
    const res = await getRunInstruction({
      keyword: state.keyword,
      cmdCode: '',
      cmdName: '',
      dispatchId: undefined,
      pageNum: pageNo || 1,
      pageSize: 10,
    })
    // 添加状态字段，默认为0（待审批）
    state.list = (res.data?.data || []).map(item => ({
      ...item,
      status: item.status !== undefined ? item.status : 1
    }))
    pagingRef.value.complete(state.list)
    if (pageNo && pageNo == 1) {
      pagingRef.value.scrollToTop()
    }
  }

  // 获取状态文字
  const getStatusText = (status) => {
    const statusMap = {
      0: '审批中',
      1: '下发中',
      2: '已驳回',
      3: '已完成',
    }
    return statusMap[status] || '待审批'
  }

  //新增
  const add = () => {
    router.push({
      path: '/pages/run-manage/run-instruction/run-add/index',
    })
  }
  const handleEdit = row => {
    router.push({
      path: '/pages/run-manage/run-instruction/run-add/index',
      query: { runCmdId: row.runCmdId },
    })
  }
  const handleCopy = row => {
    router.push({
      path: '/pages/run-manage/run-instruction/run-add/index',
      query: { runCmdId: row.runCmdId, source: 'copy' },
    })
  }
  const handleOperationTicket = row => {
    router.push({
      path: '/pages/run-manage/run-instruction/operation-ticket/index',
      query: { cmdCode: row.cmdCode },
    })
  }
  const handleRunDetails = row => {
    router.push({
      path: '/pages/run-manage/run-instruction/run-details/index', 
      query: { runCmdId: row.runCmdId, cmdCode: row.cmdCode },
    })
  }

  // 审批
  const handleApprove = (row) => {
    router.push({
      path: '/pages/run-manage/run-instruction/run-add/index',
      query: { runCmdId: row.runCmdId, type: 'approve' },
    })
  }

  // 接收
  const handleReceive = (row) => {
    router.push({
      path: '/pages/run-manage/run-instruction/run-add/index',
      query: { runCmdId: row.runCmdId, type: 'receive' },
    })
  }

  //删除
  const handleDelete = row => {
    state.showConfirmDel = true
    state.runCmdId = row.runCmdId
  }
  const onConfirmDelete = () => {
    deleteRunInstruction({ runCmdIds: state.runCmdId }).then(res => {
      state.showConfirmDel = false
      uNotifyRef.value.show({
        type: 'success',
        title: '删除成功',
        duration: 800,
      })
      pagingRef.value.reload()
      handleClose()
    })
  }
</script>
<style lang="scss" scoped>
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10rpx;
  }
  
  .status-tag {
    background-color: #FFF3E8;
    color: #F77234;
    border: 1px solid #F77234;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    font-size: 28rpx;
  }
  :deep(.u-btn) {
    margin-left: 20rpx;
    padding: 0 20rpx !important;
    margin-right: 0 !important;
  }
</style>
