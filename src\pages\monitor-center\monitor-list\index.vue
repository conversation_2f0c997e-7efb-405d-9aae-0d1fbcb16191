<template>
  <view class="page-nav-top-common-bg1">
    <NavBar :title="props.title" :background="{ backgroundColor: 'transparent' }">
      <template #right>
        <image
          src="@/static/images/search.png"
          class="w-[48rpx] h-[48rpx] mr-[32rpx]"
          @click="toggleSearch"
        />
        <image
          :src="state.isMap ? listIcon : mapIcon"
          class="w-[48rpx] h-[48rpx] mr-[32rpx]"
          @click="state.isMap = !state.isMap"
        />
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
  </view>

  <view v-show="!state.isMap">
    <List
      ref="listRef"
      :indexCodeOptions="state.indexCodeOptions"
      :icon="state.icon"
      :title="props.title"
      :siteCategoryId="props.siteCategoryId"
    />
  </view>

  <view v-show="state.isMap" style="height: calc(100% - 70px - 20rpx)">
    <Map
      :isMap="state.isMap"
      :indexCodeOptions="state.indexCodeOptions"
      :title="props.title"
      :icon="state.icon"
      :siteCategoryId="props.siteCategoryId"
    />
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { getOptions } from '@/api/common.ts'
  import mapIcon from '@/static/icons/icon-map.svg'
  import listIcon from '@/static/icons/icon-list.svg'
  import Map from './components/map.vue'
  import List from './components/list.vue'
  import { siteIcons } from '../config.ts'

  const router = useRouter()
  const uNotifyRef = ref()
  const listRef = ref()

  const props = defineProps(['title', 'siteCategoryId', 'objectCategoryCode'])
  const state = reactive({
    isMap: false,
    icon: siteIcons[props.objectCategoryCode],
    indexCodeOptions: [],
    list: []
  })

  // 切换搜索框显示/隐藏
  const toggleSearch = () => {
    if (listRef.value) {
      listRef.value.state.showSearch = !listRef.value.state.showSearch
      // 如果隐藏搜索框，清空搜索内容
      if (!listRef.value.state.showSearch) {
        listRef.value.state.searchValue = ''
        listRef.value.pagingRef.reload()
        listRef.value.getList(1)
      }
    }
  }

  getOptions('monitoringIndex').then(res => {
    const arr = (res.data || []).map(el => ({ label: el.value, value: el.key, unit: el.option1 }))
    arr.unshift({ label: '全部', value: 'all' })
    state.indexCodeOptions = arr
  })
</script>

<style lang="scss" scoped></style>
