<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="库存详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <u-cell-group :border="false">
          <u-cell-item
            title="仓库编码"
            :arrow="false"
            :value="props.warehouseCode || ''"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="库存名称"
            :arrow="false"
            :value="props.warehouseName || ''"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="所属组织"
            :arrow="false"
            :value="props.organizationName || ''"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="物料种类"
            :arrow="false"
            :value="props.goodsCount || ''"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="仓管员"
            :arrow="false"
            :value="props.stockmanName || ''"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
        </u-cell-group>
      </view>

      <view class="list-content">
        <u-section title="库存信息" class="mt-[30rpx]" font-size="34" line-color="#3772FF" :right="false"></u-section>

        <Table :tableData="state.list" :column="tableColumns">
          <template v-if="state.list.length" #totalRow>
            <u-tr>
              <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
                <text v-if="ele.title === '序号'">合计</text>
                <text v-else-if="ele.dataIndex === 'stockQty'">{{ _.sum(state.list.map(el => el.stockQty)) }}</text>
                <text v-else>　</text>
              </u-td>
            </u-tr>
          </template>

          <template #action="{ record }">
            <view class="text-[#165DFF]" @click.stop="onDetailClick(record)">查看</view>
          </template>
        </Table>
      </view>
    </scroll-view>

    <u-popup
      v-model="state.isShowDetail"
      mode="bottom"
      class="confirm-popup"
      closeable
      @close="state.isShowDetail = false"
      border-radius="32"
    >
      <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">物料详情</view>
      <u-line color="#F2F3F5" />

      <view class="px-[40rpx] py-[32rpx]">
        <u-cell-group :border="false">
          <u-cell-item
            title="物料编码"
            :arrow="false"
            :value="state.currentRow.goodsCode || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="物料名称"
            :arrow="false"
            :value="state.currentRow.goodsName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="物料类型"
            :arrow="false"
            :value="state.currentRow.goodsTypeName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="供应商"
            :arrow="false"
            :value="state.currentRow.supplier || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
        </u-cell-group>

        <view class="grid grid-cols-2 gap-[20rpx] mt-[40rpx]">
          <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
            <text class="text-[#86909C]">型号</text>
            <text class="text-[15px]">{{ state.currentRow.goodsModel || '-' }}</text>
          </view>
          <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
            <text class="text-[#86909C]">规格</text>
            <text class="text-[15px]">{{ state.currentRow.goodsSpec || '-' }}</text>
          </view>
          <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
            <text class="text-[#86909C]">最低库存</text>
            <text class="text-[15px]">{{ state.currentRow.minStock || '-' }}</text>
          </view>
          <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
            <text class="text-[#86909C]">最高库存</text>
            <text class="text-[15px]">{{ state.currentRow.maxStock || '-' }}</text>
          </view>
          <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
            <text class="text-[#86909C]">单价</text>
            <text class="text-[15px] text-[#F76560]">
              {{ state.currentRow.unitPrice ? `￥${state.currentRow.unitPrice}` : '-' }}
            </text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { getStockPage } from '../../services'
  import Table from '@/components/MyTable/index.vue'
  import * as _ from 'lodash'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '物料编码', dataIndex: 'goodsCode', width: 120 },
    { title: '物料名称', dataIndex: 'goodsName', width: 200 },
    { title: '当前库存', dataIndex: 'stockQty', width: 140 },
    { title: '物料详情', slotName: 'action', width: 140 }
  ]

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)

  const props = defineProps({
    warehouseId: undefined,
    warehouseCode: undefined,
    warehouseName: undefined,
    organization: undefined,
    organizationName: undefined,
    goodsCount: undefined,
    stockmanId: undefined,
    stockmanName: undefined,
    status: undefined
  })

  const state = reactive({
    list: [],
    isShowDetail: false,
    currentRow: {}
  })

  const getList = (pageNo, pageSize) => {
    getStockPage({
      warehouseId: props.warehouseId,
      pageNum: pageNo || 1,
      pageSize: 10
    }).then(res => {
      state.list = res.data?.data || []
    })
  }
  getList()

  const onDetailClick = record => {
    state.currentRow = record
    state.isShowDetail = true
  }
</script>

<style lang="scss" scoped>
  .list-content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: calc(100vh - 700rpx);
  }

  .container {
    max-height: calc(100vh - 44px);
  }

  :deep(.u-cell) {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
</style>
