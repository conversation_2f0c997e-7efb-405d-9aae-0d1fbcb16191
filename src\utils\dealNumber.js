// n: 保留几位有效数字
export function dealNumber(number, n) {
  if (number === null) return null
  if (number === 0 || number === '0') return 0
  if (!number) return ''

  let num = +number
  if (num >= n * 10) {
    return +num.toFixed(0)
  }

  return +num.toPrecision(n)
}

export function getFixedNum(num, count) {
  if (num === null) return null
  if (num === 0 || num === '0') return 0

  if (!num) return ''

  return +num.toFixed(count)
}
