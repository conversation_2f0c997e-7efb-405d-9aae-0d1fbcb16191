<template>
  <view class="page-nav-top-common">
    <NavBar title="调拨管理" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
  </view>

  <ZPaging ref="pagingRef" style="margin-top: 80px; z-index: 10" v-model="state.list" @query="getList">
    <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
      <view class="header">
        <text class="header-name">调拨记录: {{ el.allotCount || '-' }}</text>

        <u-tag
          size="mini"
          style="font-size: 26rpx"
          border-color="transparent"
          :text="el.status == 1 ? '启用' : '停用'"
          :type="el.status == 1 ? 'success' : 'error'"
        />
      </view>

      <view class="content">
        <view class="content-title">
          <view class="title">
            {{ el.warehouseName || '-' }}
          </view>
        </view>

        <view class="indicate-item">所属组织: {{ el.organizationName || '-' }}</view>

        <view class="mt-[10rpx]">
          <view class="flex item-center justify-between">
            <text class="text-[13px] text-[#86909C]">仓管员&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ el.stockmanName || '-' }}</text>
          </view>
          <view class="flex item-center justify-between mt-[10rpx]">
            <text class="text-[13px] text-[#86909C]">仓库编码&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ el.warehouseCode || '-' }}</text>
          </view>
        </view>
      </view>
    </view>
  </ZPaging>

  <LimeFab :offset="{ x: -1, y: 400 }" heightGap="60">
    <view class="add-btn" @click="onAddClick">
      <u-icon name="plus" color="#ffffff" size="36"></u-icon>
    </view>
  </LimeFab>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { allotAppPage } from '../services'
  import LimeFab from '@/components/lime-fab/index.vue'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)

  const state = reactive({
    list: [],
  })

  onShow(() => {
    getList(1)
  })

  const getList = (pageNo, pageSize) => {
    allotAppPage({
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  const onItemClick = el => {
    router.push({ path: '/pages/warehouse/allot/record/index', query: el })
  }

  const onAddClick = () => {
    router.push({ path: '/pages/warehouse/allot/add/index' })
  }
</script>

<style lang="scss" scoped>
  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 32rpx;
      background: #e8f3ff;

      .header-name {
        font-size: 28rpx;
        color: #4e5969;
        line-height: 28rpx;
      }
    }

    .content {
      padding: 0 32rpx 32rpx;
      .content-title {
        font-size: 32rpx;
        color: #1d2129;
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        .title {
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .indicate-item {
        font-size: 28rpx;
        color: #86909c;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }

  .add-btn {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: linear-gradient(44deg, #3772ff 13%, #5ddcf5 82%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
