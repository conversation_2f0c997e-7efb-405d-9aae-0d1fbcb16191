import { request } from '@/utils/request'

// 更新用户
export function userSimpleInfoUpdate(data: any) {
  return request({
    url: '/sys/user/simpleInfo/update',
    method: 'post',
    data
  })
}

// 登录人修改密码
export function changePassword(params: any) {
  return request({
    url: '/sys/user/changePassword',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 修改密码-获取短信验证码
export function getCaptcha(params: any) {
  return request({
    url: '/sys/user/changePasswordBySms/getCaptcha',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 根据短信验证码修改密码
export function changePasswordBySms(params: any) {
  return request({
    url: '/sys/user/changePasswordBySms',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
