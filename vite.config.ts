import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import vueJsx from '@vitejs/plugin-vue-jsx'
import requireTransform from 'vite-plugin-require-transform'

import nested from 'tailwindcss/nesting'
import tailwindcss from 'tailwindcss'
// @ts-ignore
import tailwindcssConfig from './tailwind.config.ts' // 注意匹配实际文件
import postcssPresetEnv from 'postcss-preset-env'
import uniTailwind from '@uni-helper/vite-plugin-uni-tailwind'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
    vueJsx(),
    uniTailwind({
      /* options */
    }),
    requireTransform({})
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  css: {
    postcss: {
      plugins: [
        nested(),
        tailwindcss({
          config: tailwindcssConfig
        }),
        postcssPresetEnv({
          stage: 3,
          features: { 'nesting-rules': false }
        })
      ]
    }
  }
})
