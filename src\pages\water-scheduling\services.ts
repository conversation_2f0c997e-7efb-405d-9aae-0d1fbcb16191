import { request } from '@/utils/request'

// 水量台账管理-列表分页查询
export function getWaterLedgerPage(data: object) {
  return request({
    url: '/custom/water/consumption/page',
    method: 'post',
    data,
  })
}

//水量台账管理-详情
export function getWaterLedgerDetails(params: object) {
  return request({
    url: '/custom/water/consumption/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水量台账管理——水量复核
export function reviewWaterLedger(params: object) {
  return request({
    url: '/custom/water/consumption/check',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水量台账管理-开始调度
export function startUpdateWaterLedger(data: object) {
  return request({
    url: '/custom/water/consumption/startUpdate',
    method: 'post',
    data,
  })
}

// 水量台账管理-结束调度
export function endUpdateWaterLedger(data: object) {
  return request({
    url: '/custom/water/consumption/endUpdate',
    method: 'post',
    data,
  })
}
