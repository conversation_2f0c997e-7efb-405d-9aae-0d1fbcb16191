<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar :title="props.objectName" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
  </view>

  <view
    style="background: #ffffff; padding: 30rpx 30rpx 0"
    :style="{
      height: `calc(100vh - 44px - 20rpx - ${userStore.statusBarHeight}px)`,
    }"
  >
    <view v-if="!!state.activeItem" style="height: 500rpx; border-radius: 16rpx; overflow: hidden">
      <video
        style="width: 100%; height: 100%"
        :src="state.activeItem.streamUrl"
        id="my-video"
        autoplay
        is-live
        show-mute-btn
        :poster="state.activeItem.snapUrl || NoVideoImg"
        :enable-progress-gesture="false"
        :show-progress="false"
        @error="handleError"
      ></video>
    </view>

    <scroll-view
      scroll-y
      :style="{
        height: `calc(100vh - 44px - 20rpx - ${userStore.statusBarHeight}px - ${
          !!state.activeItem ? 560 : 0
        }rpx)`,
        background: '#ffffff',
        paddingTop: '30rpx',
      }"
    >
      <view class="video-list">
        <view
          v-for="(item, index) in state.list"
          :key="index"
          class="video-item"
          :style="{
            color: state.activeItem?.cameraId === item.cameraId ? '#ffffff' : '',
            background: state.activeItem?.cameraId === item.cameraId ? '#165DFF' : '',
          }"
          @click="onItemClick(item)"
        >
          <view
            :style="{
              width: '16rpx',
              height: '16rpx',
              marginRight: '10rpx',
              borderRadius: '50%',
              background: item.onLine === 1 ? '#00B42A' : '#F53F3F',
            }"
          ></view>
          <view style="flex: 1" class="text-overflow1-webkit">{{ item.cameraName || '-' }}</view>
        </view>
      </view>

      <u-empty v-if="state.list.length === 0" margin-top="100" text="暂无数据" mode="data"></u-empty>
    </scroll-view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs, getCurrentInstance, onUnmounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onHide, onLoad, onShow, onUnload } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import { getOptions } from '@/api/common.ts'
  import NoVideoImg from '@/static/images/no-video-img.png'

  import { getObjectCameraList, getPlayOnUrl } from '../services'
  import dayjs from 'dayjs'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()

  let videoContext = null

  const props = defineProps(['objectId', 'objectType', 'objectName'])
  const state = reactive({
    activeItem: null,

    list: [],

    timer: null,
  })

  onUnmounted(() => {})

  const getList = () => {
    getObjectCameraList({
      objectId: props.objectId,
      objectType: props.objectType,
    }).then(res => {
      state.list = res.data || []

      const current = state.list[0]

      keepAliveUrl(current, () => {
        state.activeItem = current
      })
      state.timer = setInterval(() => {
        keepAliveUrl(current, () => {})
      }, 59000)
    })
  }
  getList()

  const keepAliveUrl = (current, callback) => {
    getPlayOnUrl({
      channel: current.channelCode,
      device: current.deviceCode,
      protocol: 'flv',
    }).then(res => {
      callback()
    })
  }

  const onItemClick = item => {
    if (state.timer) {
      clearInterval(state.timer)
    }
    keepAliveUrl(item, () => {
      state.activeItem = item
    })
    state.timer = setInterval(() => {
      keepAliveUrl(item, () => {})
    }, 59000)
  }

  const handleError = _.debounce(error => {
    error => {
      videoContext.play()
    }
  }, 500)
</script>

<style lang="scss" scoped>
  .video-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20rpx;
    .video-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      background: #f2f3f5;
      border-radius: 16rpx;
    }
  }
</style>
