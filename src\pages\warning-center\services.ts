import { request } from '@/utils/request'

// 预警分组-列表分页查询
export function getWarnGroupPage(data: any) {
  return request({
    url: '/warn/group/page',
    method: 'post',
    data,
  })
}

// 预警事件-最新预警事件分页查询
export function getWarnEventLatestPage(data: any) {
  return request({
    url: '/warn/event/latest/page',
    method: 'post',
    data,
  })
}

// 预警对象-历史预警事件分页查询
export function getWarnEventHistoryPage(data: any) {
  return request({
    url: '/warn/event/history/page',
    method: 'post',
    data,
  })
}
