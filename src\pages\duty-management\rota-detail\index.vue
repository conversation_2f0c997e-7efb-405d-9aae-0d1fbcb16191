<template>
  <view class="h-screen bg-[#ffffff]">
    <view class="page-nav-top-common-bg1-white">
      <NavBar :title="pageTitle" :background="{ background: 'transparent' }">
        <template #right>
          <u-icon @click="onRightClick" name="more-dot-fill" size="32" class="mr-[32rpx]"></u-icon>
        </template>
      </NavBar>
      <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

      <view class="top-bar">
        <view class="top-bar-left">
          <SvgIcon name="duty-calendar-icon" class="mr-[12rpx]" />
          <text>日志日期</text>
        </view>
        <view class="top-bar-right">
          <view class="text-[#4E5969] mr-[8rpx]">{{ dayjs(props.logDate).format('YYYY年M月D日') }}</view>
        </view>
      </view>
    </view>

    <view class="container">
      <view class="d-row">
        <view class="label">班组</view>
        <view class="value text-[#4E5969]">{{ props.groupName }}</view>
      </view>
      <u-line color="#E5E6EB" margin="40rpx 0 30rpx" />
      <view class="d-row">
        <view class="label">班次</view>
        <view
          v-if="props.shiftId != null"
          class="class-item value"
          :style="{
            color: getColor(state.details?.shiftId),
            background: getColor(state.details?.shiftId)?.replace('1)', '0.1)'),
          }"
        >
          {{ state.allShiftList.find(el => el.shiftId == state.details?.shiftId)?.shiftName }}
        </view>
      </view>

      <view class="d-row" v-if="props.logType == '3'">
        <view class="label">天气</view>
        <view class="value text-[#4E5969]">
          {{ state.weatherOptions.find(el => el.key == state.details.weatherCode)?.value || '-' }}
        </view>
      </view>
      <view class="d-row" v-if="props.logType == '3'">
        <view class="label">水位</view>
        <view class="value text-[#4E5969]">
          {{ state.details.waterLevel === null ? '-' : state.details.waterLevel + 'm' }}
        </view>
      </view>
      <view class="d-row" v-if="props.logType == '3'">
        <view class="label">降雨</view>
        <view class="value text-[#4E5969]">
          {{ state.details.rainfall === null ? '-' : state.details.rainfall + 'mm' }}
        </view>
      </view>
      <view class="d-row" v-if="props.logType == '3'">
        <view class="label">库容</view>
        <view class="value text-[#4E5969]">
          {{ state.details.storageCapacity === null ? '-' : state.details.storageCapacity + '万m³' }}
        </view>
      </view>

      <u-line color="#E5E6EB" margin="40rpx 0 30rpx" />
      <u-section title="日志内容" font-size="34" line-color="#3772FF" :right="false"></u-section>
      <scroll-view scroll-y class="scroll-content">
        <view class="mt-[20rpx] text-[#4E5969]">
          {{ state.details.logContent || '-' }}
        </view>

        <u-line color="#E5E6EB" margin="40rpx 0 30rpx" />

        <!-- <view class="text-[12px] text-[#86909C] ml-[10rpx] mb-[10rpx]">日志附件图片</view> -->
        <u-section title="日志附件图片" font-size="34" line-color="#3772FF" :right="false"></u-section>
        <MyUpload
          class="mt-[20rpx]"
          :urls="state.details.urls || []"
          :onlyView="true"
          :deletable="false"
          folderName="schedule-log"
        ></MyUpload>
      </scroll-view>
    </view>

    <u-popup
      v-model="state.showMore"
      mode="bottom"
      class="confirm-popup"
      closeable
      @close="state.showMore = false"
      border-radius="32"
    >
      <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">管理日志</view>
      <u-line color="#F2F3F5" />

      <view class="px-[32rpx] py-[32rpx] flex items-center justify-around">
        <view class="text-center" @click="state.showConfirmDel = true">
          <view class="bg-[#F2F3F5] w-[96rpx] h-[96rpx] rounded-full flex items-center justify-center">
            <SvgIcon name="delete-icon" />
          </view>
          <view class="mt-[16rpx]">删除</view>
        </view>

        <view class="text-center" @click="onEditClick">
          <view class="bg-[#F2F3F5] w-[96rpx] h-[96rpx] rounded-full flex items-center justify-center">
            <SvgIcon name="edit-icon" />
          </view>
          <view class="mt-[16rpx]">编辑</view>
        </view>
      </view>
    </u-popup>

    <ConfirmPopup
      :show="state.showConfirmDel"
      @update:show="state.showConfirmDel = val"
      @onConfirm="onConfirmDelete"
      popupTitle="删除日志"
      title="确认是否删除该条日志"
      description=""
      type="warning"
    />
  </view>
</template>

<script setup>
  import { getOptions } from '@/api/common.ts'
  import { reactive, ref, onMounted, watch, computed, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { getDetails, workShiftPage, workLogDelete } from '../services'
  import { useUserStore } from '@/store/modules/user'
  import dayjs from 'dayjs'
  import MyUpload from '@/components/MyUpload/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'

  const router = useRouter()
  const userStore = useUserStore()
  const uNotifyRef = ref()
  const colors = [
    'rgba(35, 195, 67, 1)',
    'rgba(64, 128, 255, 1)',
    'rgba(141, 78, 218, 1)',
    'rgba(247, 84, 168, 1)',
    'rgba(51, 209, 201, 1)',
    'rgba(159, 219, 29, 1)',
    'rgba(255, 154, 46, 1)',
    'rgba(247, 186, 30, 1)',
  ]

  const props = defineProps(['logType', 'logDate', 'shiftId', 'groupId', 'groupName'])

  const state = reactive({
    allShiftList: [],
    details: {},

    weatherOptions: [],

    showMore: false,
    showConfirmDel: false,
  })

  const pageTitle = computed(() => {
    if (props.logType == '1') return '值班日志详情'

    if (props.logType == '2') return '调度日志详情'

    if (props.logType == '3') return '水库值班日志详情'
  })

  onMounted(() => {})

  onShow(() => {
    getDetail()
  })

  const getColor = shiftId => {
    return colors[state.allShiftList.findIndex(ele => ele.shiftId == shiftId)]
  }

  workShiftPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.allShiftList = (res.data?.data || []).reverse()
  })

  //天气
  getOptions('dutyWeather').then(res => {
    state.weatherOptions = res?.data
  })

  const getDetail = () => {
    getDetails({ logType: props.logType, logDate: props.logDate, groupId: props.groupId }).then(res => {
      state.details = { ...res.data, urls: res.data?.attachVOS?.map(el => el.attachUrl) }
    })
  }

  const onRightClick = () => {
    state.showMore = true
  }

  const onConfirmDelete = () => {
    workLogDelete({ logIds: state.details.logId }).then(res => {
      state.showConfirmDel = false
      uNotifyRef.value.show({
        type: 'success',
        title: '删除成功',
        duration: 800,
      })
      state.showMore = false

      setTimeout(() => {
        router.back(-1)
      }, 850)
    })
  }

  const onEditClick = () => {
    state.showMore = false

    router.push({
      path: '/pages/duty-management/rota-form/index',
      query: {
        logType: props.logType,
        logDate: props.logDate,
        shiftId: props.shiftId,
        groupId: props.groupId,
        logId: state.details.logId,
        groupName: props.groupName,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .top-bar {
    margin: 20rpx 32rpx 0;
    padding: 24rpx;
    border-radius: 8rpx;
    background: #e8f3ff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top-bar-left {
      display: flex;
      align-items: center;
    }
    .top-bar-right {
      display: flex;
      align-items: center;

      .class-item {
        width: 66rpx;
        text-align: center;
        display: inline-block;
        margin-left: 16rpx;
        .current-day {
          background: #165dff;
          border-radius: 50%;
          color: #ffffff;
        }
      }
    }
  }

  .container {
    margin: 0 32rpx;
    padding: 38rpx 0;
    width: calc(100% - 64rpx);
  }
  .scroll-content {
    max-height: calc(100vh - 44px - 320rpx);
  }
  .d-row {
    display: flex;
    line-height: 56rpx;
    .value {
      margin-left: auto;
    }
  }
</style>
