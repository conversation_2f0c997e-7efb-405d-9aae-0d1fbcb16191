<template>
  <view class="flex justify-center pt-[300rpx]">
    <u-loading size="80" mode="flower"></u-loading>
  </view>
</template>

<script setup>
  import { useUserStore } from '@/store/modules/user'
  import { useRouter } from 'uni-mini-router'
  import { getUserInfo, getRouters } from '@/api/common.ts'
  import { onLoad } from '@dcloudio/uni-app'
  import { onMounted } from 'vue'
  import { usePatrolStore } from '@/store/modules/patrol.ts'

  const patrolStore = usePatrolStore()
  const userStore = useUserStore()
  const router = useRouter()

  onLoad(() => {
    patrolStore.finishPatrolInterval()
    if (!!userStore.token) {
      const rememberJson = uni.getStorageSync('remember')
      const remember = rememberJson ? JSON.parse(rememberJson) : null
      if (remember?.checked?.includes('isAutoLogin')) {
        getUserInfo(userStore.user.userId).then(res => {
          router.replaceAll('/pages/home/<USER>')
        })
      } else {
        router.replaceAll('/pages/login/index')
      }
    } else {
      router.replaceAll('/pages/login/index')
    }
  })
</script>
