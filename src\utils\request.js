import axios from 'uniapp-axios-adapter'
import { useUserStore } from '@/store/modules/user'
import router from '../router'

/**
 * @description: ContentType
 */
export const ContentTypeEnum = {
  // form-data qs
  FORM_URLENCODED: 'application/x-www-form-urlencoded;charset=UTF-8',
  // form-data upload
  FORM_DATA: 'multipart/form-data;charset=UTF-8',
  // json
  JSON: 'application/json;charset=UTF-8',
}

export const errorCode = {
  /* '400': '请求错误', */
  400: '登录认证异常',
  401: '登录认证失效，请重新登录',
  403: '无权限',
  410: '帐号已停用',
  411: '帐号已锁定',
  412: '用户名错误',
  413: '密码错误',
  414: '手机号码错误',
  415: '验证码错误',
  416: '验证码已过期',
  420: 'appId错误',
  421: '无权限登录该应用',
  422: '钉钉用户未匹配',
  423: '钉钉临时授权码无效',
  424: '钉钉应用后台免登授权码无效',
  500: '业务错误,返回错误提示',
  599: '接口入参错误',
  600: '外部接口异常',
  999: '系统出现未知错误',
}

export function request(requestConifg) {
  const userStore = useUserStore()

  const axiosIns = axios.create({
    withCredentials: false,
    timeout: 10000, // 请求超时时间
    baseURL: import.meta.env.VITE_BASE_API,
  })

  // 请求拦截
  axiosIns.interceptors.request.use(
    config => {
      // 发送请求前，可在此携带 token
      if (userStore.token) {
        config.headers['token'] = userStore.token
      }

      if (config.headers.globalLoading) {
        uni.showLoading({
          title: '',
          mask: true,
        })
      }
      return config
    },
    error => {
      uni.showToast({
        title: error.message,
        icon: 'none',
        duration: 2000,
      })

      return Promise.reject(error)
    },
  )

  // 响应拦截
  axiosIns.interceptors.response.use(
    response => {
      uni.hideLoading()
      // 请求rul
      // const requestUrl = response.config.url;
      // 未设置状态码则默认成功状态
      const code = response.data.code || 200
      // 获取错误信息
      const msg = response.data.message || errorCode[code]

      if (code === 200 && response.data.success == false) {
        uni.showToast({
          title: msg,
          icon: 'none',
          duration: 2000,
        })
      } else if (code === 400) {
        uni.showToast({
          title: msg,
          icon: 'none',
          duration: 2000,
        })

        router.replaceAll('/pages/login/index')
      } else if (code === 401) {
        uni.showToast({
          title: msg,
          icon: 'none',
          duration: 2000,
        })

        userStore.removeToken()
        router.replaceAll('/pages/login/index')
      } else if (code === 404) {
        // router.push('/404')
      } else if (code !== 200) {
        uni.showToast({
          title: msg,
          icon: 'none',
          duration: 2000,
        })
      } else {
        return response.data
      }
      return Promise.reject(msg)
    },
    error => {
      uni.hideLoading()
      let { errMsg } = error
      if (errMsg === 'Network Error') {
        errMsg = '后端接口连接异常'
      } else if (errMsg.includes('timeout')) {
        errMsg = '系统接口请求超时'
      } else if (errMsg.includes('Request noneed with status code')) {
        errMsg = '系统接口' + errMsg.substr(errMsg.length - 3) + '异常'
      } else if (errMsg.includes('Request Parameter Error')) {
        errMsg = '系统接口参数异常:' + errMsg
      }

      uni.showToast({
        title: errMsg,
        icon: 'none',
        duration: 2000,
      })
      //  return Promise.reject(error) //注释该行，否则接口请求失败会一直请求刷新错误数据，这里保证请求一次
    },
  )

  axiosIns.defaults.headers.post['Content-Type'] = ContentTypeEnum.JSON
  // 请求函数
  return new Promise((resolve, reject) => {
    axiosIns
      .request(requestConifg)
      .then(response => {
        resolve(response)
      })
      .catch(error => {
        reject(error)
      })
  })
}
