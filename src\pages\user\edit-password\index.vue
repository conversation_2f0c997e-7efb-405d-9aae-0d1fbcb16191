<template>
  <view class="edit-password">
    <NavBar title="修改密码" :background="{ background: 'transparent' }">
      <template #right>
        <text @click="onRightClick" class="nav-right">{{ state.type === 'password' ? '短信验证' : '旧密码验证' }}</text>
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="page-top-nav content">
      <Password v-if="state.type === 'password'" />
      <Captcha v-if="state.type === 'captcha'" />
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import Password from './components/password.vue'
  import Captcha from './components/captcha.vue'

  const userStore = useUserStore()
  const user = userStore.user
  const router = useRouter()

  const uNotifyRef = ref()
  const state = reactive({
    type: 'password'
  })

  const onRightClick = () => {
    state.type = state.type === 'password' ? 'captcha' : 'password'
  }
</script>

<style lang="scss" scoped>
  .edit-password {
    background: url('@/static/images/details-top-bg.png') no-repeat;
    background-size: 100%;
    height: 100%;
  }
  .content {
    padding-top: 60rpx;
  }

  .nav-right {
    font-size: 28rpx;
    color: #4e5969;
    padding-right: 32rpx;
  }
</style>
