<template>
  <view v-if="loading" class="flex justify-center mt-[50rpx]">
    <u-loading size="80" mode="flower"></u-loading>
  </view>

  <view
    id="mapBox"
    ref="mapBoxRef"
    :option="center"
    :change:option="mapbox.createMap"
    :size="sizeState"
    :change:size="mapbox.dealSize"
    :prop="area"
    :change:prop="mapbox.dealLayers"
    :data="line"
    :change:data="mapbox.dealLine"
  ></view>
</template>

<script>
  import { getTrackMap } from '../services.ts'
  import * as _ from 'lodash'
  import { usePatrolStore } from '@/store/modules/patrol.ts' //巡检对象
  import { getValueByKey } from '@/api/common.ts'

  export default {
    props: ['taskId', 'taskStatus', 'contentHeight', 'isMap'],
    data() {
      return {
        center: null,

        area: null,
        areaData: null,

        line: null,
        lineData: null,

        sizeState: 0,
        loading: true,

        timer: null,
      }
    },
    watch: {
      contentHeight(newVal) {
        this.sizeState = newVal
      },
      isMap(newVal) {
        this.$nextTick(() => {
          this.sizeState = newVal
        })
      },
    },

    mounted() {
      const store = usePatrolStore()

      getValueByKey('patrolCentralPoint').then(res => {
        this.center = res.data.split(',').map(el => +el)
      })

      setTimeout(() => {
        this.getData(this.$props.taskId)

        this.timer = setInterval(() => {
          if (this.taskStatus == 2 && store.timer) {
            this.getData(this.taskId, 'interval')
          }
        }, store.frequencyTime * 1000 + 5000)
      }, 500)
    },

    onLoad() {},

    onUnload() {
      clearInterval(this.timer)
    },

    methods: {
      onMapMounted() {
        this.area = this.areaData
        this.line = this.lineData
      },
      onStyleLoad() {
        this.loading = false
      },

      getData(taskId, type) {
        // 巡检范围
        getTrackMap({ taskId }).then(res => {
          if (!this.area) {
            this.areaData = {
              lineRange: JSON.parse(res.data.lineRange),
              lineName: res.data.lineName,
            }
          }
          let arr = res.data.details
            ?.filter(el => el.longitude && el.latitude)
            .map(el => [+el.longitude, +el.latitude])
          this.lineData = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {},
                geometry: {
                  coordinates: arr,
                  type: 'LineString',
                },
              },
            ],
          }
          if (type === 'interval') {
            this.line = this.lineData
          }
        })
      },
    },
  }
</script>

<script module="mapbox" lang="renderjs">
  import mapboxgl from 'mapbox-gl'
  import * as turf from '@turf/turf'
  import { mapBoundGeo } from '@/utils/mapBounds.js'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import { mapConfig, accessToken } from '@/utils/mapConfig'

  export default {
    data() {
      return {
        mapIns: null,
        markerIns: null,
      }
    },
  	methods: {
  		createMap(center) {
  			mapboxgl.accessToken = accessToken;
  			this.mapIns = new mapboxgl.Map({...mapConfig, center: center || mapConfig.center});

        this.mapIns.on('load', () => {
          this.$ownerInstance.callMethod('onMapMounted')
        })

        this.mapIns.on('style.load', () => {
          this.$ownerInstance.callMethod('onStyleLoad')
        })

  		},
      dealSize() {
        this.mapIns.resize()
      },

      // 巡检范围图层
      dealLayers (item) {
        if(!item) return;
        if (this.mapIns.getSource('all-area')) {
          clearSourceAndLayer(this.mapIns, ['all-area'], ['all-area', 'all-point'])
        }
        let allGeoJson = { type: 'FeatureCollection', features: [] }
        allGeoJson.features.push.apply(allGeoJson.features, item.lineRange.features)
        allGeoJson.features.push({
          type: 'Feature',
          properties: { ...item, lineRange: null },
          geometry: turf.centroid(item.lineRange).geometry
        })
        this.mapIns.addSource('all-area', { type: 'geojson', data: allGeoJson })
        mapBoundGeo(allGeoJson, this.mapIns)
        this.mapIns.addLayer({
          id: 'all-area',
          type: 'fill',
          source: 'all-area',
          paint: {
            'fill-color': '#00FF14',
            'fill-opacity': 0.35
          },
          filter: ['==', '$type', 'Polygon']
        })
        this.mapIns.addLayer({
          id: 'all-point',
          type: 'symbol',
          source: 'all-area',
          layout: {
            'text-size': 12,
            'text-field': ['get', 'lineName'],
            // 'text-offset': [0, 1.25],
            'text-anchor': 'center'
          },
          filter: ['==', '$type', 'Point']
        })
      },

      dealLine(list) {
        if(!list) return;

        if (this.mapIns.getSource('realRouteLayer')) {
          clearSourceAndLayer(this.mapIns, ['realRouteLayer'], ['realRouteLayer'])
        }

        let coords = [
          list.features[0].geometry.coordinates[
            list.features[0].geometry.coordinates.length - 1
          ][0],
          list.features[0].geometry.coordinates[
            list.features[0].geometry.coordinates.length - 1
          ][1]
        ]
        if(this.markerIns) {
          this.markerIns.remove()
        }
        this.markerIns = new mapboxgl.Marker().setLngLat(coords).addTo(this.mapIns)

        this.mapIns.addLayer({
          id: 'realRouteLayer',
          type: 'line',
          source: {
            type: 'geojson',
            lineMetrics: true,
            data: list
          },
          paint: {
            'line-width': 3,
            'line-opacity': 1,
            'line-color': '#FFA318'
          }
        })
      }
  	}
  }
</script>

<style lang="scss" scoped>
  #mapBox {
    width: 100%;
    height: 100%;
  }
</style>
