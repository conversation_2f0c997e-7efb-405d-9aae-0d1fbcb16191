@import './variables.scss';
@import '@/static/fonts/MyFont.scss';
@import './mixin.scss';

page {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  color: var(--color-text);
  font-family: PingFang SC-Medium;
  box-sizing: border-box;
}

html {
  height: 100%;
}

#app {
  height: 100%;
  background: #edeff1;
}
.page-nav-top-common {
  padding-top: 20rpx;
  background: #edeff1;
}

.page-nav-top-common-bg1 {
  background: #edeff1 url('@/static/images/nav-bar-bg.png') no-repeat;
  background-size: 100%;
  padding-top: 20rpx;
}

.page-nav-top-common-bg1-white {
  background: #ffffff url('@/static/images/nav-bar-bg.png') no-repeat;
  background-size: 100%;
  padding-top: 20rpx;
}

.page-nav-top-common-bg2 {
  background: #edeff1 url('@/static/images/details-top-bg.png') no-repeat;
  background-size: 100%;
  padding-top: 20rpx;
  height: calc(100vh - 20rpx);
}

.page-top-nav {
  margin-top: 40rpx;
}

.com-card {
  padding: 28rpx;
  margin: 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// u-view 样式重写
// 最大号的primary、方形按钮背景样式
.u-btn.u-size-default.u-btn--primary {
  background: linear-gradient(44deg, #3772ff 0%, #5ddcf5 97%);
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(69, 178, 244, 0.25);
  border-radius: 16rpx;
  border: none;
  font-size: 30rpx;
  padding: 30rpx 0;
  height: unset;
  line-height: unset;
}
.u-btn.u-size-default.u-btn--primary.u-btn--primary--disabled {
  opacity: 0.6;
}

// 模块小标题
.u-section__title {
  padding-left: 0 !important;
  margin-left: -10rpx;
  margin-bottom: 12rpx;
  .u-section__title__icon-wrap {
    position: static !important;
    .u-icon__icon.u-iconfont {
      font-size: 34rpx !important;
      position: relative;
      left: -10rpx;
    }
  }
}
// select弹窗标题
.u-select__header__title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}

// NumberBox 步进器 按钮
.u-numberbox {
  .num-btn {
    top: 0 !important;
  }
}

// 地图
.mapboxgl-ctrl-bottom-left {
  display: none;
}
.mapboxgl-ctrl.mapboxgl-ctrl-attrib.mapboxgl-compact {
  display: none;
}
