<template>
  <scroll-view class="container" scroll-y>
    <u-form :model="state.form" ref="formRef" label-width="150" :error-type="['border-bottom', 'toast']">
      <u-section
        title="基本信息"
        class="mt-[32rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>
      <view class="com-card">
        <u-form-item label="仓库名称" prop="warehouseId" required>
          <u-input
            v-model="state.select.current1.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'warehouseId'
                state.select.list = props.warehouseOptions
              }
            "
            placeholder="请选择仓库"
          />
        </u-form-item>

        <u-form-item label="盘点人" prop="checkManId" required>
          <u-input
            v-model="state.select.current2.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'checkManId'
                state.select.list = props.checkManList
              }
            "
            placeholder="请选择盘点人"
          />
        </u-form-item>

        <u-form-item label="盘点日期" prop="checkTime" required>
          <u-input
            v-model="state.form.checkTime"
            type="select"
            @click="state.showTimePicker = true"
            placeholder="请选择盘点日期"
          />
        </u-form-item>
      </view>

      <u-section
        title="摘要信息"
        class="mt-[32rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>
      <view class="com-card" style="padding: 10rpx 28rpx">
        <u-form-item label="盘点摘要" prop="remark" label-position="top" :border-bottom="false">
          <u-input type="textarea" v-model="state.form.remark" />
        </u-form-item>
      </view>
    </u-form>
  </scroll-view>

  <u-select
    v-model="state.select.show"
    mode="single-column"
    :list="state.select.list"
    @cancel="state.select.show = false"
    @confirm="onSelectConfirm"
  ></u-select>

  <u-picker
    v-model="state.showTimePicker"
    :default-time="state.defaultTime"
    mode="time"
    :params="{ year: true, month: true, day: true, hour: true, minute: true, second: true }"
    @confirm="onTimeConfirm"
  ></u-picker>

  <view class="bottom-box px-[32rpx] py-[16rpx]">
    <u-button style="height: 80rpx; width: 200rpx; padding: 0; margin-right: 0" type="primary" @click="onNextStep">
      下一步
    </u-button>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import dayjs from 'dayjs'

  const props = defineProps({
    warehouseOptions: { default: [] },
    checkManList: { default: [] }
  })
  const emits = defineEmits(['update:currentStep', 'onNext'])

  const formRef = ref(null)

  const state = reactive({
    select: {
      show: false,
      field: undefined,
      current1: { label: undefined, value: undefined },
      current2: { label: undefined, value: undefined },
      list: []
    },
    showTimePicker: false,
    defaultTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    form: {
      warehouseId: undefined,
      warehouseName: undefined,
      checkManId: undefined,
      checkManName: undefined,
      checkTime: undefined,
      remark: undefined
    },
    rules: {
      warehouseId: [{ required: true, message: '请选择仓库' }],
      checkManId: [{ required: true, message: '请选择盘点人' }],
      checkTime: [{ required: true, message: '请选择盘点日期' }]
    }
  })

  onMounted(() => {
    formRef.value.setRules(state.rules)
  })

  const onSelectConfirm = item => {
    switch (state.select.field) {
      case 'warehouseId':
        state.select.current1 = item[0]
        state.form.warehouseId = item[0].value
        state.form.warehouseName = item[0].label
        return
      case 'checkManId':
        state.select.current2 = item[0]
        state.form.checkManId = item[0].value
        state.form.checkManName = item[0].label
        return
      default:
        return
    }
  }

  const onTimeConfirm = p => {
    state.form.checkTime = dayjs(`${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}:${p.second}`).format(
      'YYYY-MM-DD HH:mm:ss'
    )
  }

  const onNextStep = () => {
    formRef.value.validate(valid => {
      if (valid) {
        emits('onNext', state.form)
        emits('update:currentStep', 1)
      }
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 32rpx;
    max-height: calc(100vh - 44px - 300rpx);
  }
  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  }
</style>
