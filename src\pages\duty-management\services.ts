import { request } from '@/utils/request'

// 移动端_排班下拉框
export function getDeptGroupList(data: any) {
  return request({
    url: '/work/schedule/getDeptGroupList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 值班--列表分页
export function workSchedulePage(data: any) {
  return request({
    url: '/work/schedule/page',
    method: 'post',
    data
  })
}

// 班次——列表
export function workShiftPage(data: any) {
  return request({
    url: '/work/shift/page',
    method: 'post',
    data
  })
}

//根据部门查询班组
export function getScheduleGroupList(data: any) {
  return request({
    url: '/work/schedule/getGroupList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 值班日志——新增
export function workLogAdd(data: any) {
  return request({
    url: '/work/log/add',
    method: 'post',
    data
  })
}

// 值班日志——更新
export function workLogUpdate(data: any) {
  return request({
    url: '/work/log/update',
    method: 'post',
    data
  })
}

// 移动端_日志详情
export function getDetails(data: any) {
  return request({
    url: '/work/log/getDetails',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 值班日志——删除
export function workLogDelete(data: any) {
  return request({
    url: '/work/log/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
// 获取班次列表
export function getWorkLogShifts(params: any) {
  return request({
    url: '/work/log/getShifts',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
