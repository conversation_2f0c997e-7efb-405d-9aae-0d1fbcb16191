.login-page {
  background: #f5f5f5 url('@/static/images/login-top-bg.png') no-repeat;
  background-size: 100%;
  width: 100vw;
  height: 100vh;
  font-size: 20rpx;

  .header {
    height: 372rpx;
    padding-top: 190rpx;
    background: linear-gradient(to bottom, rgba(124, 167, 240, 0.3), rgba(55, 114, 255, 0));
    position: relative;
    pointer-events: none;

    &::before {
      position: absolute;
      content: '';
      margin: 0 8rpx 0 24rpx;
      width: calc(100% - 32rpx);
      height: 100%;
      left: 25rpx;
      top: 205rpx;
      background: url('@/static/images/login-top-text.png') no-repeat;
      background-size: 100%;
    }
  }

  .com-header {
    padding-top: 420rpx;
  }

  .container {
    height: calc(100vh - 420rpx);
    padding: 28rpx 48rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
  }

  .login-content {
    padding: 20rpx 0 0 5rpx;
    .login-tabs {
      display: flex;
      gap: 14rpx;
      align-items: center;

      .text {
        color: #4e5969;
        font-size: 30rpx;
        margin-right: 15rpx;
        border-bottom: 4rpx solid transparent;
      }
      .active {
        color: #1d2129;
        font-size: 32rpx;
        border-bottom: 4rpx solid #3772ff;
        padding-bottom: 5rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 600;
      }
    }

    .checkbox {
      display: flex;
    }

    .submit-btn {
      margin-top: 175rpx;
      width: 413rpx;
      height: 116rpx;
      text-align: center;
    }
  }
}

:deep(.u-form) {
  margin-top: 40rpx;

  .u-form-item__body {
    height: 92rpx;
    background: #f2f3f5;
    border-radius: 16rpx;
    padding: 0rpx 30rpx;

    .u-form-item--right {
      display: flex;
      align-items: center;
    }
  }

  .code-text {
    color: #3772ff;
  }
  .u-btn--primary {
    width: 100%;
    margin: 20rpx auto;
    margin-top: 80rpx;
    background-color: $uni-color-primary;
    border-color: $uni-color-primary;
    font-size: 36rpx !important;
  }
}
