import { request } from '@/utils/request'

// 我的简报--列表分页
export function reportUserPage(data: any) {
  return request({
    url: '/war/user/report/user/page',
    method: 'post',
    data
  })
}

// 简报审核---列表分页
export function reportAdminPage(data: any) {
  return request({
    url: '/war/admin/report/admin/page',
    method: 'post',
    data
  })
}

// 我的简报详情
export function reportGet(data: any) {
  return request({
    url: '/war/user/report/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 我的简报更新(发布)--发布状态草稿状态
export function userReportReleaseUpdate(data: any) {
  return request({
    url: '/war/user/report/release/update',
    method: 'post',
    data
  })
}

// 简报审核---发布
export function adminReportReleaseUpdate(data: any) {
  return request({
    url: '/war/admin/report/release/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
