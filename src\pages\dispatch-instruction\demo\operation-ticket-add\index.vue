<template>
  <view class="page-nav-top-common-bg1">
    <NavBar title="新增操作票" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view style="height: 98%" scroll-y show-scrollbar>
      <u-form :model="state.form" ref="formRef" label-width="210" :error-type="['border-bottom', 'toast']" class="form-container">
        <panelTitle title="基础信息" />
        <view class="com-card details-card-box" style="padding: 0 28rpx;">
          <u-form-item label="操作票编码" prop="operateCode" required>
            <u-input v-model="state.form.operateCode" placeholder="请输入" maxlength="128" @change="checkOperateCodeLength" />
          </u-form-item>

          <u-form-item label="工程名称" prop="projectId" required>
            <u-input
              v-model="state.select.current1.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'projectName'
                  state.select.list = state.projectOptions
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="操作人" prop="operateUserId" required>
            <u-input
              v-model="state.select.current2.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'operateUser'
                  state.select.list = state.userOptions
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="负责人" prop="guardianUserId" required>
            <u-input
              v-model="state.select.current3.label"
              type="select"
              @click="
                () => {
                  state.select.show = true
                  state.select.field = 'guardianUser'
                  state.select.list = state.userOptions
                }
              "
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item label="操作开始时间" prop="startDate" required :border-bottom="true">
            <u-input type="input" v-model="state.form.startDate" placeholder="请选择" disabled></u-input>
            <u-icon name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('start')"></u-icon>
          </u-form-item>

          <u-form-item label="操作结束时间" prop="endDate" required :border-bottom="true">
            <u-input type="input" v-model="state.form.endDate" placeholder="请选择" disabled></u-input>
            <u-icon name="clock" color="#1D2129" size="28" @click="onOpenTimePicker('end')"></u-icon>
          </u-form-item>

          <u-form-item label="备注">
            <u-input v-model="state.form.remark" placeholder="请输入" maxlength="512" @change="checkRemarkLength" />
          </u-form-item>
        </view>

        <panelTitle title="操作项" />
        <view class="com-card details-card-box" style="padding: 10rpx 28rpx;">
          <checkbox-group @change="handleOperationChange">
            <label class="checkbox-item" v-for="item in state.operationOptions" :key="item.id">
              <view class="checkbox-wrapper">
                <checkbox activeBackgroundColor="#3772FF" iconColor="#ffffff"  :value="item.id" :checked="item.checked" />
                <text class="checkbox-label">{{item.name}}</text>
              </view>
            </label>
          </checkbox-group>
        </view>
      </u-form>
    </scroll-view>

    <view class="form-btn-group">
      <u-button class="form-sure-btn receive-btn" @click="handleSubmit">提交</u-button>
    </view>

    <u-select
      v-model="state.select.show"
      mode="single-column"
      :list="state.select.list"
      @cancel="state.select.show = false"
      @confirm="onSelectConfirm"
    ></u-select>

    <u-picker
      v-model="state.showPickerTime"
      mode="time"
      :default-time="dayjs().format('YYYY-MM-DD HH:mm')"
      :params="state.timeParams"
      @cancel="state.showPickerTime = false"
      @confirm="onTimeConfirm"
    ></u-picker>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getDispatchProjectList, getDispatchUser, addOperateCmd } from '../services'
import dayjs from 'dayjs'
import { useRouter } from 'uni-mini-router'

const router = useRouter()
const formRef = ref(null)
const uNotifyRef = ref()

const props = defineProps({
  runCmd: {
    type: String,
    default: ''
  }
})

const state = reactive({
  select: {
    show: false,
    field: undefined,
    current1: { label: undefined, value: undefined },
    current2: { label: undefined, value: undefined },
    current3: { label: undefined, value: undefined },
    list: [],
  },
  showPickerTime: false,
  timeParams: {
    year: true,
    month: true,
    day: true,
    hour: true,
    minute: true,
  }, 
  projectOptions: [],
  userOptions: [],

  // 操作项测试数据
  operationOptions: [
    { id: '1', name: "检查管理范围内河道是否存在游泳、捕鱼和船只水上作业等可能危机人身安全的情况", checked: true },
    { id: '2', name: "检查进水口拦污栅是否通畅", checked: true },
    { id: '3', name: "检查宁郭塘闸门是否开启", checked: true },
    { id: '4', name: "至10kV 102计量柜，旋转上方的电压转换开关，检查三相电压是否平衡及缺相", checked: true },
    { id: '5', name: "至低压配电室内转动总柜上方电压表旋转开关检查三相总电压是否平衡", checked: true },
    { id: '6', name: "确认所有低压电气柜开关处于断开状态", checked: true },
    { id: '7', name: "至P3出线柜，用手柄顺时针掰动90°由\"○ OFF\"转至\"I ON\"刀闸开关合闸", checked: true },
    { id: '8', name: "至控制室打开配电总柜柜门，将手柄顺时针掰动90°由\"○ OFF\"转至\"｜ ON\"", checked: true },
    { id: '9', name: "打开配电总柜控制面板，将柜内空气开关依次合上", checked: true },
    { id: '10', name: "1号、2号水泵低压控制柜内的控制方式旋钮统一指向\"手动\"", checked: true },
    { id: '11', name: "按下低压控制柜内的绿色\"手动启动按钮\"按钮启动水泵，绿色\"运行指示\"灯亮起。", checked: true },
    { id: '12', name: "等启动的那台主水泵开机流程结束，运行平稳、仪表显示稳定后才能启动下一台主水泵", checked: true },
  ],

  form: {
    operateCode: '',
    projectId: undefined,
    operateUserId: undefined,
    guardianUserId: undefined,
    startDate: '',
    endDate: '',
    remark: '',
    selectedOperations: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'] // 选中的操作项ID数组，默认全选
  },

  rules: {
    operateCode: [
      { required: true, message: '请输入操作票编码' },
      { max: 128, message: '操作票编码限制128字符' }
    ],
    projectId: [{ required: true, message: '请选择工程名称' }],
    operateUserId: [{ required: true, message: '请选择操作人' }],
    guardianUserId: [{ required: true, message: '请选择负责人' }],
    startDate: [{ required: true, message: '请选择开始时间' }],
    endDate: [{ required: true, message: '请选择结束时间' }],
    remark: [{ max: 512, message: '限制512字符' }]
  }
})

onMounted(() => {
  formRef.value.setRules(state.rules)
  
  // 获取工程列表
  getDispatchProjectList().then(res => {
    state.projectOptions = (res?.data || [])?.map(el => ({
      ...el,
      label: el.projectName,
      value: el.projectId,
    }))
  })
})

// 获取用户列表
const getUsersById = id => {
  getDispatchUser({ dispatchProjectId: id }).then(res => {
    state.userOptions = res?.data?.map(el => ({
      label: el.name,
      value: el.userId + '',
    }))
  })
}

const onSelectConfirm = item => {
  switch (state.select.field) {
    case 'projectName':
      state.select.current1 = item[0]
      state.form.projectId = item[0].value
      getUsersById(state.projectOptions.find(el => el.projectId == item[0].value)?.dispatchProjectId)
      // 清空已选择的人员
      state.select.current2.label = undefined
      state.select.current2.value = undefined
      state.form.operateUserId = undefined
      state.select.current3.label = undefined
      state.select.current3.value = undefined
      state.form.guardianUserId = undefined
      return
    case 'operateUser':
      state.select.current2 = item[0]
      state.form.operateUserId = item[0].value
      return
    case 'guardianUser':
      state.select.current3 = item[0]
      state.form.guardianUserId = item[0].value
      return
    default:
      return
  }
}

const onOpenTimePicker = (type) => {
  state.pickType = type
  state.showPickerTime = true
}

const onTimeConfirm = p => {
  const timeStr = `${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}`
  if (state.pickType === 'start') {
    state.form.startDate = timeStr
  } else {
    timeComparison(state.form.startDate, timeStr)
    state.form.endDate = timeStr
  }
}

const timeComparison = (startTime, endTime) => {
  if (endTime < startTime) {
    uNotifyRef.value.show({
      type: 'warning',
      title: '结束时间不能小于开始时间',
      duration: 800,
    })
    state.form.endDate = ''
    return
  }
}

const handleSubmit = () => {
  console.log(state.form.selectedOperations)
  formRef.value.validate(valid => {
    if (valid) {
      // 构建请求参数
      const params = {
        operateCode: state.form.operateCode,
        projectId: state.form.projectId,
        startDate: state.form.startDate + ':00',
        endDate: state.form.endDate + ':00',
        remark: state.form.remark,
        operateName: state.select.current2.label, // 操作人姓名
        guardianName: state.select.current3.label, // 负责人姓名
        isOpen: 0, // 默认值
        items: state.operationOptions.map(operation => {
          return {
            content: operation.name, 
            status: state.form.selectedOperations.includes(operation.id) ? 1 : 0 // 选中为1，未选中为0
          }
        }),
        operateDate: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 当前时间作为操作日期
        cmdCode: props.runCmd, // 使用操作票编码作为指令编码
        // acceptorName: '', // 接收人姓名，暂时为空
        // starterName: '', // 发起人姓名，暂时为空
        // operateTask: '' // 操作任务，暂时为空
      }
      // 调用接口
      addOperateCmd(params).then(res => {
        if (res.code === 200) {
          uNotifyRef.value.show({
            type: 'success',
            title: '提交成功',
            duration: 800,
          })
          setTimeout(() => {
            router.push({
              path: '/pages/run-manage/run-instruction/index',
            })
          }, 800)
        } else {
          uNotifyRef.value.show({
            type: 'error',
            title: res.msg || '提交失败',
            duration: 800,
          })
        }
      }).catch(err => {
        uNotifyRef.value.show({
          type: 'error',
          title: '提交失败',
          duration: 800,
        })
        console.error('提交操作票失败:', err)
      })
    }
  })
}

// 添加操作项变化处理函数
const handleOperationChange = (values) => {
  // 确保values是数组
  const valuesArray = Array.isArray(values) ? values : values.detail.value || [];
  state.form.selectedOperations = valuesArray;
  // 更新选中状态
  state.operationOptions.forEach(item => {
    item.checked = valuesArray.includes(item.id);
  });
}

// 检查操作票编码长度
const checkOperateCodeLength = () => {
  if (state.form.operateCode.length > 128) {
    uNotifyRef.value.show({
      type: 'warning',
      title: '操作票编码限制128字符',
      duration: 1500,
    })
    state.form.operateCode = state.form.operateCode.slice(0, 128)
  }
}

// 检查备注长度
const checkRemarkLength = () => {
  if (state.form.remark.length > 512) {
    uNotifyRef.value.show({
      type: 'warning',
      title: '限制512字符',
      duration: 1500,
    })
    state.form.remark = state.form.remark.slice(0, 512)
  }
}
</script>

<style lang="scss" scoped>
.checkbox-item {
  padding: 20rpx 0;
//   border-bottom: 1px solid #E5E6EB;
  display: block;
  width: 100%;
  
  &:last-child {
    border-bottom: none;
  }
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  
  .checkbox-label {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #1D2129;
  }
}

// .fixed-bottom {
//   position: fixed;
//   bottom: 0;
//   left: 0;
//   right: 0;
//   padding: 20rpx 30rpx;
//   background: #fff;
//   box-shadow: 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  
//   .submit-btn {
//     width: 100% !important;
//     border-radius: 60rpx;
//   }
// }

.form-container {
//   padding-bottom: 20rpx;
}

.textarea {
  :deep(.u-form-item__body) {
    display: block;
  }
  :deep(.u-form-item--left) {
    width: 100% !important;
  }
  :deep(.u-form-item--right) {
    height: 268rpx;
    border-radius: 8rpx;
    background: #f2f3f5;
    border: 1px solid #e5e6eb;
  }
}

.form-btn-group {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0 40rpx 0;
  
//   .form-cancel-btn,
//   .form-sure-btn {
//     width: 320rpx !important;
//     border-radius: 60rpx;
//   }
  .receive-btn {
    width: calc(100% - 60rpx) !important;
    border-radius: 60rpx;
  }
  .form-cancel-btn {
    background: #F2F3F5;
    color: #4E5969;
  }
}

:deep(.uni-checkbox-input) {
    background-color: #fff;
}
:deep(.uni-checkbox-input svg) {
    color: #fff;
}

:deep(.uni-checkbox-input.uni-checkbox-input-checked) {
    background-color: #3772FF !important;
    border-color: #3772FF !important;
}
</style>
