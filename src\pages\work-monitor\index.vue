<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="工情监测" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="search-input">
      <SvgIcon name="search" class="icon-search" />
      <u-input v-model="state.searchVal" :border="true" @update:model-value="onSearchChange" clearable />
    </view>

    <view class="relative">
      <u-dropdown
        ref="dropdownRef"
        height="60"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 20rpx 0"
        @open="onDropdownOpen"
      >
        <u-dropdown-item
          height="700"
          v-model="state.projectType.value"
          :title="state.projectType.title"
          :options="state.projectTypeOptions"
          @change="onProjectTypeChange"
        ></u-dropdown-item>

        <u-dropdown-item :title="state.district.title">
          <scroll-view class="drop-content" scroll-y show-scrollbar>
            <DaTree
              ref="districtTreeRef"
              :data="state.districtTree"
              labelField="districtName"
              valueField="districtCode"
              defaultExpandAll
              @change="handleDistrictTreeChange"
            ></DaTree>
          </scroll-view>
        </u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <scroll-view
    :style="{ 'max-height': `calc(100vh - 44px - 220rpx - ${userStore.statusBarHeight}px)` }"
    scroll-y
    show-scrollbar
  >
    <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
      <view class="header">
        <view class="left">
          <image class="img" :src="projectTypes[el.projectType]" alt="" />
          <view class="title">{{ el.projectName || '-' }}</view>
        </view>
        <view class="flex items-center">
          <view
            v-for="(item, index) in el.devices"
            :key="index"
            class="device-item"
            :style="{ background: item.isOnline === 1 ? '#E7FDE6' : '#F2F3F5' }"
          >
            {{ item.deviceName.slice(0, item.deviceName.indexOf('#') + 1) }}

            <image class="device-img" :src="stateTypes[item.isOnline]" alt="" />
          </view>
        </view>
      </view>
      <view style="font-size: 24rpx; color: #86909c">行政区划: {{ el.districtName }}</view>
    </view>
  </scroll-view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import TextReadMore from '@/components/hbxw-text-folding/components/hbxw-text-folding/hbxw-text-folding.vue'
  import { getOptions } from '@/api/common.ts'
  import { getProjectDeviceList, getDistrictTree } from './services'
  import dayjs from 'dayjs'
  import DaTree from '@/components/da-tree/index.vue'
  import { projectTypes, stateTypes } from './config.ts'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const dropdownRef = ref(null)
  const districtTreeRef = ref(null)

  const state = reactive({
    searchVal: undefined,
    projectTypeOptions: [],
    projectType: { value: undefined, title: '工程类型', preVal: undefined },

    districtTree: [],
    district: { title: '行政区划', value: undefined },

    list: [],
  })

  onShow(() => {
    getList(1)
  })

  onMounted(() => {
    getOptions('automateType').then(res => {
      state.projectTypeOptions = res?.data?.map(el => ({ label: el.value, value: el.key }))
    })
    getDistrictTree().then(res => {
      state.districtTree = res.data?.[0]?.children || []
    })
  })

  const getList = (pageNo, pageSize) => {
    getProjectDeviceList({
      projectName: state.searchVal,
      projectType: state.projectType.value,
      districtCode: state.district.value,
    }).then(res => {
      state.list = res.data || []
    })
  }

  const onSearchChange = _.debounce(val => {
    nextTick(() => {
      getList(1)
    })
  }, 500)

  function onProjectTypeChange(value) {
    if (value === state.projectType.preVal) {
      state.projectType = { title: '工程类型', value: undefined, preVal: undefined }
    } else {
      state.projectType = {
        title: state.projectTypeOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    getList(1)
  }

  function handleDistrictTreeChange(allSelectedKeys, currentItem) {
    if (state.district.value === currentItem.key) {
      districtTreeRef.value?.setCheckedKeys(currentItem.key, false)
      state.district = { title: '行政区划', value: undefined }
    } else {
      state.district = { title: currentItem.label, value: currentItem.key }
    }

    getList(1)
    dropdownRef.value.close()
  }

  const onItemClick = el => {
    uni.setStorageSync('workMonitorActive', el)
    router.push({
      path: '/pages/work-monitor/detail/index',
    })
  }
</script>

<style lang="scss" scoped>
  .search-input {
    padding: 10rpx 34rpx 15rpx;
    position: relative;
    .icon-search {
      position: absolute;
      left: 60rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    :deep(.u-input.u-input--border) {
      padding-left: 70rpx !important;
      border-radius: 100rpx;
    }
  }

  :deep(.u-dropdown__menu) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 40rpx;
    .u-dropdown__menu__item {
      flex: unset;
      margin-left: 40rpx;
      margin-right: 40rpx;
    }
  }
  .drop-content {
    background: #fff;
    max-height: calc(100vh - 300rpx);
    padding: 10rpx 28rpx;
  }

  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;
    padding: 0 32rpx 24rpx;

    .header {
      font-size: 32rpx;
      color: #1d2129;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 0;
      .left {
        display: flex;
        align-items: center;
        flex: 1;

        .img {
          width: 54rpx;
          height: 54rpx;
          margin: 0 0 -6rpx -16rpx;
        }
        .title {
          flex: 1;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .device-item {
        border-radius: 6rpx;
        display: flex;
        align-items: center;
        padding: 4rpx 8rpx;
        margin-left: 8rpx;
        color: #4e5969;
        font-size: 24rpx;
        .device-img {
          margin-left: 8rpx;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
</style>
