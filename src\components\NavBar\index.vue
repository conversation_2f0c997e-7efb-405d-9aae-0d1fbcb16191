<script setup lang="ts" name="NavBar">
  interface Props {
    title: string
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '',
  })
</script>

<template>
  <u-navbar
    :title="props.title"
    :border-bottom="false"
    is-back
    back-text=""
    back-icon-size="36"
    title-size="34"
    title-color="#050505"
  >
    <template>
      <slot></slot>
    </template>

    <template #right>
      <slot name="right"></slot>
    </template>
  </u-navbar>
</template>
<style lang="scss" scoped>
  :deep(.uicon-nav-back) {
    color: transparent;
    background: url('../../static/images/back_arrow.png') 0 0 no-repeat;
    background-size: 100%;
    font-weight: 700 !important;
  }

  :deep(.u-title) {
    color: #1d2129;
    font-weight: 600 !important;
    font-family: Poppins-SemiBold, Poppins, PingFang SC-Medium;
  }
</style>
