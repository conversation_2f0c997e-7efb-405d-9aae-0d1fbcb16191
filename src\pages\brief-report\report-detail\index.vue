<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="任务详情" :background="{ background: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="info-card">
      <view class="header">
        <text>发布时间: {{ props.publishTime === 'null' ? '-' : props.publishTime || '-' }}</text>
        <view :class="`tag tag${props.status}`">
          {{ subOptionsAll.find(ele => ele.value == props.status)?.name }}
        </view>
      </view>

      <view class="card-content">
        <view class="title">
          <SvgIcon name="report-icon" class="mr-[16rpx]" />
          {{ props.reportName }}
        </view>

        <view class="time-line">
          <view class="time">创建时间: {{ props.createdTime || '-' }}</view>
          <view class="time">创建人: {{ state.createdUserName || '-' }}</view>
          <view class="time" v-if="props.status == 3">发布人: {{ props.publisherName || '-' }}</view>
        </view>
      </view>
    </view>

    <view class="gap" style="height: 16rpx"></view>

    <view class="container" v-if="props.status != 1">
      <u-section title="简报预览" font-size="34" line-color="#3772FF" :right="false"></u-section>
      <template v-if="props.docUrl && props.docUrl != 'null'">
        <PreviewOffice :fileUrl="props.docUrl" :value="true">
          <view class="h-[500rpx] text-[16px] text-center align-middle leading-[300rpx] text-[#3772FF]">
            点击使用其他工具预览
          </view>
        </PreviewOffice>
      </template>
    </view>
  </view>

  <view
    class="bottom-button"
    v-if="(props.currentTab == 0 && props.status == 1) || (props.currentTab == 1 && props.status == 2)"
  >
    <u-button type="primary" size="default" v-if="props.currentTab == 0 && props.status == 1" @click="onBtnClick">
      生成简报
    </u-button>

    <u-button type="primary" size="default" v-if="props.currentTab == 1 && props.status == 2" @click="onBtnClick">
      发布
    </u-button>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { onLoad } from '@dcloudio/uni-app'
  import { reportGet, userReportReleaseUpdate, adminReportReleaseUpdate } from '../services.ts'
  import { getUserList } from '@/api/common.ts'
  import { subOptionsAll, subAuditOptions } from '../config.ts'
  import PreviewOffice from '@/components/PreviewOffice/index.vue'

  const router = useRouter()

  const uNotifyRef = ref()

  const props = defineProps({
    reportId: undefined,
    reportName: undefined,
    docUrl: undefined,
    createdTime: undefined,
    createdUserId: undefined,
    publishTime: undefined,
    publisherId: undefined,
    status: undefined,
    currentTab: undefined
  })

  const state = reactive({
    createdUserName: ''
  })

  onLoad(() => {})

  getUserList().then(res => {
    state.createdUserName = (res.data || []).find(el => el.userId == props.createdUserId)?.name
  })

  const onBtnClick = () => {
    if (props.currentTab == 0 && props.status == 1) {
      userReportReleaseUpdate({
        issue: props.issue,
        reportId: props.reportId,
        reportName: props.reportName,
        reportTemplateId: props.reportTemplateId,
        statEndTime: props.statEndTime,
        statStartTime: props.statStartTime,
        year: props.year
      }).then(res => {
        uNotifyRef.value.show({
          type: 'success',
          title: '生成成功',
          duration: 800
        })

        setTimeout(() => {
          router.back()
        }, 800)
      })
    }
    if (props.currentTab == 1 && props.status == 2) {
      adminReportReleaseUpdate({
        reportId: props.reportId
      }).then(res => {
        uNotifyRef.value.show({
          type: 'success',
          title: '发布成功',
          duration: 800
        })

        setTimeout(() => {
          router.back()
        }, 800)
      })
    }
  }
</script>

<style lang="scss" scoped>
  .info-card {
    background: #e0eaf9 url('@/static/images/info-card-bg.png') no-repeat;
    background-size: 100% 400rpx;
    padding: 20rpx;
    margin: 0 20rpx;
    border-radius: 14rpx;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 64rpx;

      font-size: 24rpx;
      color: #ffffff;
      line-height: 28rpx;
    }

    .card-content {
      background-color: #fff;
      border-radius: 16rpx;

      .title {
        padding: 20rpx 20rpx 0 20rpx;
        font-size: 36rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 400;
        color: #1d2129;
        line-height: 52rpx;

        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }

    .time-line {
      padding: 16rpx 20rpx 24rpx;
      .time {
        font-size: 28rpx;
        font-weight: 300;
        color: #86909c;
        line-height: 28rpx;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .tag {
      width: 112rpx;
      height: 44rpx;
      line-height: 44rpx;
      text-align: center;
      font-size: 24rpx;
      font-weight: 300;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 80rpx;
    }
    .tag1 {
      color: #ff9a2e;
    }
    .tag2 {
      color: #f76560;
    }
    .tag3 {
      color: #4e5969;
    }
  }

  .container {
    background-color: #fff;
    border-radius: 32rpx;
    padding: 40rpx 32rpx 0;
  }

  .bottom-button {
    // height: 194rpx;
    padding: 26rpx 32rpx;
    background: #ffffff;
    box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.3);
    border-radius: 50rpx 50rpx 0 0;
    position: fixed;
    bottom: 0;
    width: 100vw;
    z-index: 999;
  }
</style>
