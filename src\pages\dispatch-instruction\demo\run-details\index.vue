<template>
  <view class="page-details-bg" style="padding-bottom: 12rpx">
    <NavBar title="指令详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    
    <!-- 加载状态 -->
    <!-- <view v-if="state.loading" class="loading-container">
      <u-loading-icon mode="flower"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view> -->
    <view class="com-card details-card-box">
      <view class="card-title details-card-title">{{ state.details.cmdName }}</view>
      <view class="details-body">
        <view class="details-card">
          <view class="card-row">
            <text class="label">工作票编号</text>
            <text class="value">{{ state.details.cmdCode }}</text>
          </view>

          <view class="card-row">
            <text class="label">工作票名称</text>
            <text class="value">{{ state.details.cmdName }}</text>
          </view>
          <view class="card-row">
            <text class="label">调度方案</text>
            <text class="value">{{ state.details.dispatchCode == null ? '--' : state.details.dispatchCode }}</text>
          </view>

          <view class="card-row">
            <text class="label">调度类型</text>
            <text class="value">{{ state.details.dispatchTypeCode === '1' ? '防汛调度' : state.details.dispatchTypeCode === '2' ? '灌溉调度' : state.details.dispatchTypeCode === '3' ? '生态调水' : '--' }}</text>
          </view>

          <view class="card-row">
            <text class="label">计划工作时间</text>
            <text class="value work-time">{{ state.details.planStartDate }}至{{ state.details.planEndDate }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="com-card action-info-card">
      <panelTitle style="margin: 0" title="工程" />
                <view class="details-body"style="margin-top: 20rpx">
          <view v-if="state.projectInfo.length > 0">
            <view class="details-list-card" v-for="(item, index) in state.showProjectList" :key="index">
              <view class="card-row">
                <text class="title">{{ item.projectName }}</text>
              </view>
              <view class="card-row">
                <text class="label">工作负责人</text>
                <text class="value">{{ item.wardUserName || '--' }}</text>
              </view>
              <view class="card-row">
                <text class="label">备注信息</text>
                <text class="value">{{ item.remark || '--' }}</text>
              </view>
              <view class="divider" v-if="index !== state.showProjectList.length - 1"></view>
            </view>
            <view class="more-btn" v-if="state.projectInfo.length > 2" @click="toggleProject">
              <text>{{ state.showMoreProject ? '折叠' : '更多' }}</text>
              <image :src="state.showMoreProject ? '/src/static/images/arrow-up.png' : '/src/static/images/arrow-down.png'" class="arrow-icon" />
            </view>
          </view>
          <view v-else class="empty-state">
            <text class="empty-text">暂无工程信息</text>
          </view>
      </view>
    </view>

    <view class="com-card action-info-card">
      <panelTitle title="审批信息" style="margin: 0" />
        <view class="details-body">
          <view class="details-card">
            <view class="card-row">
              <text class="label">审批人</text>
              <text class="value">{{ state.approvalInfo.auditUserName || '--' }}</text>
            </view>
            <view class="card-row">
              <text class="label">审批意见</text>
              <text class="value">{{ state.approvalInfo.auditResult || '--' }}</text>
            </view>
            <view class="card-row">
              <text class="label">审批时间</text>
              <text class="value">{{ state.approvalInfo.auditTime || '--' }}</text>
            </view>
            <view class="card-row">
              <text class="label">备注信息</text>
              <text class="value">{{ state.approvalInfo.remark || '--' }}</text>
            </view>
        </view>
      </view>
    </view>

    <view class="com-card action-info-card">
      <panelTitle title="工程操作信息" style="margin: 0" />
        <view class="details-body" style="margin-top: 20rpx">
          <view v-if="state.operationInfo.length > 0">
            <view class="details-list-card" v-for="(item, index) in state.showOperationList" :key="index">
              <view class="card-row">
                <text class="title">{{ item.projectName || '--' }}</text>
                <text class="tag" :class="getTagClass(item.status)">{{ item.status }}</text>
              </view>
              <view class="card-row">
                <text class="label">操作时间</text>
                <text class="value">{{ item.operationTime || '--' }}</text>
              </view>
              <view class="card-row">
                <text class="label">操作人</text>
                <text class="value">{{ item.operator || '--' }}</text>
              </view>
              <view class="card-row">
                <text class="label">负责人</text>
                <text class="value">{{ item.guardian || '--' }}</text>
              </view>
              <view class="divider" v-if="index !== state.showOperationList.length - 1"></view>
            </view>
            <view class="more-btn" v-if="state.operationInfo.length > 2" @click="toggleOperation">
              <text>{{ state.showMoreOperation ? '折叠' : '更多' }}</text>
              <image :src="state.showMoreOperation ? '/src/static/images/arrow-up.png' : '/src/static/images/arrow-down.png'" class="arrow-icon" />
            </view>
          </view>
          <view v-else class="empty-state">
            <text class="empty-text">暂无工程操作信息</text>
          </view>
      </view>
    </view>
  </view>
</template>
<script setup>
  import { reactive, onMounted, computed } from 'vue'
  import moment from 'moment'
  const props = defineProps(['runCmdId', 'cmdCode'])

  import { getRunInstructionDetails, getOperateTicket } from '../services'

  const state = reactive({
    details: {},
    // 工程信息数据
    projectInfo: [],
    showMoreProject: false,
    // 审批信息数据
    approvalInfo: {},
    // 工程操作信息数据
    operationInfo: [],
    showMoreOperation: false,
    // 加载状态
    loading: true
  })

  // 计算属性，根据是否展开显示不同数量的工程信息
  const showProjectList = computed(() => {
    return state.showMoreProject ? state.projectInfo : state.projectInfo.slice(0, 2)
  })
  
  // 计算属性，根据是否展开显示不同数量的工程操作信息
  const showOperationList = computed(() => {
    return state.showMoreOperation ? state.operationInfo : state.operationInfo.slice(0, 2)
  })

  // 展开/折叠工程信息
  const toggleProject = () => {
    state.showMoreProject = !state.showMoreProject
  }

  // 展开/折叠工程操作信息
  const toggleOperation = () => {
    state.showMoreOperation = !state.showMoreOperation
  }

  // 将计算属性添加到state中
  state.showProjectList = showProjectList
  state.showOperationList = showOperationList

  onMounted(() => {
    if (!props.runCmdId) {
      console.error('缺少必要参数 runCmdId')
      uni.showToast({
        title: '参数错误',
        icon: 'error'
      })
      state.loading = false
      return
    }
    
    getRunInstructionDetails({ runCmdId: props.runCmdId }).then(res => {
      const data = res?.data
      if (data) {
        // 基本详情信息
        state.details = data
        
        // 工程信息数据 - 从接口数据中提取
        state.projectInfo = data.projectList || []
        
        // 审批信息数据 - 从接口数据中提取
        state.approvalInfo = {
          auditUserName: data.auditUserName || '--',
          auditResult: data.auditResult === 1 ? '同意' : data.approveOpinion === 0 ? '拒绝' : '--',
          auditTime: data.auditTime || '--',
          remark: data.remark || '--'
        }
        
        // 工程操作信息数据 - 从接口数据中提取
        if (data.projectList && data.projectList.length > 0) {
          // 检查是否有完整的操作信息
          // const hasOperationInfo = data.projectList.some(project => 
          //   project.operationTime || project.operatorName || project.guardianName
          // )
          
          // if (hasOperationInfo) {
            // 如果主接口已包含操作信息，直接使用
            state.operationInfo = data.projectOpInfoList.map(project => ({
              projectName: project.projectName || '--',
              status: getOperationStatus(project.recStatusCode) || '--',
              operationTime: moment(project.operateDate).format('YYYY-MM-DD HH:mm:ss') || '--',
              operator: project.operateName || '--',
              guardian: project.guardianName || '--'
            }))
          // } 
          console.log(state.operationInfo)
        }
        
      } else {
        uni.showToast({
          title: '暂无数据',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取工程票详情失败:', err)
      // uni.showToast({
      //   title: '获取详情失败',
      //   icon: 'error'
      // })
    }).finally(() => {
      state.loading = false
    })

    // getOperateTicket({ cmdCode: props.cmdCode, pageNum: 1, pageSize: 1000 }).then(res => {
    //   console.log(res, props.cmdCode)
    //   state.operationInfo = res.data.data.map(el => ({
    //     projectName: el.projectName,
    //     status: getOperationStatus(el.recStatusCode),
    //     operationTime: el.operateDate,
    //     operator: el.operateName,
    //     guardian: el.guardianName
    //   }))
    // })
  })
  
  // 根据状态码获取操作状态文本
  const getOperationStatus = (status) => {
    console.log(status)
    switch (status) {
      case 0:
        return '暂未接收'
      case 1:
        return '已接收'
      default:
        return '未知'
    }
  }
  
  // 根据状态获取标签样式类
  const getTagClass = (status) => {
    switch (status) {
      case '暂未接收':
        return 'tag-pending'
      case '已接收':
        return 'tag-completed'
      default:
        return 'tag-unknown'
    }
  }
</script>
<style lang="scss" scoped>
.divider {
  height: 2rpx;
  border-top: 1px dashed #E5E6EB;
  margin: 16rpx 0 16rpx 0;
}

.more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0 0 0;
  color: #3772FF;
  font-size: 28rpx;

  .arrow-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 8rpx;
  }
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #fff;
}

.tag-received {
  background-color: #3772FF;
}

.tag-pending {
  background-color: #FFECE8 !important;
  color: #F53F3F !important;
  border-color: #F53F3F !important;
}

.tag-processing {
  background-color: #FF9500;
}


.tag-unknown {
  background-color: #F5222D;
}

.action-info-card {
    background: linear-gradient(to bottom, #E9F4FF 3%, #ffffff 18%);
  }

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .loading-text {
    margin-top: 20rpx;
    color: #666;
    font-size: 28rpx;
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}
</style>
