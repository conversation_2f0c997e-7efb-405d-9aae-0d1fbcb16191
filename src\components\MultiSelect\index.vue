<template>
  <u-popup mode="bottom" :round="10" class="popup-content">
    <view class="popup-header">
      <text class="popup-title">请选择</text>
      <u-button :hair-line="false" class="popup-close" @click="close">×</u-button>
    </view>
    <view class="popup-container">
      <scroll-view class="container" scroll-y>
        <checkbox-group @change="checkboxChange">
          <label class="checkbox-label" v-for="item in props.selectOptions" :key="item.value">
            <checkbox :value="item.value" />
            {{ item.label }}
          </label>
        </checkbox-group>
      </scroll-view>

      <view class="btn-group">
        <u-button class="cancel-btn" type="primary" @click="close">取消</u-button>
        <u-button class="submit-btn" type="primary" @click="ok">确定</u-button>
      </view>
    </view>
  </u-popup>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue'

  const props = defineProps(['selectOptions'])

  const state = reactive({
    ids: [],
    userIdNames: [],
  })

  const emits = defineEmits(['onMultipleChoice', 'close'])

  const checkboxChange = e => {
    state.ids = e.detail.value
  }

  const ok = () => {
    if (state.ids?.length) {
      const nameArray = state.ids.map(id => {
        const foundItem = props?.selectOptions.find(item => item.value === id)
        return foundItem ? foundItem.label : ''
      })

      emits('onMultipleChoice', state.ids?.join(','), nameArray?.join(','))
    }
  }
  const close = () => {
    emits('close')
  }
</script>

<style lang="scss" scoped>
  .popup-content {
    color: #1d2129;
    .popup-header {
      width: 100%;
      height: 80rpx;
      padding-top: 40rpx;
      text-align: center;
      position: relative;
      border-bottom: 1px solid #f2f3f5;
      padding-bottom: 80rpx;
      .popup-title {
        color: #1d2129;
        font-family: 'PingFang SC';
      }
      .popup-close {
        font-size: 60rpx;
        border: none;
        position: absolute;
        top: 20rpx;
        right: 10rpx;
        width: 100rpx;
        color: #4e5969;
      }
    }
    .popup-container {
      padding: 40rpx;
      .container {
        height: 360px;
        overflow-x: auto;
      }
      .checkbox-label {
        display: inline-block;
        width: 310rpx;
        margin-bottom: 6rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .btn-group {
      display: flex;
    }
    .cancel-btn {
      width: 40%;
      margin-top: 30rpx;
      color: #666;
      background: #fff7e8;
    }
    .submit-btn {
      width: 44%;
      margin-top: 30rpx;
      height: 96rpx;
    }
  }
</style>
