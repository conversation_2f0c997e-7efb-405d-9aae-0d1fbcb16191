<template>
  <view class="page-details-bg">
    <NavBar title="操作票详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    <view class="com-card details-card-box" style="height: 604rpx">
      <view class="card-title details-card-title">{{ state.details.operateCode }}</view>
      <view class="details-body">
        <view class="details-card">
          <view class="card-row">
            <text class="label">操作票编号</text>
            <text class="value">{{ state.details.operateCode || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">工程名称</text>
            <text class="value">{{ state.details.projectName || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">操作日期</text>
            <text class="value">{{ state.details.operateDate || '--' }}</text>
          </view>

          <view class="card-row">
            <text class="label">操作人</text>
            <text class="value">{{ state.details?.operateName || '--' }}</text>
          </view>
          <!-- <view class="card-row">
            <text class="label">发令人</text>
            <text class="value">{{ state.details.guardianName || '--' }}</text>
          </view>
          <view class="card-row">
            <text class="label">发令时间</text>
            <text class="value">{{ state.details.starterDate || '--' }}</text>
          </view> -->
          <view class="card-row">
            <text class="label">负责人</text>
            <text class="value">{{ state.details.guardianName }}</text>
          </view>
          <view class="card-row">
            <text class="label">操作开始时间</text>
            <text class="value">{{ state.details.startDate }}</text>
          </view>

          <view class="card-row">
            <text class="label">操作结果时间</text>
            <text class="value">{{ state.details?.endDate == null ? '--' : state.details?.endDate }}</text>
          </view>
          <view class="card-row">
            <text class="label">备注</text>
            <text class="value work-time">{{ state.details?.remark == null || state.details?.remark == '' ? '--' : state.details?.remark }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="com-card details-card-box">
      <view class="operation-card">
        <view class="details-list-row">
          <panelTitle title="操作项目" />
          <image class="details-list" src="@/static/images/details-list.png"></image>
        </view>

        <view class="operation-content" v-for="(el, i) in state.details?.operateCmdDetailsList" :key="i">
          <view class="top">
            <text class="text">操作内容：</text>
            <text class="result-bg" :class="{ close: !el.status }">
              <div class="result-txt">结果: </div>
              <image class="icon close" v-if="!el.status" src="@/static/images/operation-ticket-close.png"></image>
              <image class="icon" v-else src="@/static/images/operation-ticket-ok.png"></image>
            </text>
          </view>
          <view class="content">{{ el.content }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
  import { reactive, onMounted } from 'vue'
  const props = defineProps(['operateCmdId'])

  import { getOperateTicketDetails, getDispatchProjectList } from '../services'

  const state = reactive({
    details: {
    },
    projectOptions:[]
  })

  onMounted(() => {
    getDispatchProjectList().then(res => {
      state.projectOptions = (res?.data || [])?.map(el => ({
        ...el,
        label: el.projectName,
        value: el.projectId,
        dispatchProjectId: el.dispatchProjectId,
      }))
    })
    getOperateTicketDetails({ operateCmdId: props.operateCmdId }).then(res => {
      state.details = res?.data
      state.details.projectName = state.projectOptions.find(el => el.value === state.details.projectId)?.label

    })
  })
</script>
<style lang="scss" scoped>
  .operation-card {
    height: 100%;
  }
  .details-list-row {
    display: flex;
    align-items: center;
    .details-list {
      width: 84rpx;
      height: 84rpx;
      margin-left: auto;
    }
  }
  .operation-content {
    width: 94%;
    margin: 30rpx auto;
    position: relative;
    border-radius: 24rpx;
    background: #eef5ff;
    border: 1px solid #eaf2fd;
    .top {
      display: flex;
      .text {
        color: #86909c;
        padding: 24rpx 24rpx 0 24rpx;
      }

      .result-bg {
        width: 116rpx;
        height: 48rpx;
        text-align: center;
        margin-left: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url('@/static/images/operation-ticket-ok-bg.png') no-repeat 0 0 / 100% 100%;
        .result-txt {
          font-size: 24rpx;
          font-weight: 400;
          color: #3C5985;
          line-height: normal;
        }
        &.close {
          background: url('@/static/images/operation-ticket-close-bg.png') no-repeat 0 0 / 100% 100%;
        }
        .icon {
          width: 32rpx;
          height: 24rpx;
          margin-top: 10rpx;
          &.close {
            width: 24rpx;
          }
        }
      }
    }
    .content {
      padding: 24rpx;
      padding-top: 16rpx;
      color: #1d2129;
      font-size: 30rpx;
    }
  }
</style>
