<template>
  <view class="home-content">
    <view class="com-header header">
      <view class="title-group">
        <image class="logo" src="../../static/logo.png" />
        <text class="title ml-[16rpx]">桃花江灌区</text>
      </view>
      <view class="user-name" @click="onUserClick">
        <image class="avatar" v-if="state.avatar" :src="state.avatar" />
        <text class="avatar" v-else>{{ state.userName?.slice(state?.userName?.length - 2) }}</text>
        <text class="user-name-text">{{ state.userName }}</text>
      </view>
    </view>

    <view class="section com-card">
      <view
        v-for="(el, idx) in state.apps"
        :key="idx"
        @click="onAppClick(el)"
        :class="`flex flex-col items-center app ${
          idx > state.apps.length - (state.apps.length % 4 || 4) - 1 ? 'pb-[0]' : 'pb-[28rpx]'
        }`"
      >
        <image class="img" :src="el.icon" alt="" />
        <text class="label">{{ el.menuName }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { usePatrolStore } from '@/store/modules/patrol.ts'
  import dayjs from 'dayjs'
  import { allApps } from './config'
  import { getUserInfo, getUserProfile, getRouters, getDeptLoginOrtList } from '@/api/common.ts'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import QueryString from 'query-string'
  import { getMyTaskList } from '@/pages/patrol/patrol-list/services.ts'

  const userStore = useUserStore()
  const patrolStore = usePatrolStore()
  const router = useRouter()

  const state = reactive({
    apps: [],
    weather: null,
    userName: '',
    avatar: '',
  })

  onLoad(() => {
    // getUserProfile()
    getUserInfo(userStore.user.userId).then(res => {
      state.userName = res?.data?.name
      state.avatar = res?.data?.avatar
      userStore.setUserInfo(userStore.token, res.data)
    })

    getDeptLoginOrtList().then(res => {
      userStore.setOrtList((res.data || []).map((el, i) => ({ ...el, extra: i })))
    })

    getRouters().then(res => {
      state.apps = res?.data?.map(item => {
        const r = allApps.find(el => el.url === item.route)

        return { ...item, icon: r?.icon }
      })

      userStore.setRoutes(state.apps)
    })

    // 获取巡检中的任务
    patrolStore.setFrequencyTime(() => {
      getMyTaskList({
        taskStatus: 2,
        patrolType: 2,
      }).then(res => {
        // 继续巡检中的任务
        if (res?.data?.length > 0) {
          patrolStore.setPatrolStart(res.data[0].taskId, true, res => {})
        }
      })
    })
  })
  onShow(() => {
    getUserInfo(userStore?.user?.userId).then(res => {
      state.userName = res?.data?.name
      state.avatar = res?.data?.avatar
      userStore.setUserInfo(userStore.token, res.data)
    })
  })

  const onUserClick = () => {
    uni.switchTab({
      url: '/pages/user/index',
    })
  }

  function onAppClick(ele) {
    if (ele.route.includes('?')) {
      router.push({
        path: `/pages${ele.route.split('?')[0]}/index`,
        query: QueryString.parse(ele.route.split('?')[1]),
      })
    } else {
      router.push(`/pages${ele.route}/index`)
    }
  }
</script>

<style scoped lang="scss">
  @import './index.scss';
</style>
