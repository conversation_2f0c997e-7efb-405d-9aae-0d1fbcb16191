<template>
  <view class="page-nav-top-common-bg1">
    <NavBar title="水雨情简报" :background="{ background: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
    <view class="pr-[10rpx] pl-[20rpx]">
      <u-tabs
        :list="state.tabsList"
        bg-color="transparent"
        v-model="state.currentTab"
        active-color="#165DFF"
        bar-width="80"
        @change="onTabChange"
      ></u-tabs>
    </view>
  </view>

  <view class="search-bar">
    <u-subsection
      class="sub-content"
      active-color="#165DFF"
      :list="state.subOptions"
      v-model="state.subVal"
      @change="onSubChange"
    ></u-subsection>

    <u-dropdown
      ref="dropdownRef"
      height="80"
      title-size="28"
      menu-icon="arrow-down-fill"
      menu-icon-size="20"
      style="margin: 30rpx 0"
    >
      <u-dropdown-item
        v-model="state.publisher.value"
        height="850"
        :title="state.publisher.title"
        :options="state.userList"
        @change="onPublisherChange"
      ></u-dropdown-item>
    </u-dropdown>
  </view>

  <ZPaging ref="pagingRef" style="margin-top: 200px; z-index: 10" v-model="state.list" @query="getList">
    <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
      <view class="header">
        <text class="header-name">发布时间: {{ el.publishTime || '-' }}</text>

        <u-tag
          size="mini"
          style="font-size: 26rpx"
          border-color="transparent"
          :text="subOptionsAll.find(ele => ele.value == el.status)?.name"
          :type="subOptionsAll.find(ele => ele.value == el.status)?.type"
        />
      </view>

      <view class="content">
        <view class="content-title">
          <SvgIcon name="report-icon" class="mr-[16rpx]" />
          <view class="title">
            {{ el.reportName || '-' }}
          </view>
        </view>
        <view class="create-time">
          <text>创建时间: {{ el.createdTime || '-' }}</text>
          <u-button
            v-if="state.currentTab === 1 && el.status === 2"
            style="justify-content: flex-end; margin: 0"
            type="primary"
            size="mini"
            @click.stop="onPublishClick(el)"
          >
            立即发布
          </u-button>
        </view>
        <view class="create-time mt-[6rpx]" v-if="el.status == 3">发布人: {{ el.publisherName || '-' }}</view>
      </view>
    </view>
  </ZPaging>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { reportUserPage, reportAdminPage, adminReportReleaseUpdate } from '../services'
  import { getUserList } from '@/api/common.ts'
  import { subOptionsAll, subAuditOptions } from '../config.ts'

  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)

  const state = reactive({
    tabsList: [{ name: '简报查看' }, { name: '简报审核' }],
    currentTab: 0,
    subOptions: subOptionsAll,
    subVal: 0,
    userList: [],
    list: [],
    prevSelectValue: undefined,
    publisher: { title: '发布人', value: undefined },
  })

  onLoad(() => {})
  onShow(() => {
    getList(1)
  })

  getUserList().then(res => {
    state.userList = (res.data || []).map(el => ({ ...el, label: el.name, value: el.userId }))
  })

  const onTabChange = index => {
    state.currentTab = index
    state.subVal = 0
    if (index === 0) state.subOptions = subOptionsAll
    if (index === 1) state.subOptions = subAuditOptions
    pagingRef.value.reload()
  }

  const onSubChange = index => {
    state.subVal = index
    pagingRef.value.reload()
  }

  const getList = (pageNo, pageSize) => {
    if (state.currentTab === 0) {
      reportUserPage({
        publisherId: state.publisher.value,
        status: subOptionsAll[state.subVal].value,
        pageNum: pageNo || 1,
        pageSize: 10,
      }).then(res => {
        state.list = res.data?.data || []
        pagingRef.value.complete(res.data?.data || [])
        if (pageNo && pageNo == 1) {
          pagingRef.value.scrollToTop()
        }
      })
    }
    if (state.currentTab === 1) {
      reportAdminPage({
        publisherId: state.publisher.value,
        status: subAuditOptions[state.subVal].value,
        pageNum: pageNo || 1,
        pageSize: 10,
      }).then(res => {
        state.list = res.data?.data || []
        pagingRef.value.complete(res.data?.data || [])
        if (pageNo && pageNo == 1) {
          pagingRef.value.scrollToTop()
        }
      })
    }
  }

  const onPublisherChange = value => {
    if (value === state.prevSelectValue) {
      state.publisher = { title: '发布人', value: undefined }
      state.prevSelectValue = undefined
    } else {
      state.publisher = { title: state.userList.find(el => el.value == value)?.label, value: value }
      state.prevSelectValue = value
    }

    getList(1)
  }

  const onPublishClick = el => {
    adminReportReleaseUpdate({ reportId: el.reportId }).then(res => {
      uNotifyRef.value.show({
        type: 'success',
        title: '发布成功',
        duration: 800,
      })
      setTimeout(() => {
        getList(1)
      }, 800)
    })
  }

  const onItemClick = el => {
    router.push({ path: '/pages/brief-report/report-detail/index', query: { ...el, currentTab: state.currentTab } })
  }
</script>

<style lang="scss" scoped>
  .search-bar {
    background-color: #f9f9f9;
    position: relative;
    height: 140rpx;
    display: flex;
    align-items: center;

    .sub-content {
      position: absolute;
      width: 510rpx;
      height: 100%;
      left: 32rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 12;
    }

    :deep(.u-dropdown__menu) {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .u-dropdown__menu__item {
        flex: unset;
        margin-right: 32rpx;
      }
    }
  }

  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 20rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 32rpx;
      background: #f7f8fa;

      .header-name {
        font-size: 28rpx;
        color: #4e5969;
        line-height: 28rpx;
      }
    }

    .content {
      padding: 0 32rpx 16rpx;
      .content-title {
        font-size: 32rpx;
        color: #1d2129;
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        .title {
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .create-time {
        font-size: 28rpx;
        color: #86909c;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
</style>
