import { request } from '@/utils/request'

// 应急抢修-列表分页查询
export function getEmergencyRepairPage(data: object) {
  return request({
    url: '/emergency/repair/page',
    method: 'post',
    data,
  })
}

//应急抢修-详情
export function getEmergencyRepairDetails(params: object) {
  return request({
    url: '/emergency/repair/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 应急抢修-新增
export function addEmergencyRepair(data: object) {
  return request({
    url: '/emergency/repair/add',
    method: 'post',
    data,
  })
}

// 应急抢修——删除
export function deleteEmergencyRepair(params: object) {
  return request({
    url: '/emergency/repair/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//开始处置
export function disposeEmergencyRepair(params: object) {
  return request({
    url: '/emergency/repair/dispose',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//处置完成
export function disposalCompletedRepair(params: object) {
  return request({
    url: '/emergency/repair/disposeCopy',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//忽略问题
export function loseEmergencyRepair(params: object) {
  return request({
    url: '/emergency/repair/lose',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//撤回
export function withdrawEmergencyRepair(params: object) {
  return request({
    url: '/emergency/repair/recall',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 应急抢修-复核
export function checkEmergencyRepair(data: object) {
  return request({
    url: '/emergency/repair/check',
    method: 'post',
    data,
  })
}
