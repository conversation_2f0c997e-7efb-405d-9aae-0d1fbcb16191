<template>
  <view class="have-bg-page">
    <view class="details-bg"></view>
    <NavBar :title="state.navTitle" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <image
          class="status-img"
          v-if="state.repairDetails?.status === 1"
          src="~@/static/images/repair-wait-dispose-of.png"
        ></image>
        <image
          class="status-img"
          v-else-if="state.repairDetails?.status === 2"
          src="~@/static/images/repair-revoke.png"
        ></image>
        <image
          class="status-img"
          v-else-if="state.repairDetails?.status === 3"
          src="~@/static/images/repair-under-disposal.png"
        ></image>
        <image
          class="status-img"
          v-else-if="state.repairDetails?.status === 4"
          src="~@/static/images/repair-wait-check.png"
        ></image>
        <image
          class="status-img"
          v-else-if="state.repairDetails?.status === 5"
          src="~@/static/images/repair-finish.png"
        ></image>

        <view class="row first">
          <text class="label">工单编号</text>
          <text class="value">{{ state.repairDetails?.serialNumber }}</text>
        </view>
        <view class="row">
          <text class="label">提报单位</text>
          <text class="value">{{ state.repairDetails?.deptName }}</text>
        </view>
        <view class="separation-line">
          <text class="circle"></text>
          <text class="line"></text>
          <text class="circle"></text>
        </view>
        <view class="row first">
          <text class="label">处置状态</text>
          <text class="value">
            {{ disposalOptions.find(el => el.value == state.repairDetails?.status)?.label || '--' }}
          </text>
        </view>
        <view class="row">
          <text class="label">应急类型</text>
          <text class="value">
            {{ emergencyTypeOptions.find(el => el.value == state.repairDetails?.emergencyType)?.label || '--' }}
          </text>
        </view>
        <view class="row">
          <text class="label">提报时间</text>
          <text class="value">{{ state.repairDetails?.createdTime || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">应急部位</text>
          <text class="value">
            {{
              state.repairDetails?.objectCategoryCode == 'HP'
                ? '水利工程'
                : state.repairDetails?.objectCategoryCode == 'RL'
                ? '水系'
                : '监测站点'
            }}
          </text>
        </view>
        <view class="row">
          <text class="label">所属工程/水系/站点</text>
          <text class="value">{{ props?.objectNames || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">问题描述</text>
          <text class="value">{{ state.repairDetails?.content || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">备注</text>
          <text class="value">{{ state.repairDetails?.remark || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">附件</text>
          <text class="value" v-if="state.repairDetails?.positionAttaches == null">{{ '--' }}</text>
          <view class="img-box">
            <u-image
              class="img"
              :src="el.attachUrl"
              v-for="(el, i) in state.repairDetails?.positionAttaches"
              :key="i"
            ></u-image>
          </view>
        </view>
      </view>

      <view
        class="com-card"
        v-if="
          state.repairDetails?.status === 3 || state.repairDetails?.status === 4 || state.repairDetails?.status === 5
        "
      >
        <view class="row">
          <text class="label">处置反馈</text>
          <text class="value">{{ state.repairDetails?.disposeFeedback || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">反馈时间</text>
          <text class="value">{{ state.repairDetails?.disposeDate || '--' }}</text>
        </view>
      </view>

      <view class="com-card" v-if="state.repairDetails?.status === 4 || state.repairDetails?.status === 5">
        <view class="row">
          <text class="label">处置详情</text>
          <text class="value">{{ state.repairDetails?.disposeContent || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">处置完成时间</text>
          <text class="value">{{ state.repairDetails?.disposeFinishDate || '--' }}</text>
        </view>
      </view>
      <view class="com-card" v-if="state.repairDetails?.status === 5">
        <view class="row">
          <text class="label">现场情况</text>
          <text class="value">
            {{ state.repairDetails?.state == 1 ? '已解决' : state.repairDetails?.state == 2 ? '未解决' : '--' }}
          </text>
        </view>
        <view class="row">
          <text class="label">备注</text>
          <text class="value">{{ state.repairDetails?.remarks || '--' }}</text>
        </view>
        <view class="row">
          <text class="label">附件</text>
          <text class="value" v-if="state.repairDetails?.checkAttaches == null">{{ '--' }}</text>
          <view class="img-box">
            <u-image
              class="img"
              :src="el.attachUrl"
              v-for="(el, i) in state.repairDetails?.checkAttaches"
              :key="i"
            ></u-image>
          </view>
        </view>
      </view>
      <!--form-->
      <u-form :model="state.form" ref="formRef" label-width="150" :error-type="['border-bottom', 'toast']">
        <view class="com-card" v-if="props?.source == 'startDisposal'">
          <u-form-item label="处置反馈" prop="disposeFeedback" required>
            <u-input class="textarea" v-model="state.form.disposeFeedback" placeholder="请输入" />
          </u-form-item>
        </view>
        <view class="btn-group" v-if="props?.source == 'startDisposal'">
          <u-button class="btn ignore-btn" @click="ignore">忽略问题</u-button>
          <u-button class="btn" @click="disposeOf">开始处置</u-button>
        </view>

        <view class="com-card" v-if="props?.source == 'finishDisposal'">
          <u-form-item label="处置详情" prop="disposeContent" required>
            <u-input class="textarea" v-model="state.form.disposeContent" placeholder="请输入" />
          </u-form-item>
        </view>
        <view class="btn-group" v-if="props?.source == 'finishDisposal'">
          <u-button class="btn" @click="finishDisposalSubmit">处置完成</u-button>
        </view>

        <view class="com-card" v-if="props?.source == 'toReview'">
          <view>现场复核</view>

          <u-form-item class="part-form" label="现场情况">
            <u-radio-group v-model="state.form.state">
              <u-radio v-for="(item, index) in sceneOptions" :key="index" :name="item.value">
                <text class="radio-text" :class="{ activeRadio: item.value == state.form.state }">
                  {{ item.name }}
                </text>
              </u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item label="备注">
            <u-input class="textarea remark" v-model="state.form.remarks" placeholder="请输入" />
          </u-form-item>
          <u-form-item label="附件">
            <MyUpload
              :urls="state.form.positionAttaches"
              @update:urls="
                urls => {
                  state.form.positionAttaches = urls
                }
              "
              folderName="emergency-repair"
            ></MyUpload>
          </u-form-item>
        </view>
        <view class="btn-group" v-if="props?.source == 'toReview'">
          <u-button class="btn" @click="checkSubmit">提交复核结果</u-button>
        </view>
      </u-form>
    </scroll-view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import {
    getEmergencyRepairDetails,
    disposeEmergencyRepair,
    disposalCompletedRepair,
    loseEmergencyRepair,
    checkEmergencyRepair,
  } from '../services'
  import MyUpload from '@/components/MyUpload/index.vue'

  const disposalOptions = [
    { label: '待处置', value: 1 },
    { label: '已撤回', value: 2 },
    { label: '处置中', value: 3 },
    { label: '待复核', value: 4 },
    { label: '已完成', value: 5 },
  ]
  const emergencyTypeOptions = [
    { label: '隐患问题', value: 1 },
    { label: '异常情况', value: 2 },
  ]
  const sceneOptions = [
    { name: '已解决', value: 1 },
    { name: '未解决', value: 2 },
  ]
  const router = useRouter()

  const uNotifyRef = ref()
  const formRef = ref(null)

  const props = defineProps(['repairId', 'objectNames', 'source'])

  const state = reactive({
    navTitle: '详情',
    repairDetails: {},
    form: {
      disposeFeedback: '',
      disposeContent: '',
      state: 1,
      remarks: '',
      positionAttaches: [],
    },
    rules: {
      disposeFeedback: [{ required: true, message: '处置反馈不能为空' }],
      disposeContent: [{ required: true, message: '处置详情不能为空' }],
    },
  })

  onMounted(() => {
    getDetails()
    formRef.value.setRules(state.rules)
    state.navTitle =
      props?.source === 'startDisposal'
        ? '开始处置'
        : props?.source === 'finishDisposal'
        ? '结束处置'
        : props?.source === 'toReview'
        ? '复核'
        : '详情'
  })

  const getDetails = () => {
    getEmergencyRepairDetails({
      repairId: props?.repairId,
    }).then(res => {
      state.repairDetails = res?.data
    })
  }
  //忽略问题
  const ignore = () => {
    formRef.value.validate(valid => {
      if (valid) {
        loseEmergencyRepair({ repairId: props?.repairId, disposeFeedback: state.form.disposeFeedback }).then(res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '忽略问题成功',
            duration: 800,
          })
        })
        toRepair()
      }
    })
  }
  const disposeOf = () => {
    formRef.value.validate(valid => {
      if (valid) {
        disposeEmergencyRepair({ repairId: props?.repairId, disposeFeedback: state.form.disposeFeedback }).then(res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '开始处置成功',
            duration: 800,
          })
        })
        toRepair()
      }
    })
  }
  //处置完成
  const finishDisposalSubmit = () => {
    formRef.value.validate(valid => {
      if (valid) {
        disposalCompletedRepair({ repairId: props?.repairId, disposeContent: state.form.disposeContent }).then(res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '处置完成成功',
            duration: 800,
          })
        })
        toRepair()
      }
    })
  }
  const checkSubmit = () => {
    const params = {
      positionAttaches: state.form.positionAttaches,
      remarks: state.form.remarks,
      repairId: props?.repairId,
      state: state.form.state,
    }
    checkEmergencyRepair(params).then(res => {
      uNotifyRef.value.show({
        type: 'success',
        title: '复核成功',
        duration: 800,
      })
      toRepair()
    })
  }
  const toRepair = () => {
    setTimeout(() => {
      router.push({
        path: '/pages/emergency-repair/index',
      })
    }, 800)
  }
</script>

<style lang="scss" scoped>
  .have-bg-page {
    margin: -34rpx;
    background: #cdefff url('@/static/images/details-nav-bar-bg.png') no-repeat;
    background-size: 100%;
    padding: 40rpx;
    position: relative;
    .details-bg {
      position: absolute;
      top: 230rpx;
      left: 36rpx;
      width: 90%;
      height: 86%;
      border-radius: 16rpx;
      transform: rotate(-3deg);
      background: rgba(255, 255, 255, 0.3);
    }

    .separation-line {
      height: 30rpx;
      display: flex;
      margin: 0 -36rpx;
      .circle {
        width: 30rpx;
        height: 30rpx;
        border-radius: 30rpx;
        background: #cdefff;
      }
      .line {
        flex: 1;
        margin: 16rpx 10rpx;
        border-top: 1px dashed #e5e6eb;
      }
    }
  }
  .com-card {
    position: relative;
    padding-top: 16rpx;
    .status-img {
      position: absolute;
      right: 16rpx;
      width: 160rpx;
      height: 160rpx;
    }
    .first {
      padding-top: 30rpx;
    }
  }
  .row {
    margin-bottom: 26rpx;
    display: flex;
    .label {
      font-weight: 400;
      font-size: 28rpx;
      color: #4e5969;
      width: 80px;
    }
    .value {
      font-weight: 400;
      font-size: 30rpx;
      color: #1d2129;
      flex: 1;
    }
    .img-box {
      flex: 1;
      display: flex;
      .img,
      :deep(.u-image__image) {
        width: 120rpx !important;
        height: 120rpx !important;
        margin-right: 20rpx;
      }
    }
  }
  .part-form {
    :deep(.u-form-item__body) {
      display: block;
    }
    :deep(.u-radio__icon-wrap) {
      visibility: hidden;
    }
    .radio-text {
      height: 64rpx;
      border-radius: 8rpx;
      color: #3d3d3d;
      font-size: 30rpx;
      padding: 4rpx 18rpx;
      background: #f2f3f5;
      margin-left: -42rpx;
    }
    .activeRadio {
      color: #fff;
      background: #165dff;
    }
  }
  .textarea {
    height: 268rpx;
    border-radius: 8rpx;
    background: #f2f3f5;
    border: 1px solid #e5e6eb;
    &.remark {
      height: 120rpx;
    }
  }
  .btn-group {
    display: flex;
    .btn {
      width: 330rpx;
      height: 88rpx;
      font-size: 30rpx;
      border-radius: 16rpx;
      color: #fff;
      background: #3772ff;
      &::after {
        border: none;
      }
    }
    .ignore-btn {
      color: #165dff;
      background: #f7f8fa;
    }
  }
</style>
