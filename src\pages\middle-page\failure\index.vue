<template>
  <view class="edit-failure">
    <view class="content">
      <view class="failure"></view>
      <text class="text">{{ props.title }}</text>
      <view class="text-[#86909C] text-center">
        {{ props.desc || '' }}
        <text class="text-[#F76560]">{{ props.errText }}</text>
      </view>
    </view>

    <u-button class="submit-btn" type="primary" size="default" @click="onBtnClick">{{ props.btnText }}</u-button>

    <view class="text-[#4E5969] text-center mt-[40rpx]">
      {{ props.otherJumpText }}
      <text class="text-[#165DFF]" @click="onOtherJump">立即返回</text>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'

  const userStore = useUserStore()
  const user = userStore.user
  const router = useRouter()

  const props = defineProps({
    title: { default: 'XX完成' },
    desc: `XXXX成功，XXXXXX:XXXX`,
    errText: 'xxx',
    btnText: '立即返回',
    jumpType: 'back',
    url: 1,
    otherJumpText: '回到库存盘点列表,',
    otherUrl: 2
  })

  const onBtnClick = () => {
    if (Number.isInteger(+props.url)) {
      uni.navigateBack({ delta: +props.url })
    } else {
      router[props.jumpType](props.url)
    }
  }

  const onOtherJump = () => {
    if (Number.isInteger(+props.otherUrl)) {
      uni.navigateBack({ delta: +props.otherUrl })
    } else {
      router[props.jumpType](props.otherUrl)
    }
  }
</script>

<style lang="scss" scoped>
  .edit-failure {
    background: url('~@/static/images/details-top-bg.png') no-repeat;
    background-size: 100%;
    height: 100%;
  }

  .content {
    padding: 250rpx 40rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .failure {
      width: 200rpx;
      height: 200rpx;
      background: url('@/static/images/status-failure-bg.png') no-repeat;
      background-size: 100% 100%;
    }

    .text {
      margin-top: 20rpx;
      font-size: 44rpx;
      font-weight: 600;
      font-family: PingFang SC-Medium, PingFang SC;
      color: #1d2129;
      line-height: 80rpx;
    }

    .back {
      font-size: 28rpx;
      font-weight: 300;
      color: #4e5969;
      line-height: 52rpx;
    }
  }

  .submit-btn {
    margin-top: 60rpx;
    height: 116rpx;
    text-align: center;
    width: 284rpx;
    border-radius: 16rpx;

    :deep(.u-button__text) {
      font-family: PingFang SC-Medium, PingFang SC;
    }
  }
</style>
