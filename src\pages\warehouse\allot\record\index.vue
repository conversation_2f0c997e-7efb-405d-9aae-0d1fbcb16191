<template>
  <view class="page-nav-top-common">
    <NavBar title="调拨记录" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="info-content">
      <view class="header">
        <text class="header-name">调拨记录: {{ props.allotCount || '-' }}</text>

        <u-tag
          size="mini"
          style="font-size: 26rpx"
          border-color="transparent"
          :text="props.status == 1 ? '启用' : '停用'"
          :type="props.status == 1 ? 'success' : 'error'"
        />
      </view>

      <view class="content">
        <view class="content-title">
          <view class="title text-overflow1 w-full">
            {{ props.warehouseName || '-' }}
          </view>
        </view>

        <view class="indicate-item text-overflow1 w-full">所属组织: {{ props.organizationName || '-' }}</view>

        <view class="grid grid-cols-2 mt-[20rpx] gap-[20rpx]">
          <view class="bg-[#F7F8FA] rounded-[8rpx] px-[16rpx] py-[6rpx] flex item-center">
            <text class="text-[13px] text-[#86909C]">仓管员&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ props.stockmanName || '-' }}</text>
          </view>
          <view class="bg-[#F7F8FA] rounded-[8rpx] px-[16rpx] py-[6rpx] flex item-center">
            <view class="text-[13px] text-[#86909C] text-overflow1 w-[115rpx]">仓库编码&nbsp;</view>
            <view class="text-[13px] text-[#1D2129] text-overflow1 max-h-[36rpx]">
              {{ props.warehouseCode || '-' }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <u-section
    title="调拨记录"
    class="mt-[30rpx] ml-[30rpx]"
    font-size="34"
    line-color="#3772FF"
    :right="false"
  ></u-section>

  <view class="relative">
    <u-dropdown
      ref="dropdownRef"
      height="60"
      title-size="28"
      menu-icon="arrow-down-fill"
      menu-icon-size="20"
      style="margin: 0 0 20rpx"
      @open="onDropdownOpen"
    >
      <u-dropdown-item
        height="600"
        v-model="state.warehouseVal.value"
        :title="state.warehouseVal.title"
        :options="state.warehouseOptions"
        @change="onWarehouseValChange"
      ></u-dropdown-item>

      <u-dropdown-item
        height="600"
        v-model="state.manVal.value"
        :title="state.manVal.title"
        :options="state.manList"
        @change="onManValChange"
      ></u-dropdown-item>

      <u-dropdown-item :title="state.timePicker.title"></u-dropdown-item>
    </u-dropdown>
  </view>

  <view class="bg-[#ffffff]">
    <ZPaging ref="pagingRef" style="margin-top: calc(44px + 510rpx); z-index: 10" v-model="state.list" @query="getList">
      <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
        <view class="content-title">
          <view class="title">{{ el.allotNo || '-' }}</view>
        </view>

        <view class="indicate-item">
          <text class="text-[#86909C]">调入仓库:&nbsp;</text>
          {{ el.inboundWarehouseName || '-' }}
        </view>
        <view class="indicate-item">
          <text class="text-[#86909C]">调拨人:&nbsp;</text>
          {{ el.assignerName || '-' }}
        </view>
        <view class="indicate-item">
          <text class="text-[#86909C]">调拨时间:&nbsp;</text>
          {{ el.allotTime || '-' }}
        </view>
        <view class="indicate-item">
          <text class="text-[#86909C]">调拔摘要:&nbsp;</text>
          {{ el.remark || '-' }}
        </view>
      </view>
    </ZPaging>
  </view>

  <u-picker
    v-model="state.timePicker.showPicker"
    :default-time="state.timePicker.defaultTime"
    mode="time"
    :params="{ year: true, month: true, day: true, hour: false, minute: false, second: false }"
    cancel-text="重置"
    @confirm="onTimeConfirm"
    @cancel="onTimeCancel"
    @close="onTimeClose"
  ></u-picker>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { getConfigPage, getAllotManList, allotPage } from '../../services'
  import dayjs from 'dayjs'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)

  const props = defineProps([
    'allotCount',
    'organization',
    'organizationName',
    'status',
    'stockmanId',
    'stockmanName',
    'warehouseCode',
    'warehouseId',
    'warehouseName'
  ])

  const state = reactive({
    warehouseOptions: [],
    warehouseVal: { value: undefined, title: '调拨仓库', preVal: undefined },
    manList: [],
    manVal: { value: undefined, title: '调拨人', preVal: undefined },
    timePicker: {
      showPicker: false,
      defaultTime: dayjs().format('YYYY-MM-DD'),
      title: '调拨日期',
      startTime: undefined,
      endTime: undefined
    },
    list: []
  })

  getConfigPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.warehouseOptions = (res.data?.data || []).map(el => ({
      ...el,
      label: el.warehouseName,
      value: el.warehouseId
    }))
  })

  getAllotManList().then(res => {
    state.manList = (res.data || []).map(el => ({ ...el, label: el.checkManName, value: el.checkManId }))
  })

  const getList = (pageNo, pageSize) => {
    allotPage({
      warehouseId: state.warehouseVal.value,
      assignerId: state.manVal.value,
      startTime: state.timePicker.startTime,
      endTime: state.timePicker.endTime,
      pageNum: pageNo || 1,
      pageSize: 10
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  function onWarehouseValChange(value) {
    if (value === state.warehouseVal.preVal) {
      state.warehouseVal = { title: '调拨仓库', value: undefined, preVal: undefined }
    } else {
      state.warehouseVal = {
        title: state.warehouseOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value
      }
    }

    pagingRef.value.reload()
  }

  function onManValChange(value) {
    if (value === state.manVal.preVal) {
      state.manVal = { title: '调拨人', value: undefined, preVal: undefined }
    } else {
      state.manVal = {
        title: state.manList.find(el => el.value == value)?.label,
        value: value,
        preVal: value
      }
    }
    pagingRef.value.reload()
  }

  const onDropdownOpen = index => {
    if (index === 2) {
      nextTick(() => {
        state.timePicker.showPicker = true
      })
    }
  }
  const onTimeConfirm = p => {
    state.timePicker = {
      ...state.timePicker,
      startTime: dayjs(`${p.year}-${p.month}-${p.day}`).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(`${p.year}-${p.month}-${p.day}`).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      title: `${p.year}-${p.month}-${p.day}`
    }

    pagingRef.value.reload()
    dropdownRef.value.close()
  }
  const onTimeCancel = () => {
    state.timePicker = {
      ...state.timePicker,
      startTime: undefined,
      endTime: undefined,
      title: '调拨日期'
    }
    pagingRef.value.reload()
    dropdownRef.value.close()
  }
  const onTimeClose = () => {
    dropdownRef.value.close()
  }

  const onItemClick = el => {
    router.push({ path: '/pages/warehouse/allot/detail/index', query: { allotId: el.allotId } })
  }
</script>

<style lang="scss" scoped>
  .info-content {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 20rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 32rpx;
      background: #e8f3ff;

      .header-name {
        font-size: 28rpx;
        color: #4e5969;
        line-height: 28rpx;
      }
    }

    .content {
      padding: 0 32rpx 32rpx;
      .content-title {
        font-size: 34rpx;
        color: #1d2129;
        font-weight: 600;
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        .title {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .indicate-item {
        font-size: 28rpx;
        color: #86909c;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }

  .item {
    margin: 20rpx 30rpx;
    padding: 10rpx 30rpx 14rpx;
    border-radius: 16rpx;
    background-color: #fff;

    .title {
      font-size: 36rpx;
      color: #1d2129;
      font-weight: 500;
      display: flex;
      align-items: center;
      padding: 6rpx 0;

      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .indicate-item {
      font-size: 28rpx;
      display: flex;
      align-items: center;
      margin-bottom: 4rpx;
      &:last-child {
        margin-bottom: 4rpx;
      }
    }
  }

  .add-btn {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: linear-gradient(44deg, #3772ff 13%, #5ddcf5 82%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
