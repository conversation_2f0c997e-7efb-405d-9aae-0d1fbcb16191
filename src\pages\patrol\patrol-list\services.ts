import { request } from '@/utils/request'

// 我的巡检任务
export function getMyTaskList(data: { taskStatus?: number; patrolType: any }) {
  return request({
    url: '/patrol/task/myTask/list',
    method: 'post',
    data
  })
}

//开始巡检
export function patrolStart(data: { taskId: string }) {
  return request({
    url: '/patrol/task/start',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
