import { request } from '@/utils/request'

// 我的任务详情
export function getTaskProfile(data: { taskId: string }) {
  return request({
    url: '/patrol/task/profile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

export function getContent(data: { taskId: string; objectId: string }) {
  return request({
    url: '/patrol/task/content/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 校验
export interface CheckType {
  items: any
  objectId: string
  taskId: string
}
export function check(data: CheckType) {
  return request({
    url: '/patrol/task/content/check',
    method: 'post',
    data
  })
}
//提交
export interface SubmitType {
  attaches: any
  items: any
  objectId: string
  taskId: string
}
export function submit(data: SubmitType) {
  return request({
    url: '/patrol/task/content/submit',
    method: 'post',
    data
  })
}
