<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="维养记录" :background="{ backgroundColor: 'transparent' }">
      <template #right>
        <text class="add-btn" @click="add">+</text>
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="relative">
      <u-dropdown
        ref="dropdownRef"
        height="80"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 0"
        @open="onDropdownOpen"
      >
        <u-dropdown-item :title="state.maintenanceUnit.title">
          <scroll-view class="drop-content" scroll-y show-scrollbar>
            <DaTree
              ref="maintenanceTreeRef"
              :data="state.deptTree"
              labelField="deptName"
              valueField="deptId"
              defaultExpandAll
              @change="handleMaintenanceTreeChange"
            ></DaTree>
          </scroll-view>
        </u-dropdown-item>

        <u-dropdown-item
          height="700"
          v-model="state.maintenanceStatus.value"
          :title="state.maintenanceStatus.title"
          :options="state.statusOptions"
          @change="onChangeMaintenanceStatus"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <ZPaging
    ref="pagingRef"
    :style="{ marginTop: `calc(44px + ${userStore.statusBarHeight}px + 100rpx)`, paddingTop: '28rpx', zIndex: 10 }"
    v-model="state.list"
    @query="getList"
  >
    <view class="com-card" v-for="(el, idx) in state.list" :key="idx">
      <view class="card-row top">
        <view
          class="status"
          :class="{ waiting: el?.status == 1, inProgress: el?.status == 2, finish: el?.status == 3 }"
        >
          {{ state.statusOptions?.find(ele => ele.value == el.status)?.label }}
        </view>
        <view class="number value">{{ el.serialNumber }}</view>
      </view>
      <view class="card-row">
        <text class="label">维养单位</text>
        <text class="value text-ellipsis">{{ el.deptName }}</text>
      </view>
      <view class="card-row">
        <text class="label">维养人员</text>
        <text class="value">{{ el.userNames }}</text>
      </view>
      <view class="card-row">
        <text class="label">维养范围</text>
        <text class="value text-ellipsis">{{ el.objectNames == null ? '--' : el.objectNames }}</text>
      </view>
      <view class="card-row">
        <text class="label">维养内容</text>
        <text class="value text-ellipsis">{{ el.content }}</text>
      </view>
      <view class="card-row">
        <text class="date">{{ el.maintenanceDate }}</text>
        <view class="btn-group">
          <u-button class="btn no-bg-btn" @click="handleDeleteMaintenance(el)">删除</u-button>
          <u-button class="btn details-btn" @click="onClickDetails(el)">详情</u-button>
        </view>
      </view>
    </view>
  </ZPaging>
  <ConfirmPopup
    :show="state.showConfirmDel"
    @update:show="
      () => {
        state.showConfirmDel = val
      }
    "
    @onConfirm="onConfirmDelete"
    popupTitle="删除"
    title="确认是否删除?"
    description=""
    type="warning"
  />
</template>

<script setup>
  import { reactive, ref, onMounted, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import DaTree from '@/components/da-tree/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import { getOptions, getTreeByLoginOrgId } from '@/api/common.ts'
  import { getMaintenanceRecord, deleteMaintenance } from './services'

  const router = useRouter()
  const userStore = useUserStore()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)
  const maintenanceTreeRef = ref(null)

  const state = reactive({
    deptTree: [],
    statusOptions: [],
    maintenanceUnit: { title: '维养单位', value: undefined },
    maintenanceStatus: { title: '维养状态', value: undefined, preVal: undefined },

    list: [],
    delId: undefined,
  })
  onMounted(() => {
    getOptions('maintenanceStatus').then(res => {
      state.statusOptions = (res?.data || []).map(el => ({ label: el.value, value: el.key }))
    })

    getTreeByLoginOrgId().then(res => {
      state.deptTree = res?.data
    })
  })
  const onDropdownOpen = () => {
    nextTick(() => {
      maintenanceTreeRef.value?.setCheckedKeys(state.maintenanceUnit.value, true)
    })
  }
  onShow(() => {
    getList(1)
  })

  const getList = (pageNo, pageSize) => {
    getMaintenanceRecord({
      deptId: state.maintenanceUnit.value,
      endTime: undefined,
      objectCategoryCode: undefined,
      objectId: undefined,
      serialNumber: undefined,
      startTime: undefined,
      status: state.maintenanceStatus.value,
      type: undefined,

      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  //新增
  const add = () => {
    router.push({
      path: '/pages/maintenance-record/add/index',
    })
  }

  //详情
  const onClickDetails = el => {
    router.push({
      path: '/pages/maintenance-record/details/index',
      query: {
        maintenanceId: el.id,
        userNames: el.userNames,
        objectNames: el.objectNames == null ? '--' : el.objectNames,
      },
    })
  }

  const handleDeleteMaintenance = el => {
    state.showConfirmDel = true
    state.delId = el?.id
  }
  const onConfirmDelete = () => {
    deleteMaintenance({ ids: state.delId }).then(res => {
      state.showConfirmDel = false
      uNotifyRef.value.show({
        type: 'success',
        title: '删除成功',
        duration: 800,
      })
    })

    pagingRef.value.reload()
  }

  function handleMaintenanceTreeChange(allSelectedKeys, currentItem) {
    if (state.maintenanceUnit.value === currentItem.key) {
      maintenanceTreeRef.value?.setCheckedKeys(currentItem.key, false)
      state.maintenanceUnit = { title: '维养单位', value: undefined }
    } else {
      state.maintenanceUnit = { title: currentItem.label, value: currentItem.key }
    }
    pagingRef.value.reload()
    dropdownRef.value.close()
  }
  function onChangeMaintenanceStatus(value) {
    if (value === state.maintenanceStatus.preVal) {
      state.maintenanceStatus = { title: '维养状态', value: undefined, preVal: undefined }
    } else {
      state.maintenanceStatus = {
        title: state.statusOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }
    pagingRef.value.reload()
  }
</script>

<style lang="scss" scoped>
  @import url(./maintenance.scss);
</style>
