<template>
  <view class="page-nav-top-common">
    <NavBar title="新增" :background="{ backgroundColor: '#fff' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="100"></u-top-tips>

    <scroll-view class="container" scroll-y>
      <u-form :model="state.form" ref="formRef" label-width="130" :error-type="['border-bottom', 'toast']">
        <view class="com-card">
          <u-form-item class="text-form" label="调度日期" prop="dispatchDate" required>
            <u-input
              v-model="state.form.dispatchDate"
              type="select"
              @click="state.showDispatchDate = true"
              placeholder="请选择"
            />
          </u-form-item>

          <u-form-item class="part-form" label="调度类型" prop="dispatchType" required>
            <u-radio-group v-model="state.form.dispatchType">
              <u-radio
                @change="changeDispatchType"
                v-for="(item, index) in dispatchTypeOptions"
                :key="index"
                :name="item.value"
              >
                <text class="radio-text" :class="{ activeRadio: item.value == state.form.dispatchType }">
                  {{ item.label }}
                </text>
              </u-radio>
            </u-radio-group>
          </u-form-item>
        </view>

        <view class="com-card">
          <view class="open-device">
            <view class="device-text-group">
              <view class="text">开启设备</view>
              <view class="open-row" v-for="(el, i) in state.duration" :key="i">
                <u-checkbox v-model="el.disabled">{{ el.name }}</u-checkbox>
              </view>
            </view>
            <view class="open-duration-group" v-if="state.form.dispatchType == 1">
              <view class="text">开启时长(小时)</view>
              <view class="open-row" v-for="(el, i) in state.duration" :key="i">
                <u-number-box
                  :positive-integer="false"
                  :input-width="120"
                  v-model="el.number"
                  :min="0"
                  :max="24"
                  input-height="64"
                  :disabled="!el.disabled"
                ></u-number-box>
              </view>
            </view>

            <view class="open-duration-group" v-if="state.form.dispatchType == 2">
              <view class="text">开启时长(小时)</view>
              <view class="input-bg" v-for="(el, i) in state.duration" :key="i">
                <u-input
                  v-model="el.range.startTime"
                  type="text"
                  placeholder="开始时间"
                  :disabled="true"
                  @click="onChangeTime(el, i, 'start')"
                />
                <text class="input-line" @click="onChangeTime(el, i, 'start')"></text>
                <u-input
                  v-model="el.range.endTime"
                  type="text"
                  placeholder="结束时间"
                  :disabled="true"
                  @click="onChangeTime(el, i, 'end')"
                />
              </view>
            </view>
          </view>

          <u-form-item label="备注" class="textarea-form">
            <u-input class="textarea" v-model="state.form.remark" placeholder="请输入" />
          </u-form-item>
        </view>
      </u-form>
    </scroll-view>

    <u-button
      class="btn-primary"
      :disabled="state.form.dispatchDate?.length == 0 && !state.form.content"
      type="primary"
      @click="submitBtn"
    >
      提交
    </u-button>
  </view>

  <u-calendar
    :max-date="dayjs().add(100, 'year').format('YYYY-MM-DD')"
    v-if="state.showDispatchDate"
    v-model="state.showDispatchDate"
    mode="date"
    @change="onChangeDate"
  ></u-calendar>

  <u-picker mode="time" v-model="state.showTime" :params="params" @confirm="timeConfirm"></u-picker>

  <ConfirmPopup
    :show="state.showConfirm"
    @update:show="
      () => {
        state.showConfirm = val
      }
    "
    @onConfirm="onConfirmSubmit"
    popupTitle="确认提交调度单？"
    title="确认后将不能修改调度单内容。"
    description=""
    type="warning"
  />
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { addHydropowerDispatch } from '../services'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import dayjs from 'dayjs'

  const dispatchTypeOptions = [
    { label: '日常调度', value: 1 },
    { label: '临时调度', value: 2 },
  ]
  const params = {
    hour: true,
    minute: true,
  }

  const router = useRouter()
  const props = defineProps(['taskId'])

  const uNotifyRef = ref()
  const formRef = ref(null)

  const state = reactive({
    showDispatchDate: false,

    showConfirm: false,

    duration: [
      {
        number: 0,
        disabled: false,
        name: '1#发电机',
        sort: 1,
        range: {
          startTime: undefined,
          endTime: undefined,
        },
      },
      {
        number: 0,
        disabled: false,
        name: '2#发电机',
        sort: 2,
        range: {
          startTime: undefined,
          endTime: undefined,
        },
      },
      {
        number: 0,
        disabled: false,
        name: '3#发电机',
        sort: 3,
        range: {
          startTime: undefined,
          endTime: undefined,
        },
      },
    ],

    showTime: false,
    timeIndex: 0,
    timeType: '',

    form: {
      detailsAdds: [],
      dispatchDate: dayjs().format('YYYY-MM-DD'),
      dispatchType: 1,
      remark: undefined,
    },
    rules: {
      dispatchDate: [{ required: true, message: '调度日期不能为空' }],
      dispatchType: [{ required: true, message: '调度类型不能为空' }],
    },
  })

  onMounted(() => {
    formRef.value.setRules(state.rules)
  })

  //
  const changeDispatchType = val => {
    state.duration.forEach(el => {
      el.disabled = false
      el.range.startTime = undefined
      el.range.endTime = undefined
      el.number = 0
    })

    state.form.dispatchType = val
  }
  const timeConfirm = e => {
    if (state.timeType === 'start') {
      state.duration[state.timeIndex].range.startTime = e.hour + ':' + e.minute
    } else if (state.timeType === 'end') {
      state.duration[state.timeIndex].range.endTime = e.hour + ':' + e.minute
    }
  }

  const onChangeDate = e => {
    state.form.dispatchDate = e.result
  }

  //
  const onChangeTime = (row, i, type) => {
    if (row.disabled) {
      state.showTime = true
      state.timeIndex = i
      state.timeType = type
    } else {
      uNotifyRef.value.show({
        type: 'warning',
        title: '请勾选设备',
        duration: 600,
      })
    }
  }

  const submitBtn = () => {
    formRef.value.validate(valid => {
      if (valid) {
        state.form.detailsAdds = []
        if (state.form.dispatchType == 1) {
          if (state.duration.every(el => el.number === null || el.number === 0)) {
            uNotifyRef.value.show({
              type: 'warning',
              title: '至少开启一台设备',
              duration: 2000,
            })
            return
          }
          state.duration.forEach(el => {
            if (el.number) {
              state.form.detailsAdds.push({
                hour: el.number,
                sort: el.sort,
              })
            }
          })
        } else if (state.form.dispatchType == 2) {
          if (state.duration.every(el => el?.range?.startTime === undefined || el?.range?.endTime === undefined)) {
            uNotifyRef.value.show({
              type: 'warning',
              title: '至少开启一台设备',
              duration: 2000,
            })
            return
          }
          for (let i = 0; i < state.duration.length; i++) {
            if (
              state.duration[i]?.range?.startTime != undefined &&
              state.duration[i]?.range?.endTime != undefined &&
              state.duration[i]?.range?.startTime > state.duration[i]?.range?.endTime
            ) {
              uNotifyRef.value.show({
                type: 'warning',
                title: '结束时间不能小于开始时间',
                duration: 2000,
              })
              return
            }
          }

          state.duration.forEach(el => {
            if (el?.range?.startTime && el?.range?.endTime) {
              state.form.detailsAdds.push({
                startTime: el?.range?.startTime,
                endTime: el?.range?.endTime,
                sort: el.sort,
              })
            }
          })
        }
        state.showConfirm = true
      }
    })
  }

  const onConfirmSubmit = () => {
    addHydropowerDispatch(state.form).then(res => {
      uNotifyRef.value.show({
        type: 'success',
        title: '成功',
        duration: 800,
      })
      setTimeout(() => {
        router.push({
          path: '/pages/hydropower-dispatch/index',
        })
      }, 800)
    })
  }
</script>

<style lang="scss" scoped>
  .select-loading {
    position: fixed;
    left: 50%;
    top: 30%;
    z-index: 999;
  }
  .container {
    .open-device {
      display: flex;
      .text {
        color: #86909c;
        font-size: 30rpx;
        margin-bottom: 20rpx;
      }
      .device-text-group {
        width: 410rpx;
      }
      .open-row,
      .input-bg {
        height: 64rpx;
        margin-bottom: 20rpx;
      }
      .input-bg {
        background: #f2f3f5;
        display: flex;
        :deep(.u-input__input) {
          padding-left: 10rpx;
          min-height: 64rpx !important;
        }
        .input-line {
          width: 20rpx;
          height: 1px;
          margin-top: 30rpx;
          margin-right: 16rpx;
          background: #86909c;
        }
      }
    }
    .textarea-form {
      :deep(.u-form-item__body) {
        display: block;
      }
    }
    .textarea {
      height: 120rpx;
      border-radius: 8rpx;
      background: #f2f3f5;
      border: 1px solid #e5e6eb;
      :deep(.u-input__input) {
        padding-left: 6rpx;
      }
    }
  }
  .btn-primary {
    width: 90%;
  }
</style>
