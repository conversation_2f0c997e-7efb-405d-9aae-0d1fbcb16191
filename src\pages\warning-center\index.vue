<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="预警中心" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="search-input">
      <SvgIcon name="search" class="icon-search" />
      <u-input v-model="state.searchVal" :border="true" @update:model-value="onSearchChange" clearable />
    </view>

    <view class="relative">
      <u-dropdown
        ref="dropdownRef"
        height="60"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 20rpx 0"
        @open="onDropdownOpen"
      >
        <u-dropdown-item
          height="700"
          v-model="state.warningStatusVal.value"
          :title="state.warningStatusVal.title"
          :options="state.warningTypeOptions"
          @change="onWarningStatusValChange"
        ></u-dropdown-item>

        <u-dropdown-item :title="state.timePicker.title"></u-dropdown-item>

        <u-dropdown-item
          height="700"
          v-model="state.warningGroup.value"
          :title="state.warningGroup.title"
          :options="state.groupList"
          @change="onWarningGroupChange"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <ZPaging ref="pagingRef" style="margin-top: calc(44px + 250rpx); z-index: 10" v-model="state.list" @query="getList">
    <view class="item" v-for="(el, idx) in state.list" :key="idx">
      <view class="header">
        <text class="header-name">{{ el.groupName || '-' }}</text>

        <view>{{ el.eventNo }}</view>
      </view>

      <view class="content">
        <view class="content-title">
          <view class="title">
            <view
              class="dot"
              :style="{ background: state.warningTypeOptions.find(ele => ele.value == el.status)?.color }"
            ></view>
            {{ el.objectName || '-' }}
          </view>
        </view>

        <view>
          <TextReadMore
            fontSize="28rpx"
            :bthText="['展开详情', '收起']"
            :btnStyle="{ color: '#165DFF', background: 'transparent' }"
          >
            {{ el.message }}
          </TextReadMore>
        </view>

        <view class="bottom">
          <text class="time">{{ el.createdTime || '-' }}</text>

          <view class="history-btn" @click="onItemClick(el)">历史记录</view>
        </view>
      </view>
    </view>
  </ZPaging>

  <u-calendar
    :max-date="dayjs().add(100, 'year').format('YYYY-MM-DD')"
    :key="state.calendarKey"
    v-model="state.timePicker.showPicker"
    mode="range"
    @change="onDateRangeChange"
    @close="onTimeClose"
  >
    <template #tooltip>
      <view class="btn-reset" @click="onDateReset">重置</view>
    </template>
  </u-calendar>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import TextReadMore from '@/components/hbxw-text-folding/components/hbxw-text-folding/hbxw-text-folding.vue'
  import { getOptions } from '@/api/common.ts'
  import { getWarnEventLatestPage, getWarnGroupPage } from './services'
  import dayjs from 'dayjs'
  // import { outboundTypeOptions } from './config.ts'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)

  const state = reactive({
    searchVal: undefined,
    // 	状态(1-正在报警 2-已消除 3-已失效)
    warningTypeOptions: [
      { label: '正在报警', value: 1, color: '#F53F3F' },
      { label: '已消除', value: 2, color: '#00B42A' },
      { label: '已失效', value: 3, color: '#C9CDD4' },
    ],
    warningStatusVal: { value: undefined, title: '预警状态', preVal: undefined },
    groupList: [],
    warningGroup: { value: undefined, title: '预警分组', preVal: undefined },

    calendarKey: 1,
    timePicker: {
      showPicker: false,
      defaultTime: dayjs().format('YYYY-MM-DD'),
      title: '预警时间',
      startTime: undefined,
      endTime: undefined,
    },
    list: [],
  })

  onShow(() => {
    getList(1)
  })

  const params = {
    categoryId: 0,
    groupName: '',
    groupNo: '',
    objectType: null,
    warnLevel: null,
    pageNum: 1,
    pageSize: Number.MAX_SAFE_INTEGER,
  }

  getWarnGroupPage(params).then(res => {
    state.groupList = (res.data.data || []).map(el => ({ ...el, label: el.groupName, value: el.groupId }))
  })

  const getList = (pageNo, pageSize) => {
    getWarnEventLatestPage({
      objectName: state.searchVal,
      status: state.warningStatusVal.value,
      groupId: state.warningGroup.value,
      warnTimeLower: state.timePicker.startTime,
      warnTimeUpper: state.timePicker.endTime,
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  const onSearchChange = _.debounce(val => {
    nextTick(() => {
      getList(1)
    })
  }, 500)

  function onWarningStatusValChange(value) {
    if (value === state.warningStatusVal.preVal) {
      state.warningStatusVal = { title: '预警状态', value: undefined, preVal: undefined }
    } else {
      state.warningStatusVal = {
        title: state.warningTypeOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    pagingRef.value.reload()
  }

  function onWarningGroupChange(value) {
    if (value === state.warningGroup.preVal) {
      state.warningGroup = { title: '预警分组', value: undefined, preVal: undefined }
    } else {
      state.warningGroup = {
        title: state.groupList.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }
    pagingRef.value.reload()
  }

  const onDropdownOpen = index => {
    if (index === 1) {
      nextTick(() => {
        state.timePicker.showPicker = true
      })
    }
  }

  const onDateRangeChange = val => {
    state.timePicker = {
      ...state.timePicker,
      startTime: val.startDate,
      endTime: val.endDate,
    }

    pagingRef.value.reload()
    dropdownRef.value.close()
    dropdownRef.value.highlight(1)
  }

  const onDateReset = () => {
    state.calendarKey += 1
    state.timePicker = {
      ...state.timePicker,
      startTime: undefined,
      endTime: undefined,
    }

    pagingRef.value.reload()
    state.timePicker.showPicker = false
    dropdownRef.value.close()
    dropdownRef.value.highlight()
  }
  const onTimeClose = () => {
    dropdownRef.value.close()
  }

  const onItemClick = el => {
    router.push({
      path: '/pages/warning-center/record/index',
      query: {
        objectId: el.objectId,
        objectName: el.objectName,
        groupId: el.groupId,
        groupName: el.groupName,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .search-input {
    padding: 10rpx 34rpx 15rpx;
    position: relative;
    .icon-search {
      position: absolute;
      left: 60rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    :deep(.u-input.u-input--border) {
      padding-left: 70rpx !important;
      border-radius: 100rpx;
    }
  }
  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 32rpx;
      background: #e8f3ff;
      font-size: 28rpx;
      color: #4e5969;
      line-height: 28rpx;
    }

    .content {
      padding: 0 32rpx 32rpx;
      .content-title {
        font-size: 32rpx;
        color: #1d2129;
        display: flex;
        align-items: center;
        padding: 16rpx 0;
        .title {
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;

          .dot {
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8rpx;
          }
        }
      }

      .bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time {
          font-size: 24rpx;
          color: #86909c;
        }
        .history-btn {
          margin-top: 24rpx;
          padding: 16rpx 36rpx;
          background: #f7f8fa;
          border: 1px solid #e5e6eb;
          border-radius: 24rpx;
        }
      }
    }
  }

  .btn-reset {
    padding: 32rpx 12rpx 12rpx 32rpx;
  }
</style>
