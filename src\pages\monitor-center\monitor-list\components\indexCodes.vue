<template>
  <view class="indexCodes-box">
    <view
      class="indexCode"
      v-for="(item, index) in props.indexCodes || []"
      :key="index"
      :style="
        (!isOpen && props.canTuck) || index > props.indexCodes?.length - (props.indexCodes?.length % 2 == 1 ? 2 : 3)
          ? { borderColor: 'transparent' }
          : {}
      "
      v-show="!props.canTuck || isOpen || index < 2"
    >
      <view class="indicate">
        <text class="label">{{ props.indexCodeOptions.find(ele => ele.value == item.indexCode)?.label }}:</text>
        <text class="value">
          {{ item.indexCodeValue }}{{ props.indexCodeOptions.find(ele => ele.value == item.indexCode)?.unit }}
        </text>
      </view>
      <view class="time">{{ item.dateTime }}</view>
    </view>

    <view
      class="arrow w-[32rpx] h-[32rpx]"
      v-if="props.canTuck && props.indexCodes?.length > 2"
      @click.stop="onArrowClick"
    >
      <image v-if="isOpen" src="~@/static/icons/icon-arrow-tuck.svg" class="w-[32rpx] h-[32rpx]" />
      <image v-else src="~@/static/icons/icon-arrow-expend.svg" class="w-[32rpx] h-[32rpx]" />
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue'
  const props = defineProps({
    indexCodes: { default: [] },
    indexCodeOptions: { default: [] },
    canTuck: { default: true }
  })
  const isOpen = ref(false)

  const onArrowClick = () => {
    isOpen.value = !isOpen.value
  }
</script>

<style lang="scss" scoped>
  .indexCodes-box {
    display: flex;
    flex-wrap: wrap;
    position: relative;

    .arrow {
      position: absolute;
      right: 0;
      top: 90rpx;
    }

    .indexCode {
      margin-top: 30rpx;
      border-bottom: 2rpx solid #f2f3f5;
      width: 50%;
      .indicate {
        display: flex;
        align-items: center;
        .label {
          margin-right: 8rpx;
          font-size: 28rpx;
          color: #4e5969;
        }
        .value {
          font-size: 32rpx;
          font-family: DIN Alternate, DIN Alternate;
          font-weight: 700;
          color: #165dff;
        }
      }
      .time {
        margin: 14rpx 0 28rpx;
        font-size: 24rpx;
        color: #86909c;
      }
    }
  }
</style>
