<template>
  <view class="h-screen bg-[#ffffff]">
    <view class="page-nav-top-common-bg1-white">
      <NavBar title="值班表" :background="{ background: 'transparent' }"></NavBar>
      <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
      <view class="search-bar">
        <u-tabs
          class="tab-content"
          :list="tabsList"
          bg-color="transparent"
          active-color="#165DFF"
          v-model="state.currentTab"
          bar-width="80"
          @change="onTabChange"
        ></u-tabs>

        <u-dropdown
          v-if="state.groupList.length"
          ref="dropdownRef"
          height="80"
          title-size="28"
          menu-icon="arrow-down-fill"
          menu-icon-size="20"
          style="margin: 0"
        >
          <u-dropdown-item
            v-model="state.group.value"
            height="850"
            :title="state.group.title"
            :options="state.groupList"
            @change="onGroupChange"
          ></u-dropdown-item>
        </u-dropdown>
      </view>
    </view>
    <view class="search-wrapper" v-if="state.currentTab == 0 && state.groupList.length">
      <view class="month">{{ state.currentMonth.format('YYYY年M月') }}</view>
      <view class="week-btn">
        <view class="btn" :class="`${state.activeWeek === 'last' && 'text-[#165DFF]'}`" @click="onLastWeek()">
          上一周
        </view>
        <view class="btn" :class="`${state.activeWeek === 'now' && 'text-[#165DFF]'}`" @click="backWeek">本周</view>
        <view class="btn" :class="`${state.activeWeek === 'next' && 'text-[#165DFF]'}`" @click="onNextWeek()">
          下一周
        </view>
      </view>
    </view>

    <view class="rota-container" v-if="state.currentTab == 0 && state.groupList.length">
      <view class="r-row">
        <view class="user-name"></view>
        <view class="class-group">
          <view class="class-item" v-for="(week, index) in state.weekDate" :key="index">
            <view>{{ week.format('dd') }}</view>
            <view
              :class="`mx-[10rpx] font-[600] leading-[44rpx] ${
                week.format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD') ? 'current-day' : ''
              }`"
            >
              {{ week.format('DD') }}
            </view>
          </view>
        </view>
      </view>

      <scroll-view class="scroll-content" :scroll-y="true">
        <view
          class="r-row"
          :class="`${index % 2 === 0 ? 'bg-[#F7F8FA]' : ''}`"
          v-for="(item, index) in state.groupRotaList"
          :key="item.userId"
        >
          <view class="user-name">{{ item?.userName }}</view>
          <view class="class-group">
            <text
              class="class-item"
              v-for="child in item?.scheduleVOS"
              :key="child.scheduleId"
              :style="{ color: getColor(child), background: getColor(child)?.replace('1)', '0.1)') }"
            >
              {{ child.shiftName?.substring(0, 2) }}
            </text>
          </view>
        </view>
      </scroll-view>
    </view>
    <u-empty v-if="state.groupList?.length == 0" style="height: 800rpx" text="暂无数据" mode="data"></u-empty>
    <view class="calendar-container" v-if="state.groupList?.length && state.currentTab == 1">
      <WuCalendar
        startWeek="mon"
        :showMonth="false"
        :selected="state.myRotaList"
        color="#63A6FF"
        fold
        @monthSwitch="monthSwitch"
        @change="onCalendarChange"
      />

      <view class="detail">
        <view class="header">
          <view class="title">
            {{
              state.selectedDay?.workDate
                ? `${dayjs(state.selectedDay.workDate).format('DD')} ${dayjs(state.selectedDay.workDate).format(
                    'dddd',
                  )}`
                : ''
            }}
          </view>

          <view
            v-if="state.selectedDay?.scheduleId"
            class="text-center ml-[12rpx]"
            :style="{
              color: getColor(state.selectedDay),
              background: getColor(state.selectedDay)?.replace('1)', '0.1)'),
            }"
          >
            {{ state.selectedDay?.shiftName }}
          </view>
        </view>

        <view class="log-btn">
          <view class="btn-item mr-[30rpx]">
            值班日志
            <u-button
              v-if="state.selectedDay?.isLog == 0 || state.selectedDay?.isLog == null"
              style="justify-content: flex-end; margin: 0"
              type="primary"
              size="mini"
              @click.stop="onAddClick(1)"
            >
              去填写
            </u-button>
            <view v-else @click.stop="onDetailClick(1)">
              <text class="text-[#165DFF]">查看&nbsp;</text>
              <u-icon name="arrow-right"></u-icon>
            </view>
          </view>
          <view class="btn-item">
            调度日志
            <u-button
              v-if="state.selectedDay?.isSchedulingLog == 0 || state.selectedDay?.isSchedulingLog == null"
              style="justify-content: flex-end; margin: 0"
              type="primary"
              size="mini"
              @click.stop="onAddClick(2)"
            >
              去填写
            </u-button>
            <view v-else @click.stop="onDetailClick(2)">
              <text class="text-[#165DFF]">查看&nbsp;</text>
              <u-icon name="arrow-right"></u-icon>
            </view>
          </view>

          <view class="btn-item">
            水库值班日志
            <u-button
              v-if="state.selectedDay?.isReservoirLog == 0 || state.selectedDay?.isReservoirLog == null"
              style="justify-content: flex-end; margin: 0"
              type="primary"
              size="mini"
              @click.stop="onAddClick(3)"
            >
              去填写
            </u-button>
            <view v-else @click.stop="onDetailClick(3)">
              <text class="text-[#165DFF]">查看&nbsp;</text>
              <u-icon name="arrow-right"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { workSchedulePage, getDeptGroupList, workShiftPage } from '../services'
  import { useUserStore } from '@/store/modules/user'
  import dayjs from 'dayjs'
  import WuCalendar from '@/components/wu-calendar/wu-calendar/components/wu-calendar/wu-calendar.vue'

  const router = useRouter()
  const userStore = useUserStore()
  const uNotifyRef = ref()
  const tabsList = [{ name: '班组值班表' }, { name: '我的值班表' }]
  const colors = [
    'rgba(35, 195, 67, 1)',
    'rgba(64, 128, 255, 1)',
    'rgba(141, 78, 218, 1)',
    'rgba(247, 84, 168, 1)',
    'rgba(51, 209, 201, 1)',
    'rgba(159, 219, 29, 1)',
    'rgba(255, 154, 46, 1)',
    'rgba(247, 186, 30, 1)',
  ]

  const state = reactive({
    currentMonth: dayjs(),
    weekDate: [],
    activeWeek: 'now',
    currentTab: 0,

    allShiftList: [],

    calendarSelected: [],

    subVal: 0,
    list: [],
    groupList: [],
    prevSelectValue: undefined,
    group: { title: '班组', value: undefined },
    userId: undefined,
    startTime: dayjs().startOf('week'),
    endTime: dayjs().endOf('week'),
    groupRotaList: [],
    myRotaList: [],

    selectedDay: null,
  })

  onShow(() => {
    if (state.groupList?.length) {
      getList()
    } else {
      getRotaGroupList()
    }
  })

  onMounted(() => {
    getRotaGroupList()
    getSevenDay(dayjs())
  })
  //班次
  workShiftPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.allShiftList = (res.data?.data || []).reverse()
  })

  const getRotaGroupList = async () => {
    const res = await getDeptGroupList({ userId: state.userId })
    state.groupList = (res?.data || []).map(el => ({ ...el, label: el.groupName, value: el.groupId }))
    if (state.groupList.length > 0) {
      state.group = { title: state.groupList[0].label, value: state.groupList[0].value }
      getList()
    } else {
      state.group = { title: '班组', value: undefined }
      state.groupRotaList = []
      state.myRotaList = []
    }
  }

  const getList = async () => {
    const res = await workSchedulePage({
      groupId: state.group.value,
      userId: state.userId,
      startTime: state.startTime.format('YYYY-MM-DD'),
      endTime: state.endTime.format('YYYY-MM-DD'),
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
    })
    if (state.currentTab == 0) {
      state.groupRotaList = res?.data?.data || []
    } else if (state.currentTab == 1) {
      state.myRotaList = (res?.data?.data?.[0]?.scheduleVOS || []).map(el => ({
        ...el,
        date: el.workDate,
        topInfo: state.allShiftList.find(ele => ele.shiftId === el.shiftId)?.shiftName.slice(0, 2) || '　',
        topInfoColor: colors[state.allShiftList.findIndex(ele => ele.shiftId === el?.shiftId)],
        info: el.scheduleId || el.isLog ? '•' : '　',
        infoColor: el.isLog ? '#165DFF' : '#F76560',
      }))

      if (!state.selectedDay) {
        state.selectedDay = state.myRotaList.find(el => el.workDate === dayjs().format('YYYY-MM-DD'))
      } else {
        state.selectedDay = state.myRotaList.find(el => el.workDate == state.selectedDay.workDate)
      }
    }
  }

  const getColor = child => {
    return colors[state.allShiftList.findIndex(ele => ele.shiftId === child.shiftId)]
  }

  const onTabChange = index => {
    state.currentTab = index
    if (index == 0) {
      state.userId = undefined
      state.startTime = dayjs().startOf('week')
      state.endTime = dayjs().endOf('week')
      getRotaGroupList()
    } else if (index == 1) {
      state.prevSelectValue = undefined
      state.userId = userStore.user.userId
      state.startTime = dayjs().startOf('month')
      state.endTime = dayjs().endOf('month')
      getRotaGroupList()
    }
  }

  const onGroupChange = value => {
    if (value === state.prevSelectValue) {
      state.group = { title: '班组', value: undefined }
      state.prevSelectValue = undefined
    } else {
      state.group = { title: state.groupList.find(el => el.value == value)?.label, value: value }
      state.prevSelectValue = value
    }

    getList(1)
  }

  const onLastWeek = () => {
    state.activeWeek = 'last'
    state.startTime = state.startTime.subtract(1, 'week')
    state.endTime = state.endTime.subtract(1, 'week')
    getSevenDay(dayjs(state.startTime))

    getList()
  }
  const onNextWeek = () => {
    state.activeWeek = 'next'
    state.startTime = state.startTime.add(1, 'week')
    state.endTime = state.endTime.add(1, 'week')

    getSevenDay(dayjs(state.startTime))
    getList()
  }
  const backWeek = () => {
    state.activeWeek = 'now'
    state.startTime = dayjs().startOf('week')
    state.endTime = dayjs().endOf('week')
    getSevenDay(dayjs(state.startTime))

    nextTick(() => {
      getList()
    })
  }
  const getSevenDay = startDay => {
    let sevenNum = []
    state.currentMonth = startDay
    ;[...new Array(7).keys()].map(el => {
      sevenNum.push(startDay.startOf('week').add(el, 'day'))
    })

    state.weekDate = sevenNum
  }

  //我的值班表
  const monthSwitch = ({ year, month }) => {
    state.startTime = dayjs(`${year}-${month}`).startOf('month')
    state.endTime = dayjs(`${year}-${month}`).endOf('month')
    getList()
  }

  const onCalendarChange = param => {
    state.selectedDay = param.extraInfo
  }

  const onAddClick = logType => {
    router.push({
      path: '/pages/duty-management/rota-form/index',
      query: {
        logType,
        logDate: state.selectedDay.workDate,
        shiftId: state.selectedDay.shiftId,
        groupId: state.group.value,
        groupName: state.group.title,
      },
    })
    state.selectedDay = state.myRotaList.find(el => el.workDate === dayjs().format('YYYY-MM-DD'))
  }

  const onDetailClick = logType => {
    router.push({
      path: '/pages/duty-management/rota-detail/index',
      query: {
        logType,
        logDate: state.selectedDay.workDate,
        shiftId: state.selectedDay.shiftId,
        groupId: state.group.value,
        groupName: state.group.title,
      },
    })
    state.selectedDay = state.myRotaList.find(el => el.workDate === dayjs().format('YYYY-MM-DD'))
  }
</script>

<style lang="scss" scoped>
  .search-bar {
    position: relative;
    height: 80rpx;
    display: flex;
    align-items: center;

    .tab-content {
      position: absolute;
      margin-top: 6rpx;
      width: 510rpx;
      left: 0rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 12;
    }

    :deep(.u-dropdown__menu) {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .u-dropdown__menu__item {
        flex: unset;
        margin-right: 32rpx;
      }
    }
  }

  .search-wrapper {
    padding: 40rpx 32rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .month {
      color: #1d2129;
      font-size: 36rpx;
      font-weight: 600;
    }
    .week-btn {
      display: flex;
      .btn {
        margin-left: 10rpx;
        padding: 8rpx 20rpx;
        background: #f7f8fa;
      }
    }
  }

  .rota-container {
    height: calc(100vh - 44px - 270rpx);

    .r-row {
      display: flex;
      padding: 20rpx;
      border-bottom: 1rpx solid #f2f3f5;
      .user-name {
        width: 115rpx;
      }
      .class-item {
        width: 66rpx;
        text-align: center;
        display: inline-block;
        margin-left: 16rpx;
        .current-day {
          background: #165dff;
          border-radius: 50%;
          color: #ffffff;
        }
      }
    }

    .scroll-content {
      height: calc(100vh - 44px - 400rpx);
    }
  }

  .calendar-container {
    height: calc(100vh - 44px - 150rpx);

    .detail {
      margin: 30rpx;

      .header {
        display: flex;
        align-items: center;
        .title {
          font-size: 32rpx;
          font-weight: 600;
          color: #3d3d3d;
        }
      }
      .log-btn {
        margin-top: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;

        .btn-item {
          width: 47%;
          margin-bottom: 20rpx;
          height: 100rpx;
          border-radius: 16rpx;
          background: #f2f3f5;
          // flex: 1;
          display: flex;
          align-items: center;
          padding: 20rpx;
          justify-content: space-between;
        }
      }
    }
  }
</style>
