import { request } from '@/utils/request'

// 我的任务详情
export function getTaskProfile(data: { taskId: string }) {
  return request({
    url: '/patrol/task/profile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 我的巡检任务-巡检对象
export function getMyTaskObjectList(data: { taskId: string }) {
  return request({
    url: '/patrol/task/myTask/object/list',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 巡检地图
export function getTrackMap(data: { taskId: string }) {
  return request({
    url: '/patrol/task/track/map',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//开始巡检
export function patrolStart(data: { taskId: string }) {
  return request({
    url: '/patrol/task/start',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//结束巡检
export function patrolEnd(data: { taskId: string }) {
  return request({
    url: '/patrol/task/end',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 获取巡检任务异常信息
export function getTaskAbnormal(data: { taskId: string }) {
  return request({
    url: '/patrol/task/abnormal/get',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
