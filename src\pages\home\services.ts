import { request } from '@/utils/request'

export function getWeather(data: { locationCode: string }) {
  return request({
    url: '/external/weather/today',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

export function getWarnCount() {
  return request({
    url: '/wcp/mobile/getWarnCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
