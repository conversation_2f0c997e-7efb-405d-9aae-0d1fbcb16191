<template>
  <view class="h-screen bg-[#ffffff]">
    <view class="page-nav-top-common-bg1-white">
      <NavBar :title="pageTitle" :background="{ background: 'transparent' }"></NavBar>
      <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

      <view class="top-bar">
        <view class="top-bar-left">
          <SvgIcon name="duty-calendar-icon" class="mr-[12rpx]" />
          <text>日志日期</text>
        </view>
        <view class="top-bar-right">
          <view class="text-[#4E5969] mr-[8rpx]">{{ dayjs(props.logDate).format('YYYY年M月D日') }}</view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="scroll-content">
      <u-form
        :model="state.form"
        ref="formRef"
        :rules="state.rules"
        label-position="top"
        :error-type="['border-bottom', 'toast']"
      >
        <u-form-item label="班组" label-position="left" :border-bottom="true">
          <view class="w-full text-right">
            {{ props.groupName }}
          </view>
        </u-form-item>

        <view class="f-row" v-if="state.allShiftList.find(el => el.shiftId == props.shiftId)?.shiftName">
          <text class="label">值班班次</text>
          <text
            class="value"
            :style="{
              color: getColor(props.shiftId),
              background: getColor(props.shiftId)?.replace('1)', '0.1)'),
            }"
          >
            {{ state.allShiftList.find(el => el.shiftId == props.shiftId)?.shiftName }}
          </text>
        </view>

        <u-form-item label="值班班次" prop="shiftName" :border-bottom="true" v-else>
          <u-input
            v-model="state.form.shiftName"
            type="select"
            @click="state.isShowClasses = true"
            placeholder="请选择值班班次"
          />
        </u-form-item>

        <u-form-item label="天气" prop="weatherName" required :border-bottom="true" v-if="props.logType == '3'">
          <u-input
            v-model="state.form.weatherName"
            type="select"
            @click="state.isShowWeather = true"
            placeholder="天气"
          />
        </u-form-item>
        <u-form-item label="水位" prop="waterLevel" required :border-bottom="true" v-if="props.logType == '3'">
          <u-input type="input" v-model="state.form.waterLevel" placeholder="请输入"></u-input>
          m
        </u-form-item>
        <u-form-item label="降雨" prop="rainfall" required :border-bottom="true" v-if="props.logType == '3'">
          <u-input type="input" v-model="state.form.rainfall" placeholder="请输入"></u-input>
          mm
        </u-form-item>
        <u-form-item label="库容" prop="storageCapacity" required :border-bottom="true" v-if="props.logType == '3'">
          <u-input type="input" v-model="state.form.storageCapacity" placeholder="请输入"></u-input>
          万m³
        </u-form-item>

        <u-form-item label="日志内容" prop="logContent" :border-bottom="false">
          <u-input type="textarea" maxlength="200" clearable v-model="state.form.logContent" :border="true" />
        </u-form-item>

        <u-form-item label="日志附件图片" prop="urls" :border-bottom="false">
          <MyUpload
            :urls="state.form.urls"
            @update:urls="urls => (state.form.urls = urls)"
            folderName="schedule-log"
          ></MyUpload>
        </u-form-item>
      </u-form>
    </scroll-view>
    <u-select
      v-model="state.isShowClasses"
      mode="single-column"
      :list="state.groupShiftList"
      @cancel="state.isShowClasses = false"
      @confirm="onSelectConfirmClasses"
    ></u-select>

    <u-select
      v-model="state.isShowWeather"
      mode="single-column"
      :list="state.weatherOptions"
      @cancel="state.isShowWeather = false"
      @confirm="onSelectConfirmWeather"
    ></u-select>

    <view class="bottom-button">
      <u-button type="primary" size="default" :loading="state.loading" @click="onBtnClick">发布</u-button>
    </view>
  </view>
</template>

<script setup>
  import { getOptions } from '@/api/common.ts'
  import { reactive, ref, onMounted, watch, computed, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { workShiftPage, workLogAdd, workLogUpdate, getDetails, getWorkLogShifts } from '../services'
  import { useUserStore } from '@/store/modules/user'
  import dayjs from 'dayjs'
  import MyUpload from '@/components/MyUpload/index.vue'

  const router = useRouter()
  const formRef = ref(null)
  const userStore = useUserStore()
  const uNotifyRef = ref()
  const colors = [
    'rgba(35, 195, 67, 1)',
    'rgba(64, 128, 255, 1)',
    'rgba(141, 78, 218, 1)',
    'rgba(247, 84, 168, 1)',
    'rgba(51, 209, 201, 1)',
    'rgba(159, 219, 29, 1)',
    'rgba(255, 154, 46, 1)',
    'rgba(247, 186, 30, 1)',
  ]

  const props = defineProps(['logType', 'logDate', 'shiftId', 'groupId', 'logId', 'groupName'])

  const state = reactive({
    allShiftList: [],
    groupShiftList: [],
    haveShiftId: null,

    isShowWeather: false,
    weatherOptions: [],

    form: {
      logId: undefined,
      shiftId: undefined,
      shiftName: undefined,
      logContent: '',
      urls: [],

      weatherCode: undefined,
      weatherName: undefined,
      waterLevel: undefined,
      rainfall: undefined,
      storageCapacity: undefined,
    },
    rules: {
      shiftName: [{ required: true, message: '请选择值班班次' }],

      weatherName: [{ required: true, message: '天气不能为空', trigger: 'change, blur' }],
      waterLevel: [{ required: true, message: '水位不能为空', trigger: 'change, blur' }],
      rainfall: [{ required: true, message: '降雨不能为空', trigger: 'change, blur' }],
      storageCapacity: [{ required: true, message: '库容量不能为空', trigger: 'change, blur' }],
    },
    loading: false,
  })

  const pageTitle = computed(() => {
    if (props.logType == '1') return '值班日志'

    if (props.logType == '2') return '调度日志'

    if (props.logType == '3') return '水库值班日志'
  })
  onLoad(() => {
    state.haveShiftId = props.shiftId
  })
  onMounted(() => {
    formRef.value.setRules(state.rules)
    getWorkLogShifts({ groupId: props.groupId, logDate: props.logDate }).then(res => {
      state.groupShiftList = (res.data || []).map(el => ({ label: el.shiftName, value: `${el.shiftId}` }))
    })
    workShiftPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
      state.allShiftList = (res.data?.data || []).reverse()
    })

    //天气
    getOptions('dutyWeather').then(res => {
      state.weatherOptions = res?.data?.map(el => ({ label: el.value, value: el.key }))
    })

    if (props.logId) {
      getDetails({ logType: props.logType, logDate: props.logDate, groupId: props.groupId }).then(res => {
        state.form = {
          logId: res.data.logId,
          shiftId: res.data.shiftId || undefined,
          logContent: res.data.logContent,
          urls: res.data?.attachVOS != null ? res.data?.attachVOS?.map(el => el.attachUrl) : [],
        }
        state.form.shiftName = state.allShiftList.find(el => el.shiftId == res.data.shiftId)?.shiftName
      })
    }
    if (props.shiftId) {
      state.form.shiftId = props.shiftId === 'null' ? undefined : props.shiftId
    }
  })

  const getColor = shiftId => {
    return colors[state.allShiftList.findIndex(ele => ele.shiftId == shiftId)]
  }

  const onSelectConfirmClasses = row => {
    state.form.shiftName = row[0]?.label
    state.form.shiftId = row[0]?.value
  }

  //
  const onSelectConfirmWeather = row => {
    state.form.weatherName = row[0]?.label
    state.form.weatherCode = row[0]?.value
  }

  const onBtnClick = () => {
    formRef.value.validate(valid => {
      if (valid) {
        const params = { ...state.form, logType: props.logType, logDate: props.logDate, groupId: props.groupId }
        state.loading = true

        if (state.form.logId !== undefined) {
          workLogUpdate(params)
            .then(response => {
              uNotifyRef.value.show({ type: 'success', title: '修改成功', duration: 800 })

              setTimeout(() => {
                router.push({
                  path: '/pages/middle-page/success/index',
                  query: {
                    title: '修改成功',
                    backText: '后返回我的值班列表',
                    btnText: '立即返回',
                    url: 3,
                  },
                })
              }, 800)
            })
            .catch(() => (state.loading = false))
        } else {
          workLogAdd(params)
            .then(response => {
              uNotifyRef.value.show({ type: 'success', title: '发布成功', duration: 800 })
              setTimeout(() => {
                router.push({
                  path: '/pages/middle-page/success/index',
                  query: {
                    title: '提交成功',
                    backText: '后返回我的值班列表',
                    btnText: '立即返回',
                    url: 2,
                  },
                })
              }, 800)
            })
            .catch(() => (state.loading = false))
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .top-bar {
    margin: 0rpx 32rpx;
    padding: 24rpx;
    border-radius: 8rpx;
    background: #e8f3ff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .top-bar-left {
      display: flex;
      align-items: center;
    }

    .top-bar-right {
      display: flex;
      align-items: center;

      .class-item {
        width: 66rpx;
        text-align: center;
        display: inline-block;
        margin-left: 16rpx;

        .current-day {
          background: #165dff;
          border-radius: 50%;
          color: #ffffff;
        }
      }
    }
  }

  .scroll-content {
    padding: 0 38rpx;
    width: calc(100% - 76rpx);
    max-height: calc(100vh - 44px - 320rpx);
  }

  .bottom-button {
    // height: 194rpx;
    padding: 26rpx 32rpx;
    background: #ffffff;
    box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.3);
    border-radius: 50rpx 50rpx 0 0;
    position: fixed;
    bottom: 0;
    width: 100vw;
    z-index: 999;
  }

  .f-row {
    display: flex;
    margin-top: 20rpx;

    .value {
      margin-left: auto;
    }
  }
  :deep(.u-form-item--left__content--required) {
    left: 0;
  }
  :deep(.u-form-item--left__content__label) {
    padding-left: 16rpx;
  }
</style>
