export const siteIcons = {
  // 水文站
  ['MS001_ZQ']: require('@/static/images/site-icons/hydrology.png'),
  // 水位站
  ['MS001_ZZ']: require('@/static/images/site-icons/water-level.png'),
  // 流量站
  ['MS001_FL']: require('@/static/images/site-icons/water-flow.png'),
  // 雨量站
  ['MS001_PP']: require('@/static/images/site-icons/rain.png'),
  // 水质站
  ['MS001_WQ']: require('@/static/images/site-icons/water-quality.png'),
  // 土壤墒情站
  ['MS001_SS']: require('@/static/images/site-icons/soil.png'),
  // 地下水站
  ['MS001_ZG']: require('@/static/images/site-icons/groundwater.png'),
  // 降水量站
  ['MS001-3']: require('@/static/images/site-icons/precipitation.png'),
  // 水面蒸发站
  ['MS001-4']: require('@/static/images/site-icons/water-evaporation.png'),
  // 水位站-水库
  ['MS001-9']: require('@/static/images/site-icons/reservoir-level.png'),
  // 水生态
  ['MS001-8']: require('@/static/images/site-icons/water-ecology.png'),

  // 量测水监测
  ['MS001_SL']: require('@/static/images/site-icons/MS001_SL.png'),
  // 安全监测
  ['MS001_EL']: require('@/static/images/site-icons/MS001_EL.png'),

  //水量监测站
  ['MS001_SLJL']: require('@/static/images/site-icons/MS001_SLJL.png')
}

export const terminals = [
  { label: '雨量', value: 'rainfall' },
  { label: '水位', value: 'waterLevel' },
  { label: '流量', value: 'flow' },
]

// 类型(1逐时 2逐日 3逐月 4瞬时)
export const timeRangesOptions = [
  { name: '瞬时', value: '4' },
  { name: '逐时', value: '1' },
  { name: '逐日', value: '2' },
]
export const timeRangesOptions2 = [{ name: '逐时', value: '1' }]

export const chartColors = [
  '#549DFA',
  '#AC3B2A',
  '#567EFE',
  '#20E5CB',
  '#BC20E5',
  '#FFD15C',
  '#7F43F8',
  '#DE7C45',
  '#34B548',
  '#A7D2F4',
]
