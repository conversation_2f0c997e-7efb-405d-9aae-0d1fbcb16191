/** scss注释 */
.task-card {
  background: #e0eaf9 url('@/static/images/info-card-bg.png') no-repeat;
  background-size: 100% 400rpx;
  padding: 20rpx 20rpx 0;
  margin: 0 20rpx;
  border-radius: 14rpx;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64rpx;

    font-size: 24rpx;
    color: #ffffff;
    line-height: 28rpx;
  }

  .card-content {
    background-color: #fff;
    border-radius: 16rpx;

    .title {
      padding: 20rpx 20rpx 0 20rpx;
      font-size: 32rpx;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 600;
      color: #1d2129;
      line-height: 32rpx;

      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

      .task-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 5rpx;
        vertical-align: middle;
      }
    }

    .bottom {
      padding: 0 20rpx;
      height: 70rpx;
      line-height: 70rpx;
      background: linear-gradient(180deg, rgba(194, 226, 248, 0.3) 0%, rgba(215, 229, 248, 0) 100%);
      border-radius: 16rpx;
      display: flex;

      font-size: 28rpx;
      .flex {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        .label {
          color: #86909c;
          white-space: nowrap;
        }

        .value {
          color: #1d2129;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .other-info {
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    opacity: 0;

    height: 0;
    // transition: all 0.3s;

    .indicator {
      display: flex;
      flex-wrap: wrap;
      padding: 0 20rpx;

      font-size: 28rpx;
      .flex {
        flex-basis: 50%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        &:nth-child(n + 3) {
          margin-top: 10rpx;
        }
        .label {
          color: #86909c;
          white-space: nowrap;
        }

        .value {
          color: #1d2129;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .expand-other-info {
    margin-top: 8rpx;
    padding-top: 20rpx;
    opacity: 1;

    height: 216rpx;
    // transition: all 0.3s;
  }

  .time-line {
    padding: 16rpx 20rpx;
    .time {
      font-size: 28rpx;
      font-weight: 300;
      color: #86909c;
      line-height: 28rpx;

      &:last-child {
        margin-top: 16rpx;
      }
    }
  }

  .action {
    font-size: 24rpx;
    color: #165dff;
    height: 62rpx;
    line-height: 62rpx;
    text-align: center;
  }

  .tag {
    width: 112rpx;
    height: 44rpx;
    line-height: 44rpx;
    text-align: center;
    font-size: 24rpx;
    font-weight: 300;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 80rpx;
  }
  .tag1 {
    color: #f76560;
  }
  .tag2 {
    color: #165dff;
  }
  .tag3 {
    color: #4e5969;
  }
}

.list-info {
  display: flex;
  margin: 10rpx 0 0 25rpx;

  .item {
    margin: 0 6rpx;
    display: flex;
    .label {
      font-size: 24rpx;
      color: #86909c;
      line-height: 24rpx;
      margin-right: 8rpx;
    }
    .value {
      font-size: 26rpx;
      font-weight: 700;
      line-height: 22rpx;
    }
  }
  .gap-line {
    font-size: 24rpx;
    color: #d8d8d8;
    line-height: 20rpx;
  }
}

.right-icon {
  width: 48rpx;
  height: 48rpx;
}

.float-container {
  overflow: auto;
  .container {
    height: 100%;

    .project-item {
      margin: 32rpx;
      .project-title {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #4e5969;
      }
      .object-item {
        margin-top: 18rpx;
        padding: 30rpx 20rpx 30rpx 30rpx;
        background: #f7f8fa;
        border-radius: 16rpx;

        .object-name {
          font-size: 30rpx;
          font-weight: 600;
          color: #1d2129;
          margin-bottom: 14rpx;

          display: flex;
          align-items: center;
          justify-content: space-between;

          .name {
            max-width: 510rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
          .object-status-icon {
            width: 32rpx;
            height: 32rpx;
          }
        }
        .object-code {
          font-size: 24rpx;
          color: #86909c;
        }
      }
    }
  }
}

.bottom-button {
  // height: 194rpx;
  padding: 26rpx 32rpx;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.3);
  border-radius: 50rpx 50rpx 0 0;
  position: fixed;
  bottom: 0;
  width: 100vw;
  z-index: 999;
}
