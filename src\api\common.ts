import { request, ContentTypeEnum } from '@/utils/request'

// 登录
export function ssoLogin(data: any) {
  return request({
    url: '/sys/casLogin',
    method: 'post',
    data,
  })
}

// 登出
export function logout() {
  return request({
    url: '/sys/logout',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// 用户信息
export function getUserInfo(parameter: any) {
  return request({
    url: '/sys/user/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: 'userId=' + parameter,
    //parameter
  })
}

// 获取当前登录用户信息
export function getUserProfile() {
  return request({
    url: '/sys/user/profile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 用户列表分页查询
export function getUserPage(data: any) {
  return request({
    url: '/sys/user/page',
    method: 'post',
    data,
  })
}

// 获取路由
export function getRouters() {
  return request({
    url: '/sys/user/getMenuPermissions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 获取字典下拉选项
export function getOptions(dictCode: string) {
  return request({
    url: '/sys/dict/getOptions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { dictCode },
  })
}

// 获取参数配置值
export function getValueByKey(configKey: string) {
  return request({
    url: '/sys/config/getValueByKey',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { configKey },
  })
}

// 文件上传
export function minIoUpload(data: any) {
  return request({
    url: '/external/minio/upload',
    method: 'post',
    header: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
    data,
  })
}

// 有权限登录的机构列表
export function getDeptLoginOrtList() {
  return request({
    url: '/sys/dept/loginOrg/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 切换登录机构
export function switchLoginOrg(data: any) {
  return request({
    url: '/sys/switchLoginOrg',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 用户列表
export function getUserList(params: any) {
  return request({
    url: '/sys/user/user/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//点位上报
export interface AppearType {
  latitude: string
  longitude: string
  taskId: string
  uploadTime: string
}
export function getPointAppear(data: AppearType) {
  return request({
    url: '/patrol/task/track/add',
    method: 'post',
    data,
  })
}

//查询当前登录机构的部门树
export function getTreeByLoginOrgId(params: object) {
  return request({
    url: '/sys/dept/treeByLoginOrgId',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 工程信息-获取树
export function getProjectTree(data: object) {
  return request({
    url: '/base/project/getTree',
    method: 'post',
    data,
  })
}
//水系列表
export function getRiverSystemList() {
  return request({
    url: '/base/riverSystem/list',
    method: 'post',
  })
}
// 水利对象分类-获取树 监测站点树
export function getBaseSite() {
  return request({
    url: '/base/site/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//用户列表
export function getComUserList(params: object) {
  return request({
    url: '/sys/user/user/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//获取单位站点树
export function getDeptSiteTree(data: object) {
  return request({
    url: '/war/history/deptSite/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}
