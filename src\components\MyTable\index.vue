<script setup>
  import { watch, useAttrs } from 'vue'

  const attrs = useAttrs()

  const props = defineProps({
    tableData: { default: [] },
    column: { default: [] },
    border: { default: true },
    isZebra: { default: false }
  })
</script>

<template>
  <scroll-view class="my-table bg-[#ffffff]" :scroll-x="true" show-scrollbar>
    <u-table class="table" color="#1d2129" :border-color="props.border ? '#e4e7ed' : 'transparent'">
      <u-tr>
        <u-th
          class="th"
          v-for="(item, index) in props.column"
          :key="index"
          :style="{
            flex: `0 0 ${item.width + 'rpx' || 'auto'} !important`,
            borderColor: props.border ? '#e6e6e6' : 'transparent'
          }"
        >
          {{ item.title }}
        </u-th>
      </u-tr>
      <u-tr
        v-for="(item, index) in props.tableData"
        :key="index"
        :style="props.isZebra && index % 2 != 0 ? { background: 'rgba(247,248,250,0.8)' } : {}"
        @click="() => attrs.onRowClick && attrs.onRowClick(item, index)"
      >
        <u-td
          v-for="(context, i) in props.column"
          :key="i"
          :width="context.width ? context.width + 'rpx' : 'auto'"
          class="td"
          :style="{ borderColor: props.border ? '#e6e6e6' : 'transparent' }"
        >
          <text v-if="context.title == '序号'">{{ index + 1 }}</text>
          <template v-else-if="!!context.slotName">
            <slot :name="context.slotName" :record="item"></slot>
          </template>
          <view v-else-if="!context.slotName">
            {{ item[context.dataIndex] === 0 ? '0' : item[context.dataIndex] || '　' }}
          </view>
        </u-td>
      </u-tr>
      <slot name="totalRow"></slot>
    </u-table>
    <u-empty v-if="props.tableData?.length == 0" text="暂无数据" mode="data" margin-top="40"></u-empty>
  </scroll-view>
</template>

<style scoped lang="scss">
  * {
    box-sizing: border-box;
  }
  .table {
    // border-radius: ;
  }

  .th {
    text-align: center;
    font-size: 24rpx;
    color: #86909c;
    font-weight: 400 !important;
    padding: 12rpx 0 !important;
  }

  .td {
    text-align: center;
    color: #1d2129;
    font-size: 24rpx;
    line-height: 72rpx;

    display: -webkit-box; // 将元素作为弹性伸缩盒子模型显示
    overflow: hidden; // 文本溢出限定的宽度就隐藏内容
    text-overflow: ellipsis; // 多行文本的情况下，用省略号 “…” 隐藏溢出范围的文本
    -webkit-box-orient: vertical; // 设置伸缩盒子的子元素排列方式：从上到下垂直排列
    -webkit-line-clamp: 1; // 用来限制在一个块元素显示的文本的行数
  }
</style>
