<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="应急抢修" :background="{ backgroundColor: 'transparent' }">
      <template #right>
        <text class="add-btn" @click="add" v-if="!state.isManagementBureau">+</text>
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="relative">
      <u-dropdown
        ref="dropdownRef"
        height="80"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 0"
        @open="onDropdownOpen"
      >
        <u-dropdown-item :title="state.reportingUnit.title">
          <scroll-view class="drop-content" scroll-y show-scrollbar>
            <DaTree
              ref="emergencyTreeRef"
              :data="state.deptTree"
              labelField="deptName"
              valueField="deptId"
              defaultExpandAll
              @change="handleDeptTreeChange"
            ></DaTree>
          </scroll-view>
        </u-dropdown-item>

        <u-dropdown-item
          height="700"
          v-model="state.disposalStatus.value"
          :title="state.disposalStatus.title"
          :options="disposalOptions"
          @change="onChangeDisposalStatus"
        ></u-dropdown-item>

        <u-dropdown-item
          height="700"
          v-model="state.emergencyType.value"
          :title="state.emergencyType.title"
          :options="emergencyTypeOptions"
          @change="onEmergencyTypeChange"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <ZPaging
    ref="pagingRef"
    :style="{ marginTop: `calc(44px + ${userStore.statusBarHeight}px + 100rpx)`, paddingTop: '28rpx', zIndex: 10 }"
    v-model="state.list"
    @query="getList"
  >
    <view class="com-card" v-for="(el, idx) in state.list" :key="idx">
      <view class="card-row top">
        <view
          class="status"
          :class="{
            waiting: el?.status == 1 || el?.status == 4,
            revoke: el?.status == 2,
            inProgress: el?.status == 3,
            finish: el?.status == 5,
          }"
        >
          {{ disposalOptions?.find(ele => ele.value == el.status)?.label }}
        </view>
        <view class="number value">{{ el.serialNumber }}</view>
      </view>
      <view class="card-row">
        <text class="label">提报单位</text>
        <text class="value text-ellipsis">{{ el.deptName }}</text>
      </view>
      <view class="card-row">
        <text class="label">应急类型</text>
        <text class="value">
          {{ el?.emergencyType === 1 ? '隐患问题' : el?.emergencyType === 2 ? '异常情况' : '--' }}
        </text>
      </view>
      <view class="card-row">
        <text class="label">问题描述</text>
        <text class="value text-ellipsis">{{ el.content }}</text>
      </view>
      <view class="card-row">
        <text class="date">{{ el.createdTime?.substring(0, 10) }}</text>
        <view class="btn-group">
          <u-button
            class="btn no-bg-btn"
            v-if="el?.status === 1 && state.isManagementBureau"
            @click="startDisposal(el)"
          >
            开始处置
          </u-button>
          <u-button
            class="btn no-bg-btn"
            v-else-if="el?.status === 3 && state.isManagementBureau"
            @click="finishDisposal(el)"
          >
            结束处置
          </u-button>
          <u-button
            class="btn no-bg-btn"
            v-else-if="el?.status === 4 && !state.isManagementBureau"
            @click="toReview(el)"
          >
            复核
          </u-button>
          <u-button
            class="btn no-bg-btn"
            v-else-if="el?.status === 1 && !state.isManagementBureau"
            @click="withdraw(el)"
          >
            撤回
          </u-button>
          <u-button
            class="btn no-bg-btn"
            v-else-if="el?.status === 2 && !state.isManagementBureau"
            @click="handleDeleteRepair(el)"
          >
            删除
          </u-button>
          <u-button class="btn details-btn" @click="onClickDetails(el)">详情</u-button>
        </view>
      </view>
    </view>
  </ZPaging>
  <ConfirmPopup
    :show="state.showConfirmDel"
    @update:show="
      () => {
        state.showConfirmDel = val
      }
    "
    @onConfirm="onConfirmDelete"
    popupTitle="确认删除"
    title="请确认是否删除?"
    description=""
    type="warning"
  />
  <ConfirmPopup
    :show="state.showConfirmWithdraw"
    @update:show="
      () => {
        state.showConfirmWithdraw = val
      }
    "
    @onConfirm="onConfirmWithdraw"
    popupTitle="确认撤回"
    title="请确认是否撤回?"
    description=""
    type="warning"
  />
</template>

<script setup>
  import { reactive, ref, onMounted, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import DaTree from '@/components/da-tree/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import { getValueByKey, getTreeByLoginOrgId } from '@/api/common.ts'
  import { getEmergencyRepairPage, deleteEmergencyRepair, withdrawEmergencyRepair } from './services'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)
  const emergencyTreeRef = ref(null)

  const disposalOptions = [
    { label: '待处置', value: 1 },
    { label: '已撤回', value: 2 },
    { label: '处置中', value: 3 },
    { label: '待复核', value: 4 },
    { label: '已完成', value: 5 },
  ]
  const emergencyTypeOptions = [
    { label: '隐患问题', value: 1 },
    { label: '异常情况', value: 2 },
  ]

  const state = reactive({
    deptTree: [],
    reportingUnit: { value: undefined, title: '提报单位', preVal: undefined },
    disposalStatus: { value: undefined, title: '处置状态', preVal: undefined },
    emergencyType: { value: undefined, title: '应急类型', preVal: undefined },

    list: [],
    delId: undefined,
    isManagementBureau: false,
    showConfirmDel: false,
    showConfirmWithdraw: false,

    popupTitle: '',
    popupAlert: '',
  })
  onMounted(() => {
    getTreeByLoginOrgId().then(res => {
      state.deptTree = res?.data
    })
    getValueByKey('managementBureau').then(res => {
      if (res.data == JSON.parse(uni.getStorageSync('user'))?.loginOrg?.loginOrgId) {
        state.isManagementBureau = true
      }
    })
  })
  const onDropdownOpen = () => {
    nextTick(() => {
      emergencyTreeRef.value?.setCheckedKeys(state.reportingUnit.value, true)
    })
  }

  onShow(() => {
    getList(1)
  })

  const getList = (pageNo, pageSize) => {
    getEmergencyRepairPage({
      deptId: state.reportingUnit.value,
      emergencyType: state.emergencyType.value,
      objectCategoryCode: undefined,
      objectId: undefined,
      serialNumber: undefined,
      status: state.disposalStatus.value,
      type: undefined,
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  function handleDeptTreeChange(allSelectedKeys, currentItem) {
    if (state.reportingUnit.value === currentItem.key) {
      emergencyTreeRef.value?.setCheckedKeys(currentItem.key, false)
      state.reportingUnit = { title: '提报单位', value: undefined }
    } else {
      state.reportingUnit = { title: currentItem.label, value: currentItem.key }
    }
    pagingRef.value.reload()
    dropdownRef.value.close()
  }
  function onChangeDisposalStatus(value) {
    if (value === state.disposalStatus.preVal) {
      state.disposalStatus = { title: '处置状态', value: undefined, preVal: undefined }
    } else {
      state.disposalStatus = {
        title: disposalOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }
    pagingRef.value.reload()
  }
  function onEmergencyTypeChange(value) {
    if (value === state.emergencyType.preVal) {
      state.emergencyType = { title: '应急类型', value: undefined, preVal: undefined }
    } else {
      state.emergencyType = {
        title: emergencyTypeOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    pagingRef.value.reload()
  }
  //删除
  const handleDeleteRepair = el => {
    state.showConfirmDel = true
    state.delId = el?.repairId
  }
  const onConfirmDelete = () => {
    deleteEmergencyRepair({ repairIds: state.delId }).then(res => {
      state.showConfirmDel = false
      uNotifyRef.value.show({
        type: 'success',
        title: '删除成功',
        duration: 800,
      })
      pagingRef.value.reload()
    })
  }
  //撤回
  const withdraw = el => {
    state.showConfirmWithdraw = true
    state.delId = el?.repairId
  }
  const onConfirmWithdraw = () => {
    withdrawEmergencyRepair({ repairId: state.delId }).then(res => {
      state.showConfirmWithdraw = false
      uNotifyRef.value.show({
        type: 'success',
        title: '撤回成功',
        duration: 800,
      })
      pagingRef.value.reload()
    })
  }

  //增加
  const add = () => {
    router.push({
      path: '/pages/emergency-repair/add/index',
    })
  }
  //详情
  const onClickDetails = el => {
    router.push({
      path: '/pages/emergency-repair/details/index',
      query: {
        repairId: el?.repairId,
        objectNames: el?.objectNames == null ? '--' : el?.objectNames,
      },
    })
  }
  //开始处置
  const startDisposal = el => {
    router.push({
      path: '/pages/emergency-repair/details/index',
      query: {
        repairId: el?.repairId,
        objectNames: el?.objectNames == null ? '--' : el?.objectNames,
        source: 'startDisposal',
      },
    })
  }
  const finishDisposal = el => {
    router.push({
      path: '/pages/emergency-repair/details/index',
      query: {
        repairId: el?.repairId,
        objectNames: el?.objectNames == null ? '--' : el?.objectNames,
        source: 'finishDisposal',
      },
    })
  }
  const toReview = el => {
    router.push({
      path: '/pages/emergency-repair/details/index',
      query: {
        repairId: el?.repairId,
        objectNames: el?.objectNames == null ? '--' : el?.objectNames,
        source: 'toReview',
      },
    })
  }
</script>

<style lang="scss" scoped>
  @import url(@/pages/maintenance-record/maintenance.scss);
</style>
