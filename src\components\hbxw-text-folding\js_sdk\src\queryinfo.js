// 获取单个元素节点信息
export const selector = (selector, obj) => {
	return new Promise((resolve, reject) => {
		try{
			const query = uni.createSelectorQuery().in(obj);
			query.select(selector).boundingClientRect((data) => {
				resolve(data);
			}).exec();
		}catch(err){
			reject(err)
		}
	})
}

// 获取多个元素节点信息
export const selectorAll = (selector, obj) => {
	return new Promise((resolve, reject) => {
		try{
			const query = uni.createSelectorQuery().in(obj);
			query.selectAll(selector).boundingClientRect((data) => {
				resolve(data);
			}).exec();
		}catch(err){
			reject(err)
		}
	})
}