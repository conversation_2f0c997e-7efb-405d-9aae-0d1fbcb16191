<template>
  <view class="user page-bottom-tab">
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="user-header">
      <view class="user-box">
        <image class="user-photo" v-if="userStore.user.avatar" :src="userStore.user.avatar" />
        <view class="user-photo" v-else>{{ userStore.user.name.slice(userStore.user.name.length - 2) }}</view>
        <view class="user-info">
          <text class="user-name">{{ userStore.user.name }}</text>

          <view class="phone-box">
            <view class="phone-icon mt-[4rpx]"></view>
            <text class="user-phone">{{ userStore.user.mobile }}</text>
          </view>
        </view>
      </view>
      <view class="edit-btn" @click="onEditInfoClick">修改资料</view>
    </view>

    <view class="org-content">
      <view class="flex items-center">
        <view class="user-org-img"></view>
        <view>
          <view class="org-name u-line-1">{{ state.currentOrg?.orgName }}</view>
          <view class="text-[#727FAC] text-[12px] mt-[8rpx]">当前所在组织</view>
        </view>
      </view>
      <view
        class="px-[24rpx] py-[12rpx] bg-[#405290] rounded-[50rpx] text-[#ffffff] text-[12px]"
        @click="state.isShowOrgSelect = true"
      >
        切换组织
      </view>
    </view>

    <view class="com-card operate-panel" @click="onEditPasswordClick">
      <view class="flex items-center">
        <view class="icon-edit-password"></view>
        <text class="name">修改密码</text>
      </view>
      <u-icon name="arrow-right" color="#86909C" size="26"></u-icon>
    </view>
  </view>

  <view class="logout-btn" @click="onLogoutClick">退出登录</view>

  <view class="w-full text-center text-xs text-gray-400 fixed bottom-16">当前版本 {{ version }}</view>

  <u-select
    v-model="state.isShowOrgSelect"
    :default-value="state.selectIndex"
    mode="single-column"
    :list="userStore.orgList"
    title="切换组织"
    label-name="deptName"
    value-name="deptId"
    @cancel="state.isShowOrgSelect = false"
    @confirm="onSelectConfirm"
  ></u-select>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { usePatrolStore } from '@/store/modules/patrol.ts'
  import { logout, switchLoginOrg } from '@/api/common.ts'

  import { onLoad } from '@dcloudio/uni-app'

  const userStore = useUserStore()
  const patrolStore = usePatrolStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const state = reactive({
    isShowOrgSelect: false,
    selectIndex: [0],
    currentOrg: { orgId: userStore.orgList[0]?.deptId, orgName: userStore.orgList[0]?.deptName },
  })

  // #ifdef APP-PLUS
  const version = plus.runtime.version
  // #endif

  const onEditInfoClick = () => {
    router.push('/pages/user/edit-user/index')
  }

  const onEditPasswordClick = () => {
    router.push('/pages/user/edit-password/index')
  }

  const onLogoutClick = () => {
    logout().then(res => {
      patrolStore.finishPatrolInterval()
      userStore.removeToken()

      uNotifyRef.value.show({
        type: 'success',
        title: '退出成功',
        duration: 800,
      })

      setTimeout(() => {
        router.replaceAll('/pages/login/index')
      }, 2000)
    })
  }

  const onSelectConfirm = item => {
    switchLoginOrg({ orgId: item[0]?.value }).then(res => {
      userStore.setUserInfo(res.data.token, userStore.user)
      userStore.setLoginOrg({ loginOrgId: res?.data?.loginOrgId, loginOrgName: res?.data?.loginOrgName })
      state.selectIndex = [item[0].extra]
      state.currentOrg = { orgId: res.data?.loginOrgId, orgName: res.data?.loginOrgName }
      state.isShowOrgSelect = false
    })
  }
</script>

<style lang="scss" scoped>
  .user {
    background: url('~@/static/images/details-top-bg.png') no-repeat;
    background-size: 100%;
    padding-top: 168rpx;
  }
  .user-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 110rpx;
    padding: 0 32rpx;

    .user-box {
      display: flex;
      align-items: center;
      .user-photo {
        width: 128rpx;
        height: 128rpx;
        box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(55, 114, 255, 0.24);
        border: 4rpx solid #ffffff;
        border-radius: 50%;

        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 40rpx;
        background: #4080ff;
      }
      .user-info {
        flex: 1;
        margin-left: 32rpx;
        .user-name {
          font-size: 48rpx;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #1d2129;
        }

        .phone-box {
          display: flex;
          align-items: center;
          margin-left: -16rpx;
          .phone-icon {
            width: 64rpx;
            height: 64rpx;
            background: url('@/static/icons/icon-phone.svg') no-repeat;
            background-size: 100% 100%;
          }
          .user-phone {
            margin-left: -5rpx;
            font-weight: 300;
            color: #1d2129;
            line-height: 28rpx;
          }
        }
      }
    }

    .edit-btn {
      width: 160rpx;
      height: 64rpx;
      line-height: 64rpx;
      background: #e8f3ff;
      text-align: center;
      font-size: 24rpx;
      font-weight: 300;
      color: #165dff;
      border-radius: 64rpx;
    }
  }

  .org-content {
    margin: 0 32rpx;
    padding: 10rpx 20rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: url('@/static/images/user-org-bg.png') no-repeat;
    background-size: 100% 100%;

    .user-org-img {
      margin-right: 8rpx;
      width: 92rpx;
      height: 92rpx;
      background: url('@/static/images/user-org-img.png') no-repeat;
      background-size: 100% 100%;
    }
    .org-name {
      font-size: 30rpx;
      color: #263b85;
      font-family: DingTalk JinBuTi;
      max-width: 360rpx;
    }
  }

  .operate-panel {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon-edit-password {
      width: 48rpx;
      height: 48rpx;
      background: url('@/static/icons/icon-edit-password.svg') no-repeat;
      background-size: 100% 100%;
    }
    .name {
      margin-left: 32rpx;
      font-size: 30rpx;
      font-weight: 300;
      color: #1d2129;
    }
  }

  .logout-btn {
    bottom: 180rpx;

    position: fixed;
    left: 50%;
    transform: translate(-50%);
    text-align: center;

    width: 272rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;

    font-size: 28rpx;
    color: #165dff;
  }
</style>
