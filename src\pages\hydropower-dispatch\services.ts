import { request } from '@/utils/request'

// 水电站调度管理——列表分页查询
export function getHydropowerDispatch(data: object) {
  return request({
    url: '/custom/hydropower/page',
    method: 'post',
    data,
  })
}

// 水电站调度管理——新增
export function addHydropowerDispatch(data: object) {
  return request({
    url: '/custom/hydropower/add',
    method: 'post',
    data,
  })
}

// 水电站调度管理——更新
export function editHydropowerDispatch(data: object) {
  return request({
    url: '/custom/hydropower/update',
    method: 'post',
    data,
  })
}
// 水电站调度管理——详情
export function getHydropowerDetails(params: object) {
  return request({
    url: '/custom/hydropower/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
