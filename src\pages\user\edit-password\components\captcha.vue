<template>
  <view class="title">短信验证</view>

  <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

  <view class="mobile">
    <text class="text-[#4E5969]">当前手机号:{{ user.mobile }}</text>
    <text class="text-[#165DFF] ml-2" @click="onCaptchaClick">
      {{ state.isShowTime ? `倒计时${state.seconds}s` : '获取验证码' }}
    </text>
  </view>

  <u-form labelPosition="left" :model="state.pwdForm" ref="formRef">
    <view class="com-card">
      <u-form-item label="验证码" :border-bottom="false" labelWidth="190">
        <u-input type="input" v-model="state.pwdForm.captcha" :border="false" placeholder="请输入"></u-input>
      </u-form-item>
    </view>

    <view class="com-card">
      <u-form-item label="输入新密码" :border-bottom="false" labelWidth="190">
        <u-input
          v-model="state.pwdForm.newPassword"
          :border="false"
          placeholder="请输入"
          clearable
          type="password"
        ></u-input>
      </u-form-item>
    </view>

    <view class="password-desc">
      <text class="desc">字符形式6～10位密码数，区分大小写</text>
      <PasswordCheck :newPassword="state.pwdForm.newPassword" />
    </view>

    <view class="com-card">
      <u-form-item label="再次确认密码" :border-bottom="false" labelWidth="190">
        <u-input
          :type="state.isShowPassword3 ? 'input' : 'password'"
          v-model="state.pwdForm.cfmPassword"
          :border="false"
          placeholder="请输入"
          clearable
          type="password"
        ></u-input>
      </u-form-item>
    </view>

    <u-button class="submit-btn" type="primary" :disabled="!getDisabled()" size="default" @click="onSubmit">
      确认修改
    </u-button>
  </u-form>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import Md5 from 'md5'
  import { getCaptcha, changePasswordBySms } from '../../services'
  import PasswordCheck from '@/components/PasswordCheck/index'
  import { useRouter } from 'uni-mini-router'

  const userStore = useUserStore()
  const router = useRouter()

  const user = userStore.user

  const uNotifyRef = ref()

  const state = reactive({
    pwdForm: {
      captcha: '',
      newPassword: '',
      cfmPassword: ''
    },

    isShowTime: false,
    timer: null,
    seconds: 60
  })

  const onCaptchaClick = () => {
    if (state.isShowTime) {
      uni.showToast({
        title: '请稍后再点击',
        duration: 2000
      })
      return
    }
    getCaptcha().then(res => {
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '验证码已发送',
          duration: 2000
        })
        state.isShowTime = true
      }, 500)

      state.timer = setInterval(() => {
        if (state.seconds > 0 && state.seconds <= 60) {
          state.seconds--
        } else {
          state.isShowTime = false
          clearInterval(state.timer)
          state.timer = null
        }
      }, 1000)
    })
  }

  const getDisabled = () => {
    return state.pwdForm.captcha.trim() && state.pwdForm.newPassword.trim() && state.pwdForm.cfmPassword.trim()
  }

  const onSubmit = () => {
    if (getDisabled()) {
      if (state.pwdForm.newPassword !== state.pwdForm.cfmPassword) {
        uNotifyRef.value.show({
          type: 'warning',
          title: '两次新密码不一致',
          duration: 1500
        })
        return
      }

      changePasswordBySms({
        captcha: state.pwdForm.captcha,
        newPassword: Md5(state.pwdForm.newPassword)
      }).then(res => {
        uNotifyRef.value.show({
          type: 'success',
          title: '修改成功',
          duration: 800
        })
        setTimeout(() => {
          router.push({
            path: '/pages/middle-page/success/index',
            query: {
              title: '修改成功',
              backText: '后请使用新密码重新登录账号',
              btnText: '立即登录',
              jumpType: 'replaceAll',
              url: '/pages/login/index'
            }
          })
        }, 800)
      })
    }
  }
</script>

<style lang="scss" scoped>
  .title {
    font-size: 44rpx;
    font-weight: 600;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #1d2129;
    text-align: center;
    margin-bottom: 60rpx;
  }
  .com-card {
    padding: 0 30rpx;
  }
  .mobile {
    margin-top: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
  }

  .password-desc {
    padding: 0 32rpx;
    font-size: 24rpx;
    font-weight: 300;
    color: #86909c;
    display: flex;
    justify-content: space-between;
  }

  .submit-btn {
    height: 116rpx;
    text-align: center;
    margin: 0 30rpx;
    margin-top: 70rpx;
    width: calc(100% - 60rpx);
    border-radius: 16rpx;

    :deep(.u-button__text) {
      font-family: PingFang SC-Medium, PingFang SC;
    }
  }

  :deep(.u-form-item__body) {
    padding: 0;
  }
</style>
