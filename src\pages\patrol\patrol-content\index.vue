<template>
  <NavBar title="巡检对象详情"></NavBar>
  <view class="page-nav-top-common">
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view
      class="container"
      :style="{
        maxHeight: [1, 2].includes(state.detail.taskStatus)
          ? 'calc(100vh - 168rpx - 64rpx - 50px)'
          : 'calc(100vh - 168rpx)',
      }"
    >
      <scroll-view class="content" scroll-y show-scrollbar>
        <u-form
          :model="state.form"
          ref="formRef"
          label-width="190"
          label-position="top"
          :label-style="{ 'line-height': 1.5 }"
        >
          <PanelTitle :title="props.taskName" />
          <view class="item-card" v-for="(ele, index) in state.dataSource.items" :key="ele.itemId">
            <!-- 单行文本 -->
            <view v-if="ele.inputType == 1">
              <u-form-item :label="ele.itemName">
                <u-input
                  :disabled="state.detail.taskStatus != 2"
                  v-model="ele.itemValue"
                  clearable
                  placeholder="请输入巡检项结果"
                />
              </u-form-item>
            </view>

            <!-- 多行文本 -->
            <view v-if="ele.inputType == 2">
              <u-form-item :label="ele.itemName">
                <u-input
                  :disabled="state.detail.taskStatus != 2"
                  v-model="ele.itemValue"
                  type="textarea"
                  clearable
                  placeholder="请输入巡检项结果"
                />
              </u-form-item>
            </view>

            <!-- 数值 -->
            <view v-if="ele.inputType == 3">
              <u-form-item :label="ele.itemName">
                <u-input
                  :disabled="state.detail.taskStatus != 2"
                  v-model="ele.itemValue"
                  type="number"
                  clearable
                  placeholder="请输入巡检项结果"
                />
              </u-form-item>
            </view>

            <!-- 下拉框 -->
            <view v-if="ele.inputType == 4">
              <u-form-item :label="ele.itemName">
                <u-input
                  :disabled="state.detail.taskStatus != 2"
                  v-model="ele.itemValue"
                  type="select"
                  @click="onFocus(ele)"
                  placeholder="请输入巡检项结果"
                />
              </u-form-item>
            </view>
          </view>

          <PanelTitle title="其他信息" />

          <view class="item-card" style="padding-bottom: 14rpx">
            <view style="font-size: 30rpx; padding: 32rpx 0">现场状况</view>
            <MyUpload
              :urls="state.dataSource.attaches"
              @update:urls="urls => (state.dataSource.attaches = urls)"
              folderName="patrol/object/attaches"
            ></MyUpload>
          </view>
        </u-form>
      </scroll-view>

      <u-select
        v-model="state.showPicker"
        mode="single-column"
        :list="state.currentItems.columns"
        @cancel="state.showPicker = false"
        @confirm="onSelectConfirm"
      ></u-select>
    </view>

    <u-button
      v-if="state.detail.taskStatus == 2"
      size="default"
      type="primary"
      native-type="submit"
      @click="onSubmitClick"
      class="bottom-button"
    >
      提交
    </u-button>

    <u-modal v-model="show" title="异常提醒" show-cancel-button mask-close-able @confirm="onSubmit">
      <view class="dialog pl-[30rpx] pr-[30rpx]">当前巡检对象存在异常，是否进行提交？</view>
    </u-modal>
  </view>
</template>

<script setup lang="ts">
  import { reactive, onMounted, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { getTaskProfile, getContent, check, submit } from './services'
  import { onLoad } from '@dcloudio/uni-app'
  import MyUpload from '@/components/MyUpload/index.vue'

  const router = useRouter()
  const uNotifyRef = ref()

  const props: any = defineProps(['taskId', 'objectId', 'taskName'])

  const show = ref(false)

  const state: any = reactive({
    detail: {},
    dataSource: { attaches: [] },
    form: {},
    showPicker: false,
    currentItems: { columns: [] },
  })

  onMounted(() => {
    getContent({ taskId: props.taskId, objectId: props.objectId }).then(res => {
      state.dataSource = res?.data || {}

      state.dataSource.items?.forEach(item => {
        if (item.options != '') {
          item.columns = item.options.split(',').map(el => ({ label: el, value: el }))
        } else {
          item.columns = []
        }

        // 下拉框默认选中第一个
        if (item.inputType == 4) {
          item.itemValue = item.columns[0].value
        }
      })

      if (state.dataSource.attaches.length != 0) {
        state.dataSource.attaches = state.dataSource.attaches.map(el => el.attachUrl)
      }
    })

    getTaskProfile({ taskId: props.taskId }).then((res: any) => {
      state.detail = res.data || {}
    })
  })

  //input框点击时获取当前的值
  const onFocus = (ele: any) => {
    state.currentItems = ele
    state.showPicker = true
  }

  const onSelectConfirm = item => {
    state.dataSource.items.forEach(el => {
      if (el.itemId == state.currentItems.itemId) {
        el.itemValue = item[0].value
      }
    })

    state.showPicker = false
  }

  //校验和提交
  function onSubmitClick() {
    let params: any = {
      items: state.dataSource.items.map(ele => ({ itemId: ele.itemId, itemValue: ele.itemValue })),
      objectId: props.objectId,
      taskId: props.taskId,
    }

    check(params).then((res: any) => {
      //存在异常
      if (res.data.abnormalItemIds.length > 0) {
        show.value = true
      } else {
        onSubmit()
      }
    })
  }
  //提交
  function onSubmit() {
    let params: any = {
      attaches: state.dataSource.attaches.map(el => ({ attachUrl: el, attachType: 1 })),
      items: state.dataSource.items.map(ele => ({ itemId: ele.itemId, itemValue: ele.itemValue })),
      objectId: props.objectId,
      taskId: props.taskId,
    }

    submit(params).then((res: any) => {
      show.value = false
      uNotifyRef.value.show({
        type: 'success',
        title: '提交成功',
        duration: 800,
      })
      setTimeout(() => {
        router.back(-1)
      }, 850)
    })
  }
</script>

<style scoped lang="scss">
  .container {
    flex: 1;
    overflow: auto;

    .item-card {
      padding: 0 32rpx;
      margin: 20rpx 32rpx;
      background: #ffffff;
      border-radius: 16rpx;
    }
  }

  .bottom-button {
    margin: 0 32rpx;
    width: calc(100% - 64rpx);
    position: fixed;
    bottom: 30rpx;
  }

  :deep(.u-popup) {
    flex: unset;
  }
</style>
