<template>
  <view class="page-nav-top-common-bg2">
    <NavBar :title="state.workMonitorActive.projectName" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view
      :style="{ 'max-height': `calc(100vh - 44px - 30rpx - ${userStore.statusBarHeight}px)` }"
      scroll-y
      show-scrollbar
    >
      <view class="card-item" v-for="(el, idx) in state.workMonitorActive.devices" :key="idx">
        <view class="header">
          <view class="left">
            <view :class="`status ${el.isOnline === 1 ? 'online' : 'offline'}`">
              {{ el.isOnline === 1 ? '在线' : '离线' }}
            </view>
            <view class="title">{{ el.deviceName }}</view>
          </view>
          <view class="right" @click="onItemClick(el)">
            <text class="mr-[8rpx]">监测指标</text>
            <SvgIcon name="arrow-down" :class="{ arrow: el.deviceCode === state.activeDevice?.deviceCode }" />
          </view>
        </view>

        <view style="margin-top: 20rpx">
          <text style="color: #4e5969">设备编码:</text>
          {{ el.deviceCode }}
        </view>
        <view style="margin-top: 14rpx">
          <text style="color: #4e5969">最后上传时间:</text>
          {{ el.dateTime }}
        </view>

        <view v-if="el.deviceCode === state.activeDevice?.deviceCode" class="mt-[28rpx]">
          <Table :tableData="state.view" :column="tableColumn"></Table>
          <view
            v-if="el.deviceCode === state.activeDevice?.deviceCode && !state.view?.length"
            class="text-center mt-[18rpx] text-[#86909C]"
          >
            暂无数据
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, onBeforeUnmount } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import * as _ from 'lodash'
  import { useUserStore } from '@/store/modules/user'
  import Table from '@/components/MyTable/index.vue'
  import { getOptions } from '@/api/common.ts'

  const userStore = useUserStore()
  const router = useRouter()
  const uNotifyRef = ref()

  const tableColumn = [
    { title: '监测指标', dataIndex: 'indexName', width: 156 },
    {
      title: '监测值',
      dataIndex: 'indexValue',
      width: 140,
    },
    { title: '时间', dataIndex: 'dateTime' },
  ]

  const state = reactive({
    view: [],
    workMonitorActive: {},
    activeDevice: null,
    isOpen: [],
    conStatus: [],
    overStatus: [],
    faultStatus: [],
    runStatus: [],
  })

  onMounted(() => {
    state.workMonitorActive = uni.getStorageSync('workMonitorActive')
    getOptions('gateOpenStatus').then(res => {
      state.isOpen = res.data.map(el => ({
        ...el,
        label: el.value,
        value: el.key,
      }))
    })
    getOptions('gateConStatus').then(res => {
      state.conStatus = res.data.map(el => ({
        ...el,
        label: el.value,
        value: el.key,
      }))
    })
    getOptions('gateOverStatus').then(res => {
      state.overStatus = res.data.map(el => ({
        ...el,
        label: el.value,
        value: el.key,
      }))
    })
    getOptions('gateFaultStatus').then(res => {
      state.faultStatus = res.data.map(el => ({
        ...el,
        label: el.value,
        value: el.key,
      }))
    })
    getOptions('gateRunStatus').then(res => {
      state.runStatus = res.data.map(el => ({
        ...el,
        label: el.value,
        value: el.key,
      }))
    })
  })

  onBeforeUnmount(() => {
    uni.removeStorageSync('workMonitorActive')
  })

  const onItemClick = el => {
    if (state.activeDevice?.deviceCode === el.deviceCode) {
      state.activeDevice = null
      return
    }
    state.activeDevice = el
    state.view = el?.indexDetails.map(item => {
      return {
        ...item,
        indexValue: getLabel(item.indexValue, item),
      }
    })
    // state.activeDevice?.indexDetails = el?.indexDetails.map(item => {
    //   return {
    //     ...item,
    //     indexValue: getLabel(item.indexValue, item),
    //   }
    // })
  }
  const getLabel = (text, record) => {
    let label = ''
    if (record.indexName == '开关状态') {
      label = state.isOpen.find(item => item.value == record.indexValue)?.label
    } else if (record.indexName == '运行状态') {
      label = state.runStatus.find(item => item.value == record.indexValue)?.label
    } else if (record.indexName == '故障状态') {
      label = state.faultStatus.find(item => item.value == record.indexValue)?.label
    } else if (record.indexName == '检修状态') {
      label = state.overStatus.find(item => item.value == record.indexValue)?.label
    } else if (record.indexName == '控制状态') {
      label = state.conStatus.find(item => item.value == record.indexValue)?.label
    } else {
      label = record.indexValue
    }
    return label
  }
</script>

<style lang="scss" scoped>
  .card-item {
    padding: 24rpx;
    background: #ffffff;
    border-radius: 16rpx;
    margin: 0 30rpx 30rpx;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        display: flex;
        align-items: center;
        .status {
          padding: 4rpx 16rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          color: #ffffff;
          &.online {
            background: #e8ffea;
            border: 1px solid #00b42a;
            color: #00b42a;
          }
          &.offline {
            background: #f2f3f5;
            border: 1px solid #c9cdd4;
            color: #4e5969;
          }
        }
        .title {
          font-size: 32rpx;
          font-weight: 500;
          color: #1d2129;
          margin-left: 16rpx;
        }
      }
      .right {
        padding: 4rpx 16rpx;
        font-size: 28rpx;
        color: #86909c;
        border-radius: 8rpx;
        background: #f2f3f5;
        border: 1px solid #e5e6eb;
        color: #1d2129;
        display: flex;
        align-items: center;

        .arrow {
          transform: rotate(180deg);
        }
      }
    }
  }
</style>
