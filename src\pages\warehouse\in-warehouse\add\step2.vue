<template>
  <ZPaging
    ref="pagingRef"
    style="margin-top: 140px; height: calc(100vh - 44px - 320rpx)"
    v-model="state.list"
    :refresher-enabled="false"
  >
    <view class="item" v-for="(el, idx) in state.list" :key="idx">
      <view class="content">
        <view class="content-title">
          <view class="title">
            {{ el.goodsName || '-' }}
          </view>

          <view class="w-[32rpx] text-[#FF6F6A]" @click="onDelete(el, idx)"><u-icon name="trash"></u-icon></view>
        </view>

        <view class="indicate-item">
          <text>编码 {{ el.goodsCode }}</text>
          <text class="ml-[16rpx] mr-[16rpx] text-[28rpx] text-[#E5E6EB]">|</text>
          <text>规格 {{ el.goodsSpec }}</text>
        </view>
        <view class="indicate-item mt-[6rpx]">
          <text>型号 {{ el.goodsModel }}</text>
          <text class="ml-[16rpx] mr-[16rpx] text-[28rpx] text-[#E5E6EB]">|</text>
          <text>批号 {{ el.batchNo }}</text>
        </view>

        <view class="flex items-center justify-between my-[24rpx]">
          <view>
            账面数量
            <text style="font-size: 38rpx; color: #f76560">{{ el.stockQty }}</text>
          </view>
          <u-number-box v-model="el.inboundQty" @change="value => onNumberChange(value, idx)"></u-number-box>
        </view>
      </view>

      <view class="bottom">
        <SvgIcon name="icon-remark" class="mr-[8rpx]" />
        <u-input v-model="el.remark" :border="false" height="50" placeholder="备注点什么吧" />
      </view>
    </view>
  </ZPaging>

  <LimeFab :offset="{ x: -1, y: 400 }" heightGap="80">
    <view class="add-btn" @click="onAddClick">
      <u-icon name="plus" color="#ffffff" size="36"></u-icon>
    </view>
  </LimeFab>

  <u-popup
    v-model="state.isShowGoods"
    mode="bottom"
    class="confirm-popup"
    closeable
    @close="state.isShowGoods = false"
    border-radius="32"
  >
    <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">选择备件</view>
    <u-line color="#F2F3F5" />

    <view class="mx-[32rpx] pb-[32rpx]">
      <u-input
        class="mb-[32rpx]"
        v-model="state.searchVal"
        :border="true"
        @update:model-value="onSearchChange"
        clearable
      />

      <scroll-view style="height: 780rpx" scroll-y>
        <Table
          :tableData="tableData"
          :border="false"
          :isZebra="true"
          @RowClick="onRowClick"
          :column="tableColumns"
        ></Table>
      </scroll-view>
    </view>
  </u-popup>

  <view class="bottom-box px-[32rpx] py-[16rpx]">
    <view>
      <view class="flex items-center">
        合计:&nbsp;
        <text style="font-size: 38rpx; color: #f76560">{{ _.sum(state.list.map(el => el.inboundQty)) }}</text>
      </view>
      <view style="color: #86909c; font-size: 24rpx">种类:&nbsp;{{ state.list.length }}</view>
    </view>
    <view class="flex">
      <u-button
        :hair-line="false"
        style="
          height: 80rpx;
          width: 200rpx;
          padding: 0;
          margin-right: 16rpx;
          border-color: transparent;
          background-color: #f2f3f5;
        "
        @click="onPrevStep"
      >
        上一步
      </u-button>
      <u-button style="height: 80rpx; width: 200rpx; padding: 0; margin-right: 0" type="primary" @click="onNextStep">
        下一步
      </u-button>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs, computed } from 'vue'
  import * as _ from 'lodash'
  import Table from '@/components/MyTable/index.vue'
  import { getStockPage } from '../../services.ts'
  import LimeFab from '@/components/lime-fab/index.vue'

  const tableColumns = [
    { title: '备件名称', dataIndex: 'goodsName' },
    { title: '备件编码', dataIndex: 'goodsCode' },
    { title: '物料库存', dataIndex: 'stockQty' }
  ]

  const emits = defineEmits(['update:currentStep', 'onNext'])

  const props = defineProps(['baseInfo'])
  const pagingRef = ref(null)
  const state = reactive({
    list: [],
    tableList: [],
    isShowGoods: false,
    searchVal: undefined
  })

  const tableData = computed(() => state.tableList.filter(ele => ele.isSelect === false))

  watch(
    () => props.baseInfo.warehouseId,
    newVal => {
      nextTick(() => {
        state.list = []
        getStockList()
      })
    },
    { deep: true, immediate: true }
  )

  const onAddClick = () => {
    state.isShowGoods = true
  }

  const onDelete = (el, idx) => {
    state.list.splice(idx, 1)

    const index = state.tableList.findIndex(
      ele =>
        ele.goodsId === el.goodsId &&
        ele.goodsCode === el.goodsCode &&
        ele.batchNo === el.batchNo &&
        ele.goodsModel === el.goodsModel
    )
    state.tableList[index].isSelect = false
  }

  const onSearchChange = _.debounce(val => {
    getStockList(val)
  }, 500)
  function getStockList(val) {
    getStockPage({
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
      warehouseId: props.baseInfo.warehouseId,
      goodsName: val
    }).then(res => {
      state.tableList = (res.data?.data || []).map(el => ({
        ...el,
        isSelect: false,
        inboundQty: el.inboundQty || 0,
        price: el.price || 0
      }))
    })
  }

  const onRowClick = row => {
    state.list.push({ ...row, remark: '' })

    const index = state.tableList.findIndex(
      ele =>
        ele.goodsId === row.goodsId &&
        ele.goodsCode === row.goodsCode &&
        ele.batchNo === row.batchNo &&
        ele.goodsModel === row.goodsModel
    )

    state.tableList[index].isSelect = true

    pagingRef.value.scrollToBottom()

    state.isShowGoods = false
  }

  const onNumberChange = (val, idx) => {
    state.list[idx].price = Math.round(val.value * state.list[idx].unitPrice)
  }

  const onPrevStep = () => {
    emits('update:currentStep', 0)
  }
  const onNextStep = () => {
    emits('onNext', state.list)
    emits('update:currentStep', 2)
  }
</script>

<style lang="scss" scoped>
  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 20rpx 30rpx;
    overflow: hidden;

    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8rpx 32rpx;
      background: #f7f8fa;
    }

    .content {
      padding: 32rpx 32rpx 0;
      .content-title {
        font-size: 34rpx;
        color: #1d2129;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10rpx;
        .title {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .indicate-item {
        font-size: 28rpx;
        color: #86909c;
        display: flex;
        align-items: center;
      }
    }
  }

  .add-btn {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: linear-gradient(44deg, #3772ff 13%, #5ddcf5 82%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
