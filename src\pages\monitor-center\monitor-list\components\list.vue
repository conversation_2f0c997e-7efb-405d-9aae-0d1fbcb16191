<template>
  <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

  <!-- 搜索框 -->
  <view v-show="state.showSearch" class="search-input">
    <SvgIcon name="search" class="icon-search" />
    <u-input
      v-model="state.searchValue"
      placeholder="请输入站点名称"
      :border="true"
      @update:model-value="onSearchChange"
      clearable
    />
  </view>

  <u-dropdown
    ref="dropdownRef"
    height="60"
    title-size="28"
    menu-icon="arrow-down-fill"
    menu-icon-size="20"
    @open="onDropdownOpen"
    style="margin: 30rpx 0"
  >
    <u-dropdown-item :title="state.district.title">
      <scroll-view class="drop-content" scroll-y show-scrollbar>
        <DaTree
          ref="districtTreeRef"
          :data="state.districtTree"
          labelField="districtName"
          valueField="districtCode"
          defaultExpandAll
          @change="handleDistrictTreeChange"
          @expand="handleExpandChange"
        ></DaTree>
      </scroll-view>
    </u-dropdown-item>

    <u-dropdown-item :title="state.managementUnit.title">
      <scroll-view class="drop-content" scroll-y show-scrollbar>
        <DaTree
          ref="managementUnitTreeRef"
          :data="state.managementUnitTree"
          labelField="name"
          valueField="key"
          defaultExpandAll
          @change="handleManagementUnitTreeChange"
          @expand="handleExpandChange"
        ></DaTree>
      </scroll-view>
    </u-dropdown-item>

    <u-dropdown-item
      height="600"
      v-model="state.indexCode.value"
      :title="state.indexCode.title"
      :options="props.indexCodeOptions"
      @change="onIndexCodeChange"
    ></u-dropdown-item>
  </u-dropdown>

  <ZPaging ref="pagingRef" :style="{ marginTop: state.showSearch ? '180px' : '130px' }" v-model="state.list" @query="getList">
    <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
      <view class="header">
        <view class="header-item">
          <image src="~@/static/icons/icon-position.svg" class="w-[32rpx] h-[32rpx] mr-[8rpx]" />
          <text class="header-name">{{ el.districtFullName }}</text>
        </view>
        <text class="ml-[16rpx] mr-[16rpx] text-[28rpx] text-[#E5E6EB]">|</text>
        <view class="header-item">
          <image src="~@/static/icons/icon-river-system.svg" class="w-[32rpx] h-[32rpx] mr-[8rpx]" />
          <text class="header-name">{{ el.riverSystemName }}</text>
        </view>
      </view>

      <view class="content">
        <view class="site-title">
          <image :src="props.icon" alt="" class="w-[32rpx] h-[32rpx] mr-[16rpx]" />
          {{ el.siteName }}
        </view>
        <IndexCodes :indexCodes="el.indexCodes" :indexCodeOptions="props.indexCodeOptions" />
      </view>
    </view>
  </ZPaging>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { getDistrictTree, appPage, siteTerminal } from '../../services.ts'
  import { getDeptSiteTree } from '@/api/common.ts'
  import DaTree from '@/components/da-tree/index.vue'
  import IndexCodes from './indexCodes.vue'
  import SvgIcon from '@/components/SvgIcon/index.vue'

  const router = useRouter()
  const uNotifyRef = ref()
  const dropdownRef = ref()
  const districtTreeRef = ref()
  const managementUnitTreeRef = ref()
  const pagingRef = ref(null)

  const props = defineProps(['siteCategoryId', 'icon', 'indexCodeOptions', 'title'])
  const state = reactive({
    isMap: false,
    showSearch: false,
    searchValue: '',
    districtTree: [],
    district: { title: '按区域', value: undefined },
    managementUnitTree: [],
    managementUnit: { title: '按管理单位', value: undefined },
    indexCode: { title: '监测指标', value: undefined, preVal: undefined },
    list: [],
  })

  // 初始化数据
  getDistrictTree().then(res => {
    state.districtTree = res.data || []
  })

  // 过滤出所有"xxx所"的节点（不包含父元素和子元素）
  const filterManagementUnits = (data) => {
    const result = []

    const traverse = (nodes) => {
      if (!nodes || !Array.isArray(nodes)) return
      
      nodes.forEach(node => {
        // 检查节点名称是否以"所"结尾，且类型为category
        if (node.name && node.name.endsWith('所')) {
          // 创建新的节点，去掉children属性
          const filteredNode = {
            id: node.id,
            code: node.code,
            name: node.name,
            parentId: node.parentId,
            key: node.key,
            type: node.type,
            icon: node.icon,
            ext: node.ext,
            children: [], // 清空子元素
            isLeaf: true // 设置为叶子节点
          }
          result.push(filteredNode)
        }

        // 递归遍历子节点
        if (node.children && node.children.length > 0) {
          traverse(node.children)
        }
      })
    }

    traverse(data)
    return result
  }

  // 获取管理单位树的函数
  const getManagementUnitTree = (indexCode = null) => {
    const params = {
      labels: 2,
      indexCode: indexCode
    }

    getDeptSiteTree(params).then(res => {
      // 过滤数据，只保留"xxx所"的节点
      const filteredData = filterManagementUnits(res.data || [])
      state.managementUnitTree = filteredData

      // 如果当前选中的管理单位在新数据中不存在，则重置选择
      if (state.managementUnit.value && filteredData.length > 0) {
        const exists = filteredData.some(node => node.key === state.managementUnit.value)
        if (!exists) {
          state.managementUnit = { title: '按管理单位', value: undefined }
          pagingRef.value.reload()
          getList(1)
        }
      }
    })
  }

  // 在扁平化的管理单位列表中查找节点的辅助函数
  const findNodeInTree = (tree, targetValue) => {
    return tree.some(node => node.key === targetValue)
  }

  // 初始化管理单位树
  getManagementUnitTree()

  const onDropdownOpen = index => {
    if (index === 0) {
      nextTick(() => {
        if (state.district.value) {
          districtTreeRef.value?.setCheckedKeys(state.district.value, true)
        }
      })
    }
    if (index === 1) {
      nextTick(() => {
        if (state.managementUnit.value) {
          managementUnitTreeRef.value?.setCheckedKeys(state.managementUnit.value, true)
        }
      })
    }
  }

  function handleDistrictTreeChange(allSelectedKeys, currentItem) {
    if (allSelectedKeys === null) {
      // 取消选中，重置为默认状态
      state.district = { title: '按区域', value: undefined }
    } else {
      state.district = { title: currentItem.label, value: currentItem.key }
    }

    pagingRef.value.reload()
    getList(1)
    dropdownRef.value.close()
  }

  function handleManagementUnitTreeChange(allSelectedKeys, currentItem) {
    if (allSelectedKeys === null) {
      // 取消选中，重置为默认状态
      state.managementUnit = { title: '按管理单位', value: undefined }
    } else {
      state.managementUnit = { title: currentItem.label, value: currentItem.key }
    }

    pagingRef.value.reload()
    getList(1)
    dropdownRef.value.close()
  }

  function handleExpandChange(expand, currentItem) {}

  // 搜索功能
  const onSearchChange = (value) => {
    state.searchValue = value
    pagingRef.value.reload()
    getList(1)
  }

  function onIndexCodeChange(value) {
    console.log('=== onIndexCodeChange Debug ===')
    console.log('传入的value:', value)
    console.log('当前state.indexCode:', state.indexCode)
    console.log('props.indexCodeOptions:', props.indexCodeOptions)

    // 检查是否点击了相同的选项（取消选中）
    if (value === state.indexCode.preVal) {
      // 重置为默认状态
      state.indexCode = { title: '监测指标', value: undefined, preVal: undefined }
      value = undefined
      console.log('重置为默认状态')
    } else {
      // 选择新的选项
      const foundOption = props.indexCodeOptions.find(el => el.value == value)
      console.log('找到的选项:', foundOption)

      if (foundOption) {
        state.indexCode = {
          title: foundOption.label,
          value: value,
          preVal: value
        }
      } else {
        // 如果没找到选项，重置为默认状态
        state.indexCode = { title: '监测指标', value: undefined, preVal: undefined }
        value = undefined
      }
    }

    console.log('设置后的state.indexCode:', state.indexCode)

    // 根据监测指标重新获取管理单位树
    const indexCodeParam = (value === 'all' || !value) ? null : value
    console.log('indexCodeParam:', indexCodeParam)
    getManagementUnitTree(indexCodeParam)

    pagingRef.value.reload()
    getList(1)
  }

  const getList = (pageNo) => {
    console.log('=== getList Debug ===')
    console.log('state.indexCode:', state.indexCode)

    const params = {
      districtCode: state.district.value,
      indexCode: state.indexCode.value === 'all' ? undefined : state.indexCode.value,
      deptId: state.managementUnit.value,
      siteName: state.searchValue || undefined,
      siteCategoryId: +props.siteCategoryId,
      pageNum: pageNo || 1,
      pageSize: 10,
    }

    console.log('请求参数params:', params)

    appPage(params).then(res => {
      console.log('API响应:', res)
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }
  const isSiteIndex = async value => {
    const res = await siteTerminal({ siteId: value })
    if (res.data?.length > 0) {
      return true
    } else {
      return false
    }
  }
  const onItemClick = async el => {
    let isIndex = await isSiteIndex(el.siteId)
    if (isIndex == false) {
      uNotifyRef.value.show({
        type: 'warning',
        title: '未关联监测指标',
        duration: 800,
      })

      return
    }
    router.push({
      path: '/pages/monitor-center/monitor-detail/index',
      query: { title: props.title, siteId: el.siteId },
    })
  }

  // 暴露给父组件的方法和数据
  defineExpose({
    state,
    pagingRef,
    getList
  })
</script>

<style lang="scss" scoped>
  .search-input {
    padding: 10rpx 34rpx 15rpx;
    position: relative;
    .icon-search {
      position: absolute;
      left: 60rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    :deep(.u-input.u-input--border) {
      padding-left: 70rpx !important;
      border-radius: 100rpx;
    }
  }

  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      padding: 16rpx;
      background: #f7f8fa;

      .header-item {
        flex: 1;
        display: flex;
        align-items: center;

        .header-name {
          font-size: 28rpx;
          color: #4e5969;
          line-height: 28rpx;

          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }

    .content {
      padding: 22rpx 32rpx 12rpx;
      .site-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #1d2129;
        display: flex;
        align-items: center;
      }
    }
  }
  .drop-content {
    background: #fff;
    max-height: calc(100vh - 300rpx);
    padding: 10rpx 28rpx;
  }
  :deep(.u-dropdown__menu__item__text) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
</style>
