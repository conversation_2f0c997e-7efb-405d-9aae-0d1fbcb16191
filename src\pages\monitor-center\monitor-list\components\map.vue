<template>
  <view style="height: 100%">
    <u-popup v-model="showDetail" mode="bottom" :mask="false" border-radius="40">
      <view class="item">
        <view class="header">
          <view class="header-item">
            <image src="~@/static/icons/icon-position.svg" class="w-[32rpx] h-[32rpx] mr-[8rpx]" />
            <text class="header-name">{{ currentInfo.districtFullName }}</text>
          </view>
          <text class="ml-[16rpx] mr-[16rpx] text-[28rpx] text-[#E5E6EB]">|</text>
          <view class="header-item">
            <image src="~@/static/icons/icon-river-system.svg" class="w-[32rpx] h-[32rpx] mr-[8rpx]" />
            <text class="header-name">{{ currentInfo.riverSystemName }}</text>
          </view>

          <view
            class="w-[130rpx] whitespace-nowrap text-[#165DFF] text-[22rpx] text-left"
            @click="onDetailClick(currentInfo)"
          >
            查看详情
          </view>
        </view>

        <view class="content">
          <view class="site-title">
            <image :src="icon" alt="" class="w-[32rpx] h-[32rpx] mr-[16rpx]" />
            <view class="title">{{ currentInfo.siteName }}</view>
          </view>

          <scroll-view scroll-y="true" style="height: 500rpx; max-height: 500rpx">
            <IndexCodes
              :canTuck="false"
              :indexCodes="currentInfo.indexCodes"
              :indexCodeOptions="indexCodeOptions"
            />
          </scroll-view>
        </view>
      </view>
    </u-popup>

    <view
      id="mapBox"
      ref="mapBoxRef"
      :size="sizeState"
      :change:size="mapbox.dealSize"
      :option="center"
      :change:option="mapbox.createMap"
      :data="geojsonPoints"
      :change:data="mapbox.dealPoints"
      :current="currentInfo"
      :change:current="mapbox.addMarker"
      style="z-index: 2"
    ></view>
  </view>
</template>

<script>
  import * as _ from 'lodash'
  import { getValueByKey } from '@/api/common.ts'
  import { appPage } from '../../services.ts'
  import IndexCodes from './indexCodes.vue'
  import { useRouter } from 'uni-mini-router'

  export default {
    name: 'Map',
    components: { IndexCodes },
    props: ['isMap', 'siteCategoryId', 'icon', 'indexCodeOptions', 'title'],
    data() {
      return {
        center: null,
        sizeState: 0,

        geojsonPoints: null,
        geojsonPointsData: null,

        showDetail: false,
        currentInfo: {},
      }
    },
    watch: {
      isMap(newVal) {
        this.$nextTick(() => {
          this.sizeState += 1
        })
      },
    },

    mounted() {
      getValueByKey('patrolCentralPoint').then(res => {
        this.center = res.data.split(',').map(el => +el)
      })
      appPage({
        siteCategoryId: this.siteCategoryId,
        pageNum: 1,
        pageSize: 9999999,
      }).then(res => {
        const arr = (res.data?.data || [])
          .filter(el => el.longitude && el.latitude)
          .map(el => ({
            type: 'Feature',
            properties: { ...el, status: 'normal' },
            geometry: {
              type: 'Point',
              coordinates: [+el.longitude, +el.latitude],
            },
          }))

        this.geojsonPointsData = {
          type: 'FeatureCollection',
          features: arr,
        }
      })
    },

    onLoad() {},

    onUnload() {},

    methods: {},
    setup(props) {
      const router = useRouter()
      function onDetailClick(currentInfo) {
        router.push({
          path: '/pages/monitor-center/monitor-detail/index',
          query: { title: props.title, siteId: currentInfo.siteId },
        })
      }

      function onMapMounted() {
        this.geojsonPoints = this.geojsonPointsData
      }

      function onClickPoint(currentInfo) {
        this.showDetail = true
        this.currentInfo = { ...currentInfo, indexCodes: JSON.parse(currentInfo.indexCodes) }
        this.geojsonPoints = JSON.parse(
          JSON.stringify({
            ...this.geojsonPoints,
            features: this.geojsonPoints.features.map(el => ({
              ...el,
              properties: {
                ...el.properties,
                status: el.properties.siteId === currentInfo.siteId ? 'active' : 'normal',
              },
            })),
          }),
        )
      }
      return { onDetailClick, onMapMounted, onClickPoint }
    },
  }
</script>

<script module="mapbox" lang="renderjs">
  import mapboxgl from 'mapbox-gl'
  import * as turf from '@turf/turf'
  import { mapBoundGeo } from '@/utils/mapBounds.js'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import { mapConfig, accessToken } from '@/utils/mapConfig'

  export default {
    data() {
      return {
        mapIns: null,
        markerIns: null
      }
    },
  	methods: {
  		createMap(center) {
        if(!center) return;
  			mapboxgl.accessToken = accessToken;
  			this.mapIns = new mapboxgl.Map({...mapConfig, center: center || mapConfig.center});

        this.mapIns.on('load', () => {
          this.$ownerInstance.callMethod('onMapMounted')
          // this.onMapMounted()
        })

        this.mapIns.on('style.load', () => {
          // this.$ownerInstance.callMethod('onStyleLoad')
          // this.onStyleLoad()
        })

  		},

      dealSize() {
        this.mapIns?.resize()
      },

      addMarker(currentInfo) {
        this.markerIns?.remove()

        this.markerIns = new mapboxgl.Marker({
            color: '#3D93FD',
            scale: 0.8,
          }).setLngLat({lng: +currentInfo.longitude, lat: +currentInfo.latitude}).addTo(this.mapIns)
      },

      dealPoints(geojsonPoints) {
        if(!geojsonPoints) return

        if (this.mapIns.getSource('all-point')) {
          clearSourceAndLayer(this.mapIns, ['all-point'], ['all-point'])
          mapBoundGeo(geojsonPoints, this.mapIns, { top: 50, bottom: 400, left: 50, right: 50 })
        } else {
          mapBoundGeo(geojsonPoints, this.mapIns)
        }


        this.mapIns.addSource('all-point', { type: 'geojson', data: geojsonPoints })

        this.mapIns.addLayer({
          id: 'all-point',
          type: 'circle',
          source: 'all-point',
          paint: {
            'circle-color': [
              'match',
              ['get', 'status'],
              'normal',
              '#165DFF',
              'active',
              'transparent',

              '#165DFF',
              ],
            'circle-radius': ['interpolate', ['exponential', 1], ['zoom'], 5, 5, 14, 12],
            'circle-stroke-width': 1,
            'circle-stroke-color': [
              'match',
              ['get', 'status'],
              'normal',
              '#ffffff',
              'active',
              'transparent',

              '#ffffff',
              ],
          }
        })
        .on('click', 'all-point', (e) => {
          const currentInfo = e.features[0].properties
          this.$ownerInstance.callMethod('onClickPoint', currentInfo)
          // this.onClickPoint(currentInfo)
        })
      },
  	}
  }
</script>

<style lang="scss" scoped>
  #mapBox {
    width: 100%;
    height: 100%;
  }

  .item {
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      padding: 16rpx;
      background: #f7f8fa;

      .header-item {
        flex: 1;
        display: flex;
        align-items: center;

        .header-name {
          font-size: 28rpx;
          color: #4e5969;
          line-height: 28rpx;

          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }

    .content {
      padding: 22rpx 32rpx 12rpx;
      .site-title {
        font-size: 36rpx;
        color: #1d2129;
        display: flex;
        align-items: center;
        .title {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  /* :deep(.u-drawer) {
    left: unset;
    top: unset;
    height: 600rpx;
    width: 100%;
    .u-mask {
      display: none;
    }
  } */
  /* :deep(.u-mask.u-mask-zoom) {
    pointer-events: none;
  } */
</style>
