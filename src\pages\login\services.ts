import { request } from '@/utils/request'

// 用户名密码登录
export interface LoginType {
  appId: string
  username: string
  password: string
}
export function login(data: LoginType) {
  return request({
    url: '/sys/login',
    method: 'post',
    data,
    headers: {
      globalLoading: true
    }
  })
}

// 获取验证码
export function getMobileCaptchaLogin(parameter: any) {
  return request({
    url: '/sys/mobileCaptchaLogin/getCaptcha',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: 'mobile=' + parameter
    //parameter
  })
}
//验证码登录
export function captchaLogin(data: LoginType) {
  return request({
    url: '/sys/mobileCaptchaLogin',
    method: 'post',
    data,
    headers: {
      globalLoading: true
    }
  })
}
