import { request } from '@/utils/request'

// 水利对象分类-根据编码获取树
export function getTreeByCode(data: any) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// APP水雨情列表
export function appPage(data: any) {
  return request({
    url: '/war/app/page',
    method: 'post',
    data,
  })
}

// 行政区划-获取树
export function getDistrictTree(data: any) {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 站点下的终端
export function siteTerminal(data: any) {
  return request({
    url: '/base/terminal/siteTerminal',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 站点终端列表--全部指标
export function allList(data: any) {
  return request({
    url: '/war/rainWater/allList',
    method: 'post',
    data,
  })
}

// 站点终端统计
export function statistics(data: any) {
  return request({
    url: '/war/rainWater/statistics',
    method: 'post',
    data,
  })
}

// 站点终端列表--雨量 水位 流量指标
export function rainWaterList(data: any) {
  return request({
    url: '/war/rainWater/list',
    method: 'post',
    data,
  })
}

// 综合监测指标
export function indicatorList(data: any) {
  return request({
    url: '/dw/overall/list',
    method: 'post',
    data,
  })
}
