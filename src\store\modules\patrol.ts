import { defineStore } from 'pinia'
import { getPointAppear, getValueBy<PERSON>ey } from '@/api/common.ts'
import dayjs from 'dayjs'
import * as _ from 'lodash'

export const usePatrolStore = defineStore('patrol', {
  unistorage: true, // 开启后对 state 的数据读写都将持久化

  state() {
    return {
      patrolType: 0,
      timer: null,
      frequencyTime: 0,
      preLocation: null,
    }
  },
  actions: {
    setPatrolType(patrolType: any) {
      this.patrolType = patrolType
    },

    setFrequencyTime(callback: any) {
      getValueByKey('patrolReportingFrequency').then(res => {
        this.frequencyTime = +res.data
        callback()
      })
    },

    setPatrolStart(taskId: any, isInterval: boolean, callback: any) {
      uni.showLoading({
        title: '获取点位中',
      })

      clearInterval(this.timer)

      const getCurrentLocation = () => {
        uni.getLocation({
          type: 'wgs84',
          accuracy: 'best',
          isHighAccuracy: true,
          highAccuracyExpireTime: 4000,
          success: function () {
            uni.getLocation({
              type: 'wgs84',
              accuracy: 'best',
              isHighAccuracy: true,
              highAccuracyExpireTime: 4000,
              success: res => {
                // console.log('当前位置的经纬度：' + [res.longitude, res.latitude])
                uni.hideLoading()
                let lo = [res.longitude, res.latitude]

                this.preLocation = { longitude: lo[0], latitude: lo[1] }

                if (!this.preLocation?.longitude || !this.preLocation?.latitude) return

                getPointAppear({
                  ...this.preLocation,
                  taskId,
                  uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                }).then(res => {
                  callback()
                })
              },
              fail: () => {
                uni.hideLoading()
                if (!this.preLocation?.longitude || !this.preLocation?.latitude) return
                getPointAppear({
                  ...this.preLocation,
                  taskId,
                  uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                }).then(res => {
                  callback()
                })
              },
            })
          },
          fail: () => {
            uni.hideLoading()
            if (!this.preLocation?.longitude || !this.preLocation?.latitude) return
            getPointAppear({
              ...this.preLocation,
              taskId,
              uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            }).then(res => {
              callback()
            })
          },
        })
      }

      getCurrentLocation()

      if (isInterval) {
        this.timer = setInterval(getCurrentLocation, this.frequencyTime * 1000)
      }
    },
    finishPatrolInterval() {
      clearInterval(this.timer)
      uni.hideLoading()
      this.timer = null
      this.preLocation = null
    },
  },
})
