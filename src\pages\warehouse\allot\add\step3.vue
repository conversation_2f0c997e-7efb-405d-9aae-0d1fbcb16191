<template>
  <scroll-view class="container" scroll-y show-scrollbar>
    <u-section
      title="基本信息"
      class="mt-[30rpx] ml-[32rpx]"
      font-size="34"
      line-color="#3772FF"
      :right="false"
    ></u-section>
    <view class="com-card">
      <u-cell-group :border="false">
        <u-cell-item
          title="调出仓库"
          :arrow="false"
          :value="props.baseInfo.outboundWarehouseName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="调入仓库"
          :arrow="false"
          :value="props.baseInfo.inboundWarehouseName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>

        <u-cell-item
          title="调拨时间"
          :arrow="false"
          :value="props.baseInfo.allotTime || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="调拨人"
          :arrow="false"
          :value="props.baseInfo.assignerName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="调拨摘要"
          :arrow="false"
          :value="props.baseInfo.remark || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>
    </view>

    <view class="list-content">
      <u-section
        title="库存信息"
        class="mt-[30rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>

      <Table :tableData="props.goodsList" :column="tableColumns">
        <template v-if="props.goodsList?.length" #totalRow>
          <u-tr>
            <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
              <text v-if="ele.title === '序号'">合计</text>
              <text v-else-if="ele.dataIndex === 'allotQty'">
                {{ _.sum(props.goodsList.map(el => el.allotQty)) }}
              </text>
              <text v-else-if="ele.dataIndex === 'price'">{{ _.sum(props.goodsList.map(el => el.price)) }}</text>
              <text v-else>　</text>
            </u-td>
          </u-tr>
        </template>

        <template #action="{ record }">
          <view class="text-[#165DFF]" @click.stop="onDetailClick(record)">查看</view>
        </template>
      </Table>
    </view>
  </scroll-view>

  <view class="bottom-box px-[32rpx] py-[16rpx]">
    <view></view>
    <view class="flex">
      <u-button
        :hair-line="false"
        style="
          height: 80rpx;
          width: 200rpx;
          padding: 0;
          margin-right: 16rpx;
          border-color: transparent;
          background-color: #f2f3f5;
        "
        @click="onPrevStep"
      >
        上一步
      </u-button>
      <u-button style="height: 80rpx; width: 200rpx; padding: 0; margin-right: 0" type="primary" @click="onSubmit">
        提交
      </u-button>
    </view>
  </view>

  <ConfirmPopup
    :show="state.showConfirmSubmit"
    @update:show="state.showConfirmSubmit = val"
    @onConfirm="onConfirm"
    popupTitle="提交调拨"
    title="确认提交调拨"
    description="确认后,将不能修改填写内容"
  />

  <u-popup
    v-model="state.isShowDetail"
    mode="bottom"
    class="confirm-popup"
    closeable
    @close="state.isShowDetail = false"
    border-radius="32"
  >
    <view class="text-[#1D2129] text-[16px] text-center font-semibold leading-[95rpx]">物料详情</view>
    <u-line color="#F2F3F5" />

    <view class="px-[40rpx] py-[32rpx]">
      <u-cell-group :border="false">
        <u-cell-item
          title="备件编码"
          :arrow="false"
          :value="state.currentRow.goodsCode || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件名称"
          :arrow="false"
          :value="state.currentRow.goodsName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件规格"
          :arrow="false"
          :value="state.currentRow.goodsSpec || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="备件型号"
          :arrow="false"
          :value="state.currentRow.goodsModel || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="供应商"
          :arrow="false"
          :value="state.currentRow.supplier || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>

      <view class="grid grid-cols-2 gap-[20rpx] mt-[40rpx]">
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">最低库存</text>
          <text class="text-[15px]">{{ state.currentRow.minStock || '-' }}</text>
        </view>
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">最高库存</text>
          <text class="text-[15px]">{{ state.currentRow.maxStock || '-' }}</text>
        </view>
        <view class="flex flex-col items-center bg-[#F7F8FA] rounded-[16rpx] py-[20rpx]">
          <text class="text-[#86909C]">单价</text>
          <text class="text-[15px]">
            {{ state.currentRow.unitPrice }}
          </text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs, computed } from 'vue'
  import * as _ from 'lodash'
  import { useRouter } from 'uni-mini-router'
  import Table from '@/components/MyTable/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import { allotAdd } from '../../services.ts'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '备件编码', dataIndex: 'goodsCode', width: 120 },
    { title: '备件名称', dataIndex: 'goodsName', width: 200 },
    { title: '当前库存', dataIndex: 'stockQty', width: 140 },
    { title: '调拨数量', dataIndex: 'allotQty', width: 140 },
    { title: '备件详情', slotName: 'action', width: 140 }
  ]

  const router = useRouter()
  const props = defineProps(['baseInfo', 'goodsList'])
  const emits = defineEmits(['update:currentStep'])

  const state = reactive({
    showConfirmSubmit: false,
    isShowDetail: false,
    currentRow: {}
  })

  const onDetailClick = record => {
    state.currentRow = record
    state.isShowDetail = true
  }

  const onPrevStep = () => {
    emits('update:currentStep', 1)
  }
  const onSubmit = () => {
    state.showConfirmSubmit = true
  }

  const onConfirm = () => {
    allotAdd({ ...props.baseInfo, allotRecords: props.goodsList })
      .then(res => {
        state.showConfirmSubmit = false
        router.push({
          path: '/pages/middle-page/success/index',
          query: {
            title: '调拨完成',
            desc: `新增调拨成功，调拨单号为:${res.data}`,
            backText: '后返回调拨管理列表',
            btnText: '立即返回',
            url: 2
          }
        })
      })
      .catch(err => {
        state.showConfirmSubmit = false
        router.push({
          path: '/pages/middle-page/failure/index',
          query: {
            title: '提交失败',
            desc: '调拨失败,失败原因为:',
            errText: err,
            btnText: '重新新增调拨',
            url: 1,
            otherJumpText: '回到调拨管理列表,',
            otherUrl: 2
          }
        })
      })
  }
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 32rpx;
    max-height: calc(100vh - 44px - 300rpx);
  }

  .list-content {
    background-color: #fff;
    padding-top: 10rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: 410rpx;
  }
  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
