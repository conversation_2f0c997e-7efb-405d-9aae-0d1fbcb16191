<template>
  <view style="height: 500rpx; position: relative">
    <ECharts ref="chart" @finished="init"></ECharts>
    <!-- <view
      class="customTooltips"
      :style="{ left: position[0] + 'px', top: position[1] + 'px' }"
      v-if="params.length && position.length && showTip"
    >
      <view v-for="item in params">
        <view>{{ item.seriesName }}</view>
        <view>
          <view>{{ item.value[1] }}</view>
          <view>{{ item.axisValue }}</view>
        </view>
      </view>
    </view> -->
  </view>
</template>

<script>
  import * as echarts from 'echarts/dist/echarts.esm'
  import dayjs from 'dayjs'

  const colors = [
    '#4080FF',
    '#B5E241',
    '#4363d8',
    '#911eb4',
    '#00A316',
    '#fabed4',
    '#469990',
    '#dcbeff',
    '#9A6324',
    '#aaffc3',
    '#808000',
    '#000075',
    '#a9a9a9'
  ]

  //hex -> rgba
  function hexToRgba(hex, opacity) {
    return (
      'rgba(' +
      parseInt('0x' + hex.slice(1, 3)) +
      ',' +
      parseInt('0x' + hex.slice(3, 5)) +
      ',' +
      parseInt('0x' + hex.slice(5, 7)) +
      ',' +
      opacity +
      ')'
    )
  }

  export default {
    props: ['dataSource', 'yAxisConfig'],
    data() {
      return {
        showTip: false,
        chartIns: null,
        position: [],
        params: []
      }
    },
    watch: {
      dataSource(newVal) {
        this.chartIns.setOption(this.getOptions(newVal))
      },
      yAxisConfig() {
        this.chartIns.setOption(this.getOptions(this.dataSource))
      }
    },
    methods: {
      init() {
        this.$refs.chart.init(echarts, chart => {
          this.chartIns = chart

          chart.setOption(this.getOptions(this.dataSource || []))

          // 监听tooltip显示事件
          chart.on('showTip', params => {
            this.showTip = true
          })
          chart.on('hideTip', params => {
            setTimeout(() => {
              this.showTip = false
            }, 300)
          })
        })
      },
      getOptions(data) {
        return {
          color: colors,
          tooltip: {
            trigger: 'axis',
            // shadowBlur: 0,
            textStyle: {
              textShadowBlur: 0
            },
            renderMode: 'richText',
            position: (point, params, dom, rect, size) => {
              // 假设自定义的tooltips尺寸
              const box = [170, 170]
              // 偏移
              const offsetX = point[0] < size.viewSize[0] / 2 ? 20 : -box[0] - 20
              const offsetY = point[1] < size.viewSize[1] / 2 ? 20 : -box[1] - 20
              const x = point[0] + offsetX
              const y = point[1] + offsetY

              this.position = [x, y]
              this.params = params
            }
            // formatter: (params, ticket, callback) => {}
          },
          legend: { show: false },
          grid: {
            left: '3%',
            right: '4%',
            top: '10%',
            bottom: '8%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              color: '#86909C',
              formatter: params => {
                return params.length == 19 ? dayjs(params).format('HH:mm') : params
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#86909C'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#dddddd'
              }
            },
            ...(this.yAxisConfig || {})
          },
          series: data.map((item, i) => {
            return {
              type: 'line',
              showBackground: true,
              smooth: true,
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              name: item.name,
              stack: 'something',
              emphasis: {
                focus: 'series'
              },
              areaStyle: {
                opacity: 0.4,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: hexToRgba(colors[i % colors.length], 0.3)
                  },
                  {
                    offset: 1,
                    color: '#fff'
                  }
                ])
              },
              data: item.data
            }
          })
        }
      }
    }
  }
</script>

<style>
  .customTooltips {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    padding: 20rpx;
  }
</style>
