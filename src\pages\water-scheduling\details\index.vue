<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="用水调度详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <view class="line-title">用水调度</view>
        <view class="row">
          <text class="label">调度类型</text>
          <text class="value">
            {{ schedulingTypeOptions.find(el => el.value == state.schDetails?.schedulingType)?.label }}
          </text>
        </view>
        <view class="row">
          <text class="label">灌溉范围</text>
          <text class="value">{{ state.schDetails?.districtCodeName }}</text>
        </view>
        <view class="row">
          <text class="label">调度水量</text>
          <text class="value">{{ state.schDetails?.waterValue }}万m³</text>
        </view>
        <view class="row">
          <text class="label">调度时间</text>
          <text class="value">{{ state.schDetails?.waterDate || '-' }}</text>
        </view>
        <view class="row">
          <text class="label">预计调度时长</text>
          <text class="value">
            {{ state.schDetails?.schedulingTime == null ? '-' : state.schDetails?.schedulingTime + 'h' }}
          </text>
        </view>
        <view class="row">
          <text class="label">备注</text>
          <text class="value">{{ state.schDetails?.remark || '-' }}</text>
        </view>
      </view>
      <view class="com-card">
        <view class="line-title">调度进度</view>

        <view class="time-line-box">
          <view class="time-line-group">
            <view
              class="time-line-item"
              :class="{ currentStatus: state.schDetails?.schedulingStatus === 1, bluePoint: state.step1 }"
            >
              <view class="time-line-title">下发调度单</view>
              <view class="time-line-row">
                {{ state.newDeptNames?.join(',') }}
              </view>
            </view>
            <view
              class="time-line-item"
              :class="{ currentStatus: state.schDetails?.schedulingStatus === 2, bluePoint: state.step2 }"
            >
              <view class="time-line-title">用水调度</view>
              <view>
                <view class="time-line-row">
                  <text class="time-line-label">放水开始时间：</text>
                  <text class="time-line-value">
                    {{ state.schDetails?.startDate }}
                  </text>
                </view>
                <view class="time-line-row">
                  <text class="time-line-label">放水结束时间：</text>
                  <text class="time-line-value">
                    {{
                      state.schDetails?.endDate ? state.schDetails?.endDate : state.schDetails?.startDate ? '至今' : ''
                    }}
                  </text>
                </view>
                <view class="time-line-row">
                  <text class="time-line-label">当前调度水量：</text>
                  <text class="time-line-value">
                    {{ state.schDetails?.thisWaterValue != null ? state.schDetails?.thisWaterValue + '万m³' : '-' }}
                  </text>
                </view>
              </view>
            </view>
            <view
              class="time-line-item"
              :class="{ currentStatus: state.schDetails?.schedulingStatus === 3, bluePoint: state.step3 }"
            >
              <view class="time-line-title">水量复核</view>
              <view>
                <view class="time-line-row">
                  <text class="time-line-label">水库放水量：</text>
                  <text class="time-line-value">
                    {{ state.schDetails?.waterFlow != null ? state.schDetails?.waterFlow + '万m³' : '-' }}
                  </text>
                </view>
                <view>
                  <view class="time-line-row">田间用水量：</view>
                  <view class="field-water-table" v-if="state.schDetails?.fieldWaterFlows?.length > 0">
                    <view class="field-tr" v-for="(item, index) in state.schDetails?.fieldWaterFlows" :key="index">
                      <text>{{ item.deptName }}</text>
                      <text class="value">{{ item.waterLevel }}万m³</text>
                    </view>
                    <view class="field-tr-total">
                      <text>总计</text>
                      <text class="value">{{ state.waterFlowTotal }}万m³</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view class="time-line-item" :class="{ currentStatus: state.schDetails?.schedulingStatus === 4 }">
              <view class="time-line-title">调度完成</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <u-button
      class="btn-primary"
      type="primary"
      v-if="state.curDeptId == 10021 && state.schDetails?.schedulingStatus == 1"
      @click="onClickScheduling('start', state.schDetails)"
    >
      开始调度
    </u-button>
    <u-button
      class="btn-primary"
      type="primary"
      v-if="state.curDeptId == 10021 && state.schDetails?.schedulingStatus == 2"
      @click="onClickScheduling('end', state.schDetails)"
    >
      结束调度
    </u-button>
    <u-button
      v-if="
        state.schDetails?.deptIds?.length > 0 &&
        state.schDetails?.deptIds?.includes(state.curDeptId) &&
        state.schDetails?.schedulingStatus == 3
      "
      class="btn-primary"
      type="primary"
      @click="onClickScheduling('review', state.schDetails)"
    >
      水量复核
    </u-button>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { getWaterLedgerDetails } from '../services'
  import * as _ from 'lodash'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const props = defineProps(['consumptionId'])

  const schedulingTypeOptions = [
    { label: '灌溉调度', value: 1 },
    { label: '防汛调度', value: 2 },
  ]

  const state = reactive({
    schDetails: {},
    isShowDetail: false,
    originalDeptName: ['水库大坝管理所', '水电站管理所'],
    newDeptNames: [],

    step1: false,
    step2: false,
    step3: false,
    waterFlowTotal: 0,

    curDeptId: JSON.parse(uni.getStorageSync('user'))?.loginOrg?.loginOrgId,
  })

  onMounted(() => {
    getDetails()
  })

  const getDetails = () => {
    getWaterLedgerDetails({
      consumptionId: props.consumptionId,
    }).then(res => {
      state.schDetails = res?.data
      const allDeptNames =
        res?.data?.deptNames == null ? state.originalDeptName : state.originalDeptName.concat(res?.data?.deptNames)
      state.newDeptNames = [...new Set(allDeptNames)]
      state.waterFlowTotal = res?.data?.fieldWaterFlows?.reduce(
        (accumulator, currentValue) => accumulator + currentValue.waterLevel,
        0,
      )
      //
      if (res?.data?.schedulingStatus === 2) {
        state.step1 = true
      } else if (res?.data?.schedulingStatus === 3) {
        state.step1 = true
        state.step2 = true
      } else if (res?.data?.schedulingStatus === 4) {
        state.step1 = true
        state.step2 = true
        state.step3 = true
      }
    })
  }

  const onClickScheduling = (source, el) => {
    router.push({
      path: '/pages/water-scheduling/to-review/index',
      query: {
        consumptionId: el.consumptionId,
        source: source,
        startDate: el.startDate,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .line-title {
    font-weight: 500;
    font-size: 36rpx;
    color: #1d2129;
    padding-left: 20rpx;
    position: relative;
    margin-bottom: 20rpx;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 11rpx;
      width: 6rpx;
      height: 28rpx;
      background: #3772ff;
      border-radius: 20rpx;
    }
  }

  .row {
    margin-bottom: 26rpx;
    display: flex;
    .label {
      font-weight: 400;
      font-size: 28rpx;
      color: #4e5969;
      display: inline-block;
      width: 100px;
    }
    .value {
      font-weight: 400;
      font-size: 30rpx;
      color: #1d2129;
    }
  }
  .time-line-box {
    display: flex;
    padding: 30rpx 20rpx 30rpx 28rpx;
    .time-line-group {
      flex: 1;
      .time-line-item {
        position: relative;
        padding-left: 50rpx;
        padding-bottom: 20rpx;
        &::before {
          content: '';
          position: absolute;
          left: -16rpx;
          top: 4rpx;
          z-index: 10;
          width: 24rpx;
          height: 24rpx;
          border-radius: 24rpx;
          background: #ffffff;
          border: 2rpx solid #c9cdd4;
        }
        &.currentStatus::before {
          left: -18rpx;
          background: #165dff;
          box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.3);
          border: 4rpx solid #ffffff;
        }
        &.bluePoint::before {
          background: #ffffff;
          border: 2rpx solid #165dff;
        }

        &::after {
          content: '';
          position: absolute;
          left: -6rpx;
          top: 23rpx;
          width: 6rpx;
          height: 100%;
          background: #e8f3ff;
          z-index: 1;
        }
        &:nth-last-child(1)::after {
          background: transparent;
        }
        &.bluePoint::after {
          background: #165dff;
        }
      }
      .time-line-title {
        font-weight: 500;
        font-size: 30rpx;
        color: #1d2129;
        margin-bottom: 12rpx;
      }
      .time-line-row {
        display: flex;
        margin-bottom: 12rpx;
        .time-line-label {
          min-width: 140rpx;
          color: #4e5969;
        }
        .time-line-value {
          color: #1d2129;
        }
      }
    }
    .field-water-table {
      border: 1px solid #dee0e3;
      margin-bottom: 18rpx;
      .field-tr {
        border-bottom: 1px solid #dee0e3;
      }
      .field-tr,
      .field-tr-total {
        display: flex;
        text {
          flex: 1;
          padding: 4px 6px;
        }
        .value {
          width: 30%;
          border-left: 1px solid #dee0e3;
        }
      }
    }
  }
  .btn-primary {
    width: 90%;
  }
</style>
