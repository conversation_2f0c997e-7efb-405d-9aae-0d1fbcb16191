<template>
  <view class="edit-success">
    <view class="content">
      <view class="success"></view>
      <text class="text">{{ props.title }}</text>
      <view v-if="props.desc" class="text-[#86909C] text-center">{{ props.desc }}</view>
      <text class="back">
        <text class="text-[#165DFF]">{{ num }}秒</text>
        {{ props.backText }}
      </text>
    </view>

    <u-button class="submit-btn" type="primary" size="default" @click="onBtnClick">{{ props.btnText }}</u-button>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'

  const userStore = useUserStore()
  const user = userStore.user
  const router = useRouter()

  const timer = ref()
  const num = ref(3)

  const props = defineProps({
    title: { default: 'XX完成' },
    desc: `XXXX成功，XXXXXX:XXXX`,
    backText: '后返回XXXX',
    btnText: '立即返回',
    jumpType: 'push',
    url: -1
  })

  onLoad(() => {
    timer.value = setInterval(() => {
      num.value = num.value - 1
      if (num.value === 0) {
        clearInterval(timer.value)
        if (Number.isInteger(+props.url)) {
          uni.navigateBack({ delta: +props.url })
        } else {
          router[props.jumpType](props.url)
        }
      }
    }, 1000)
  })

  const onBtnClick = () => {
    clearInterval(timer.value)
    if (Number.isInteger(+props.url)) {
      uni.navigateBack({ delta: +props.url })
    } else {
      router[props.jumpType](props.url)
    }
  }
</script>

<style lang="scss" scoped>
  .edit-success {
    background: url('~@/static/images/details-top-bg.png') no-repeat;
    background-size: 100%;
    height: 100%;
  }

  .content {
    padding: 250rpx 40rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .success {
      width: 200rpx;
      height: 200rpx;
      background: url('@/static/images/status-success-bg.png') no-repeat;
      background-size: 100% 100%;
    }

    .text {
      margin-top: 20rpx;
      font-size: 44rpx;
      font-weight: 600;
      font-family: PingFang SC-Medium, PingFang SC;
      color: #1d2129;
      line-height: 80rpx;
    }

    .back {
      font-size: 28rpx;
      font-weight: 300;
      color: #4e5969;
      line-height: 52rpx;
    }
  }

  .submit-btn {
    margin-top: 60rpx;
    height: 116rpx;
    text-align: center;
    width: 284rpx;
    border-radius: 16rpx;

    :deep(.u-button__text) {
      font-family: PingFang SC-Medium, PingFang SC;
    }
  }
</style>
