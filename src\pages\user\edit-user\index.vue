<template>
  <NavBar title="个人信息" />

  <view class="page-top-nav edit-user">
    <view class="com-card">
      <u-cell-group :border="false">
        <u-cell-item title="用户名" :arrow="false" :value="user.username || ''"></u-cell-item>

        <u-cell-item
          title="头像"
          @click="onItemClick(`/pages/user/edit-user/edit-user-info/index?avatar=${user.avatar}`)"
        >
          <template #right-icon>
            <image class="avatar" v-if="user.avatar" :src="user.avatar" />
            <view class="avatar" v-else>{{ user.name?.slice(user.name.length - 2) }}</view>
          </template>
        </u-cell-item>
        <u-cell-item
          title="姓名"
          @click="onItemClick(`/pages/user/edit-user/edit-user-info/index?name=${user.name}`)"
          :value="user.name || ''"
        ></u-cell-item>
        <u-cell-item
          title="邮箱地址"
          @click="onItemClick(`/pages/user/edit-user/edit-user-info/index?email=${user.email}`)"
          :value="user.email || ''"
        ></u-cell-item>
        <u-cell-item
          title="手机号"
          @click="onItemClick(`/pages/user/edit-user/edit-user-info/index?mobile=${user.mobile}`)"
          :value="user.mobile || ''"
        ></u-cell-item>
      </u-cell-group>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import defaultPhoto from '@/static/images/user-photo.png'

  const userStore = useUserStore()
  const router = useRouter()

  const user = ref({ ...userStore.user })

  onLoad(() => {})

  watch(
    () => router.route.value,
    newVal => {
      if (newVal.name === 'editUser') {
        user.value = userStore.user
      }
    },
    { immediate: true }
  )

  const onItemClick = url => {
    router.push(url)
  }
</script>

<style lang="scss" scoped>
  .edit-user {
    padding-top: 0rpx;

    .avatar {
      width: 68rpx;
      height: 68rpx;
      box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(55, 114, 255, 0.24);
      border: 4rpx solid #ffffff;
      border-radius: 50%;

      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 26rpx;
      background: #4080ff;
    }
  }
</style>
