<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="发电调度" :background="{ backgroundColor: 'transparent' }">
      <template #right>
        <text class="add-btn" @click="add" v-if="!state.isManagementBureau">+</text>
      </template>
    </NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="relative">
      <u-dropdown
        ref="dropdownRef"
        height="80"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 0"
        @open="onDropdownOpen"
      >
        <u-dropdown-item
          height="700"
          v-model="state.dispatchType.value"
          :title="state.dispatchType.title"
          :options="dispatchTypeOptions"
          @change="onChangeType"
        ></u-dropdown-item>
        <u-dropdown-item :title="state.timePicker.title"></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <ZPaging
    ref="pagingRef"
    :style="{ marginTop: `calc(44px + ${userStore.statusBarHeight}px + 100rpx)`, paddingTop: '28rpx', zIndex: 10 }"
    v-model="state.list"
    @query="getList"
  >
    <view class="com-card" v-for="(el, idx) in state.list" :key="idx">
      <view class="card-row top">
        <view
          class="status"
          :class="{
            waiting: el?.dispatchType == 1,
            finish: el?.dispatchType == 2,
          }"
        >
          {{ dispatchTypeOptions?.find(ele => ele.value == el.dispatchType)?.label }}
        </view>
        <view class="number value">{{ el.serialNumber }}</view>
      </view>
      <view class="card-row">
        <text class="label">开启设备:</text>
        <text class="value text-ellipsis">{{ el.deviceNames }}</text>
      </view>

      <view class="card-row">
        <text class="label">总计开启时长:</text>
        <text class="value text-ellipsis">{{ el.openHour == null ? '--' : el.openHour + '小时' }}</text>
      </view>
      <view class="card-row">
        <text class="date">{{ el.dispatchDate?.substring(0, 10) }}</text>
        <view class="btn-group">
          <u-button class="btn details-btn" @click="onClickDetails(el)">详情</u-button>
        </view>
      </view>
    </view>
  </ZPaging>

  <u-calendar
    :max-date="dayjs().add(100, 'year').format('YYYY-MM-DD')"
    :key="state.calendarKey"
    v-model="state.timePicker.showPicker"
    mode="range"
    @change="onDateRangeChange"
    @close="onTimeClose"
  >
    <template #tooltip>
      <view class="btn-reset" @click="onDateReset">重置</view>
    </template>
  </u-calendar>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import dayjs from 'dayjs'

  import { getHydropowerDispatch } from './services'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)

  const dispatchTypeOptions = [
    { label: '日常调度', value: 1 },
    { label: '临时调度', value: 2 },
  ]

  const state = reactive({
    dispatchType: { value: undefined, title: '调度类型', preVal: undefined },

    calendarKey: 1,
    timePicker: {
      showPicker: false,
      defaultTime: dayjs().format('YYYY-MM-DD'),
      title: '调度时间',
      startTime: undefined,
      endTime: undefined,
    },

    list: [],
  })

  onShow(() => {
    getList(1)
  })

  const getList = (pageNo, pageSize) => {
    getHydropowerDispatch({
      dispatchType: state.dispatchType.value,
      endTime: state.timePicker.endTime,
      startTime: state.timePicker.startTime,
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  function onChangeType(value) {
    if (value === state.dispatchType.preVal) {
      state.dispatchType = { title: '调度类型', value: undefined, preVal: undefined }
    } else {
      state.dispatchType = {
        title: dispatchTypeOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    pagingRef.value.reload()
  }
  const onDropdownOpen = index => {
    if (index === 1) {
      nextTick(() => {
        state.timePicker.showPicker = true
      })
    }
  }

  const onDateRangeChange = val => {
    state.timePicker = {
      ...state.timePicker,
      startTime: val.startDate,
      endTime: val.endDate,
    }

    pagingRef.value.reload()
    dropdownRef.value.close()
    dropdownRef.value.highlight(1)
  }

  const onDateReset = () => {
    state.calendarKey += 1
    state.timePicker = {
      ...state.timePicker,
      startTime: undefined,
      endTime: undefined,
    }

    pagingRef.value.reload()
    state.timePicker.showPicker = false
    dropdownRef.value.close()
    dropdownRef.value.highlight()
  }
  const onTimeClose = () => {
    dropdownRef.value.close()
  }

  //增加
  const add = () => {
    router.push({
      path: '/pages/hydropower-dispatch/add/index',
    })
  }
  //详情
  const onClickDetails = el => {
    router.push({
      path: '/pages/hydropower-dispatch/details/index',
      query: {
        id: el?.id,
      },
    })
  }
</script>

<style lang="scss" scoped>
  @import url(@/pages/maintenance-record/maintenance.scss);
  .card-row .value {
    width: calc(100% - 170rpx);
  }
  .card-row .label,
  .card-row .value {
    width: auto;
  }
  .card-row .label {
    margin-right: 8rpx;
  }

  .btn-reset {
    padding: 32rpx 12rpx 12rpx 32rpx;
  }
</style>
