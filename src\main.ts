import { createSSRApp } from 'vue'
import store from './store'
import router from './router'

// tailwindcss
import './styles/tailwind.css'

// 引入和风天气图标
import 'qweather-icons/font/qweather-icons.css'

// mapbox
import 'mapbox-gl/dist/mapbox-gl.css'

// dayjs
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

//组件库
import uView from 'vk-uview-ui'

// 全局样式
import './styles/index.scss'
import App from './App.vue'

// 全局组件
import NavBar from '@/components/NavBar/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import PanelTitle from '@/components/Title/panelTitle.vue'
import ECharts from '@/components/lime-echart/components/l-echart/l-echart.vue'
import ZPaging from '@/components/z-paging/components/z-paging/z-paging.vue'

dayjs.locale('zh-cn') // 全局使用简体中文

export function createApp() {
  const app = createSSRApp(App)

  // 注册全局组件
  app.component('NavBar', NavBar)
  app.component('SvgIcon', SvgIcon)
  app.component('PanelTitle', PanelTitle)
  app.component('ECharts', ECharts)
  app.component('ZPaging', ZPaging)

  app.use(store).use(router).use(uView)

  // uni.$u.config.unit = 'rpx'

  return {
    app,
    store
  }
}
