<template>
  <scroll-view class="container" scroll-y show-scrollbar>
    <u-section
      title="基本信息"
      class="mt-[30rpx] ml-[32rpx]"
      font-size="34"
      line-color="#3772FF"
      :right="false"
    ></u-section>
    <view class="com-card">
      <u-cell-group :border="false">
        <u-cell-item
          title="盘点日期"
          :arrow="false"
          :value="props.baseInfo.checkTime || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="仓库名称"
          :arrow="false"
          :value="props.baseInfo.warehouseName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="盘点人"
          :arrow="false"
          :value="props.baseInfo.checkManName || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
        <u-cell-item
          title="盘点摘要"
          :arrow="false"
          :value="props.baseInfo.remark || '-'"
          style="padding: 16rpx 30rpx"
        ></u-cell-item>
      </u-cell-group>
    </view>

    <view class="list-content">
      <u-section
        title="库存信息"
        class="mt-[30rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>

      <Table :tableData="props.goodsList" :column="tableColumns">
        <template v-if="props.goodsList?.length" #totalRow>
          <u-tr>
            <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
              <text v-if="ele.title === '序号'">合计</text>
              <text v-else-if="ele.dataIndex === 'stockQty'">{{ _.sum(props.goodsList.map(el => el.stockQty)) }}</text>
              <text v-else-if="ele.dataIndex === 'checkNum'">{{ _.sum(props.goodsList.map(el => el.checkNum)) }}</text>
              <text v-else-if="ele.dataIndex === 'profitLossNum'">
                {{ _.sum(props.goodsList.map(el => el.profitLossNum)) }}
              </text>
              <text v-else>　</text>
            </u-td>
          </u-tr>
        </template>
      </Table>
    </view>
  </scroll-view>

  <view class="bottom-box px-[32rpx] py-[16rpx]">
    <view></view>
    <view class="flex">
      <u-button
        :hair-line="false"
        style="
          height: 80rpx;
          width: 200rpx;
          padding: 0;
          margin-right: 16rpx;
          border-color: transparent;
          background-color: #f2f3f5;
        "
        @click="onPrevStep"
      >
        上一步
      </u-button>
      <u-button style="height: 80rpx; width: 200rpx; padding: 0; margin-right: 0" type="primary" @click="onSubmit">
        提交
      </u-button>
    </view>
  </view>

  <ConfirmPopup
    :show="state.showConfirmSubmit"
    @update:show="state.showConfirmSubmit = val"
    @onConfirm="onConfirm"
    popupTitle="提交盘点"
    title="确认提交盘点"
    description="确认后,将不能修改填写内容"
  />
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs, computed } from 'vue'
  import * as _ from 'lodash'
  import { useRouter } from 'uni-mini-router'
  import Table from '@/components/MyTable/index.vue'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'
  import { addCheck } from '../../services.ts'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '备件编码', dataIndex: 'goodsCode', width: 120 },
    { title: '备件名称', dataIndex: 'goodsName', width: 200 },
    { title: '账面数量', dataIndex: 'stockQty', width: 140 },
    { title: '盘点数量', dataIndex: 'checkNum', width: 140 },
    { title: '盈亏数量', dataIndex: 'profitLossNum', width: 140 },
    { title: '盈亏原因', dataIndex: 'remark', width: 180 }
  ]

  const router = useRouter()
  const props = defineProps(['baseInfo', 'goodsList'])
  const emits = defineEmits(['update:currentStep'])

  const state = reactive({
    showConfirmSubmit: false
  })

  const onPrevStep = () => {
    emits('update:currentStep', 1)
  }
  const onSubmit = () => {
    state.showConfirmSubmit = true
  }

  const onConfirm = () => {
    addCheck({ ...props.baseInfo, goodsList: props.goodsList })
      .then(res => {
        state.showConfirmSubmit = false
        router.push({
          path: '/pages/middle-page/success/index',
          query: {
            title: '盘点完成',
            desc: `仓库盘点成功，盘点单号为:${res.data}`,
            backText: '后返回库存盘点列表',
            btnText: '立即返回',
            url: 2
          }
        })
      })
      .catch(err => {
        state.showConfirmSubmit = false
        router.push({
          path: '/pages/middle-page/failure/index',
          query: {
            title: '提交失败',
            desc: '盘点失败,失败原因为:',
            errText: err,
            btnText: '重新库存盘点',
            url: 1,
            otherJumpText: '回到库存盘点列表,',
            otherUrl: 2
          }
        })
      })
  }
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 32rpx;
    max-height: calc(100vh - 44px - 300rpx);
  }

  .list-content {
    background-color: #fff;
    padding-top: 10rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: 410rpx;
  }

  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
