:deep(.u-dropdown__menu) {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 32rpx;
  .u-dropdown__menu__item {
    flex: 1;
    margin-left: 30rpx;
    margin-right: 30rpx;
  }
}
.drop-content {
  background: #fff;
  max-height: calc(100vh - 300rpx);
  padding: 10rpx 28rpx;
}
.add-btn {
  margin-right: 30rpx;
  font-size: 50rpx;
  cursor: pointer;
}
.card-row {
  display: flex;
  line-height: 56rpx;
  .number {
    font-weight: 500;
    margin-top: -4rpx;
  }
  .label {
    font-size: 28rpx;
    width: 140rpx;
    display: inline-block;
    color: #4e5969;
  }
  .value {
    font-size: 30rpx;

    color: #1d2129;
    width: calc(100% - 140rpx);
  }
  .status {
    height: 44rpx;
    line-height: 44rpx;
    padding: 0 8rpx;
    border-radius: 8rpx;
    text-align: center;
    margin-right: 16rpx;
  }
  .waiting {
    color: #f77234;
    background: #fff3e8;
    border: 1px solid #f77234;
  }
  .inProgress {
    color: #165dff;
    background: #e8f3ff;
    border: 1px solid #165dff;
  }
  .finish {
    color: #00b42a;
    background: #e8ffea;
    border: 1px solid #00b42a;
  }
  .revoke {
    color: #4e5969;
    background: #f2f3f5;
    border: 1px solid #c9cdd4;
  }
  .date {
    font-size: 24rpx;
    color: #86909c;
  }
  .btn-group {
    display: flex;
    margin-left: auto;
  }
  .btn {
    height: 64rpx;
    line-height: 64rpx;
    border: none;
    margin-left: 14rpx;
    font-family: PingFang SC;
    font-size: 28rpx;
    border-radius: 16rpx;
    padding: 0 18rpx;
    &::after {
      border: none;
    }
  }
  .details-btn {
    color: #fff;
    background: #165dff;
  }
  .no-bg-btn {
    color: #1d2129;
    background: #ffffff;
    border: 1px solid #e5e6eb;
  }
}
