<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="盘点详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <scroll-view class="container" scroll-y show-scrollbar>
      <view class="com-card">
        <u-cell-group :border="false">
          <u-cell-item
            title="盘点单号"
            :arrow="false"
            :value="props.checkNo || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="库存名称"
            :arrow="false"
            :value="props.warehouseName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="盘点时间"
            :arrow="false"
            :value="props.checkTime || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="盘点人"
            :arrow="false"
            :value="props.checkManName || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
          <u-cell-item
            title="盘点摘要"
            :arrow="false"
            :value="props.remark == 'null' ? '-' : props.remark || '-'"
            style="padding: 16rpx 30rpx"
          ></u-cell-item>
        </u-cell-group>
      </view>

      <view class="list-content">
        <u-section title="库存信息" class="mt-[30rpx]" font-size="34" line-color="#3772FF" :right="false"></u-section>

        <Table :tableData="state.list" :column="tableColumns">
          <template v-if="state.list.length" #totalRow>
            <u-tr>
              <u-td v-for="ele in tableColumns" :style="{ flex: `0 0 ${ele.width + 'rpx' || 'auto'} !important` }">
                <text v-if="ele.title === '序号'">合计</text>
                <text v-else-if="ele.dataIndex === 'stock'">{{ _.sum(state.list.map(el => el.stock)) }}</text>
                <text v-else-if="ele.dataIndex === 'checkNum'">{{ _.sum(state.list.map(el => el.checkNum)) }}</text>
                <text v-else-if="ele.dataIndex === 'profitLossNum'">
                  {{ _.sum(state.list.map(el => el.profitLossNum)) }}
                </text>
                <text v-else>　</text>
              </u-td>
            </u-tr>
          </template>
        </Table>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import { checkDetail } from '../../services'
  import Table from '@/components/MyTable/index.vue'
  import * as _ from 'lodash'

  const tableColumns = [
    { title: '序号', width: 80 },
    { title: '物料编码', dataIndex: 'goodsCode', width: 140 },
    { title: '备件名称', dataIndex: 'goodsName', width: 200 },
    { title: '账面数量', dataIndex: 'stock', width: 120 },
    { title: '盘点数量', dataIndex: 'checkNum', width: 120 },
    { title: '盈亏数量', dataIndex: 'profitLossNum', width: 120 },
    { title: '盈亏原因', dataIndex: 'remark', width: 140 }
  ]

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)

  const props = defineProps({
    checkId: undefined,
    checkManId: undefined,
    checkManName: undefined,
    checkNo: undefined,
    checkTime: undefined,
    organization: undefined,
    remark: undefined,
    warehouseId: undefined,
    warehouseName: undefined
  })

  const state = reactive({
    list: []
  })

  const getList = (pageNo, pageSize) => {
    checkDetail({
      checkId: props.checkId
    }).then(res => {
      state.list = res.data?.goodsList || []
    })
  }
  getList()
</script>

<style lang="scss" scoped>
  .list-content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 32rpx 32rpx 0 0;
    min-height: calc(100vh - 700rpx);
  }

  .container {
    max-height: calc(100vh - 44px);
  }

  :deep(.u-cell) {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
</style>
