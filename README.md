<!-- 
## 使用模拟器
> https://blog.csdn.net/Stitch_xiao/article/details/130292242

### 1. CMD命令链接
```cmd
adb version
adb kill-server
adb start-server
adb connect 127.0.0.1:7555
``` -->


> https://blog.csdn.net/weixin_50249353/article/details/131849024

D:/moniqi/MuMuPlayer-12.0/shell/adb.exe
端口设置的是 16384
```cmd
.\adb connect 127.0.0.1:16384
.\adb devices
```

### 2. 5037占用问题
`taskkill /f /im adb.exe`

### 安装包报错
`npm i --legacy-peer-deps`

## 资源
### 创建安卓证书
> https://www.yunedit.com/update/androidzhengshu/list


### 组件库 vk-uview-ui
> https://vkuviewdoc.fsq.pub/components/intro.html



### 保活插件
> https://ext.dcloud.net.cn/plugin?id=11765#rating


### vue3路由
> [uni-mini-router](https://ext.dcloud.net.cn/plugin?id=11208)

### 状态pinia与持久化
> [pinia-plugin-unistorage](https://ext.dcloud.net.cn/plugin?id=8081)

### 签名插件
> https://ext.dcloud.net.cn/plugin?id=4354

### 开发
mac电脑需要加一下到devDependencies
```
  "@esbuild/darwin-arm64": "^0.19.9",
  "@esbuild/darwin-x64": "0.17.19",
```

<!-- 暂时放弃vant -->
<!-- ### vant使用
1. 先直接使用全局的vant (直接用)，可能会有问题，如`van-field`在v-model时会报错
2. 使用 components/myuan-vantUI 里的vant组件，引入相应组件使用即可
> myuan-vantUI 在插件市场: https://ext.dcloud.net.cn/plugin?id=13536 -->