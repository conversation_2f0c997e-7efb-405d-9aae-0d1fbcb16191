import { request } from '@/utils/request'

// 维养记录-列表分页查询
export function getMaintenanceRecord(data: object) {
  return request({
    url: '/prjstd/operation/page',
    method: 'post',
    data,
  })
}

//维养记录-详情
export function getMaintenanceDetails(params: object) {
  return request({
    url: '/prjstd/operation/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 维养记录——新增
export function addMaintenance(data: object) {
  return request({
    url: '/prjstd/operation/add',
    method: 'post',
    data,
  })
}
//维养记录-删除
export function deleteMaintenance(params: object) {
  return request({
    url: '/prjstd/operation/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
