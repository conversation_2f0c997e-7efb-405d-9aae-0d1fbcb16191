<template>
  <view class="page-nav-top-common-bg2">
    <NavBar title="库存盘点" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <u-steps
      :current="state.currentStep"
      :list="[{ name: '基本信息' }, { name: '库存信息' }, { name: '提交内容' }]"
      mode="number"
    ></u-steps>

    <view v-show="state.currentStep == 0">
      <Step1
        :warehouseOptions="state.warehouseOptions"
        :checkManList="state.checkManList"
        @onNext="val => (state.baseInfo = val)"
        @update:currentStep="val => (state.currentStep = val)"
      />
    </view>

    <view v-show="state.currentStep == 1">
      <Step2
        v-if="state.baseInfo?.warehouseId"
        :baseInfo="state.baseInfo"
        @onNext="val => (state.goodsList = val)"
        @update:currentStep="val => (state.currentStep = val)"
      />
    </view>

    <Step3
      v-if="state.currentStep == 2"
      :baseInfo="state.baseInfo"
      :goodsList="state.goodsList"
      @update:currentStep="val => (state.currentStep = val)"
    />
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import { getUserPage } from '@/api/common.ts'
  import { getConfigPage, getCheckManList, getStockPage } from '../../services'
  import Step1 from './step1.vue'
  import Step2 from './step2.vue'
  import Step3 from './step3.vue'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const props = defineProps({})

  const state = reactive({
    currentStep: 0,
    warehouseOptions: [],
    checkManList: [],

    baseInfo: {},
    goodsList: []
  })

  getConfigPage({ pageNum: 0, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.warehouseOptions = (res.data?.data || [])
      .filter(el => el.status == 1)
      .map(el => ({ label: el.warehouseName, value: el.warehouseId }))
  })

  getUserPage({ isDisabled: 0, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.checkManList = (res.data.data || []).map(el => ({ label: el.name, value: el.userId }))
  })
</script>

<style lang="scss" scoped></style>
