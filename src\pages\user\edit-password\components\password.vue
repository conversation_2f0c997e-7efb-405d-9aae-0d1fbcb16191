<template>
  <view class="title">旧密码验证</view>

  <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

  <u-form labelPosition="left" :model="state.pwdForm" ref="formRef">
    <view class="com-card">
      <u-form-item prop="password" :border-bottom="false" label="原始密码" labelWidth="190">
        <u-input
          v-model="state.pwdForm.oldPassword"
          :border="false"
          placeholder="请输入"
          clearable
          type="password"
        ></u-input>
      </u-form-item>
    </view>

    <view class="com-card">
      <u-form-item prop="password" :border-bottom="false" label="输入新密码" labelWidth="190">
        <u-input
          :border="false"
          :type="state.isShowPassword2 ? 'input' : 'password'"
          v-model="state.pwdForm.newPassword"
          placeholder="请输入"
          clearable
          type="password"
        ></u-input>
      </u-form-item>
    </view>

    <view class="password-desc">
      <text class="desc">字符形式6～10位密码数，区分大小写</text>
      <PasswordCheck :newPassword="state.pwdForm.newPassword" />
    </view>

    <view class="com-card">
      <u-form-item prop="password" :border-bottom="false" label="再次确认密码" labelWidth="190">
        <u-input
          v-model="state.pwdForm.cfmPassword"
          :border="false"
          placeholder="请输入"
          clearable
          type="password"
        ></u-input>
      </u-form-item>
    </view>

    <u-button class="submit-btn" type="primary" :disabled="!isDisabled" size="default" @click="onSubmit">
      确认修改
    </u-button>
  </u-form>
</template>

<script setup>
  import { reactive, ref, computed } from 'vue'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import Md5 from 'md5'
  import { changePassword } from '../../services'
  import PasswordCheck from '@/components/PasswordCheck/index'
  import { useRouter } from 'uni-mini-router'
  import { getValueByKey } from '@/api/common'

  const router = useRouter()

  const uNotifyRef = ref()

  const state = reactive({
    pwdForm: {
      oldPassword: '',
      newPassword: '',
      cfmPassword: ''
    },
    regex: null,
    errorTips: null
  })
  onLoad(() => {
    getValueByKey('sys.userPassword.regex').then(res => {
      state.regex = res.data
    })
    getValueByKey('sys.userPassword.errorTips').then(res => {
      state.errorTips = res.data
    })
  })

  const isDisabled = computed(() => {
    return state.pwdForm.oldPassword.trim() && state.pwdForm.newPassword.trim() && state.pwdForm.cfmPassword.trim()
  })

  // const getDisabled = () => {
  //   return state.pwdForm.oldPassword.trim() && state.pwdForm.newPassword.trim() && state.pwdForm.cfmPassword.trim()
  // }

  const onSubmit = () => {
    if (isDisabled) {
      if (state.pwdForm.newPassword !== state.pwdForm.cfmPassword) {
        uNotifyRef.value.show({
          type: 'warning',
          title: '新密码输入不一致',
          duration: 1500
        })
        return
      }
      let reg = new RegExp(state.regex)
      if (!reg.test(state.pwdForm.newPassword)) {
        uNotifyRef.value.show({ type: 'warning', title: state.errorTips })
        return
      }

      changePassword({ newPassword: Md5(state.pwdForm.newPassword), oldPassword: Md5(state.pwdForm.oldPassword) }).then(
        res => {
          uNotifyRef.value.show({
            type: 'success',
            title: '修改成功',
            duration: 800
          })
          setTimeout(() => {
            router.push({
              path: '/pages/middle-page/success/index',
              query: {
                title: '修改成功',
                backText: '后请使用新密码重新登录账号',
                btnText: '立即登录',
                jumpType: 'replaceAll',
                url: '/pages/login/index'
              }
            })
          }, 800)
        }
      )
    }
  }
</script>

<style lang="scss" scoped>
  .title {
    font-size: 44rpx;
    font-weight: 600;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #1d2129;
    text-align: center;
    margin-bottom: 60rpx;
  }

  .com-card {
    padding: 0 30rpx;
  }

  .password-desc {
    margin-top: -15rpx;
    padding: 0 32rpx;
    font-size: 24rpx;
    font-weight: 300;
    color: #86909c;
    display: flex;
    justify-content: space-between;
  }

  .submit-btn {
    height: 116rpx;
    text-align: center;
    margin: 0 30rpx;
    margin-top: 70rpx;
    width: calc(100% - 60rpx);
    border-radius: 16rpx;

    :deep(.u-button__text) {
      font-family: PingFang SC-Medium, PingFang SC;
    }
  }

  :deep(.u-form-item__body) {
    padding: 0;
  }
</style>
