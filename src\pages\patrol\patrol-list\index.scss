.container {
  flex: 1;
  max-height: calc(100vh - 168rpx - 80rpx);
  overflow: auto;

  .content {
    padding: 32rpx 0;
  }

  .item-panel {
    margin: 0 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding-bottom: 32rpx;
    margin-bottom: 18rpx;
    &:last-child {
      margin-bottom: 0;
    }

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 64rpx;
      background: #f7f8fa;
      border-radius: 16rpx 16rpx 0 0;
      padding: 0 32rpx;
      font-size: 28rpx;
      color: #4e5969;
      line-height: 28rpx;
    }

    .title {
      margin-top: 20rpx;
      font-size: 32rpx;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #1d2129;
      line-height: 32rpx;

      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

      .task-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 5rpx;
        vertical-align: middle;
      }
    }

    .middle {
      display: flex;
      align-items: center;
      justify-content: space-between;

      margin: 10rpx 0;

      .time {
        font-size: 28rpx;
        font-weight: 300;
        color: #86909c;
        line-height: 48rpx;
      }
    }

    .bottom {
      background: #f7f8fa;
      border-radius: 16rpx;
      display: flex;
      justify-content: space-around;
      padding: 28rpx 0;

      .count-item {
        text-align: center;
        .label {
          font-size: 24rpx;
          color: #86909c;
          line-height: 24rpx;
          margin-bottom: 18rpx;
        }
        .value {
          height: 24rpx;
          font-size: 32rpx;
          font-weight: 600;
          line-height: 24rpx;
        }
      }
    }
  }

  .tag {
    width: 104rpx;
    height: 40rpx;
    line-height: 40rpx;
    text-align: center;
    border-radius: 4rpx;

    font-size: 26rpx;
    font-weight: 300;
  }
  .tag1 {
    background: #ffece8;
    color: #f76560;
  }
  .tag2 {
    background: #e8f3ff;
    color: #165dff;
  }
  .tag3 {
    background: #f2f3f5;
    color: #4e5969;
  }
}
