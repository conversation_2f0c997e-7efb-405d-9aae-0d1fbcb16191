import { createRouter } from 'uni-mini-router'
// import routes from './routes'
// 导入pages.json
import pagesJson from '../pages.json'
// 引入uni-parse-pages
import pagesJsonToRoutes from 'uni-parse-pages'

// 生成路由表
const routes = pagesJsonToRoutes(pagesJson)
const router = createRouter({
  routes: [...routes], // 路由表信息
})

const allowList = ['login', 'home', 'user']

router.beforeEach((to: any, from: any, next) => {
  const userJson = uni.getStorageSync('user')

  const user = userJson ? JSON.parse(userJson) : null
  if (user?.token) {
    const routes = allowList.concat((user?.routes || []).map((el: any) => el.route))

    let arr = to.path.split('/index')[0].split('/')
    const currentPath = arr[arr.length - 1]

    if (routes.includes(currentPath)) {
      next()
    } else {
      next()
      // uni.showToast({
      //   title: '暂无访问权限',
      //   icon: 'none',
      //   duration: 2000
      // })
      // next(false)
    }
  } else {
    next({ name: 'login', navType: 'replaceAll' })
  }
})

router.afterEach(() => {})

export default router
