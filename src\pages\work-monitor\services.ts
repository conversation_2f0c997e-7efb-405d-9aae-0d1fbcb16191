import { request } from '@/utils/request'

// 工情监测
export function getProjectDeviceList(data: any) {
  return request({
    url: '/base/device/data/projectDeviceList',
    method: 'post',
    data,
  })
}

// 行政区划-获取树
export function getDistrictTree(data: any) {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
