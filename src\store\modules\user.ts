import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  unistorage: true, // 开启后对 state 的数据读写都将持久化
  state() {
    return {
      token: '',
      user: {},
      routes: [],
      orgList: [],
      statusBarHeight: 0,
      loginOrg: {},
    }
  },
  actions: {
    setUserInfo(token: string, user: any) {
      this.token = token
      this.user = user
    },

    removeToken() {
      this.token = ''
    },

    setRoutes(routes: any) {
      this.routes = routes
    },

    setOrtList(orgList: any[]) {
      this.orgList = orgList
    },
    //
    setLoginOrg(loginOrg: object) {
      this.loginOrg = loginOrg
    },

    setStatusBarHeight() {
      uni.getSystemInfo({
        success: res => {
          this.statusBarHeight = res.statusBarHeight
        },
      })
    },
  },
})
