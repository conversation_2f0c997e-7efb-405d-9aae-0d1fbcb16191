<template>
  <u-subsection
    active-color="#165DFF"
    :list="props.timeRangesOptions"
    v-model="state.subVal"
    style="width: 440rpx; margin: 0 auto"
    @change="onSubChange"
  ></u-subsection>
  <u-picker
    v-model="state.showPicker"
    :default-time="state.defaultTime"
    mode="time"
    :params="state.timeParams"
    @confirm="onTimeConfirm"
  ></u-picker>
  <view class="time-bar">
    <view class="time-item" @click="onOpenTimePicker('start')">
      {{ state.startTime }}
      <SvgIcon name="icon-time" class="ml-[12rpx]" />
    </view>
    <view class="text-[14px] text-[#86909C] mx-[8rpx]">至</view>
    <view class="time-item" @click="onOpenTimePicker('end')">
      {{ state.endTime }}
      <SvgIcon name="icon-time" class="ml-[12rpx]" />
    </view>
  </view>
  <u-section
    :title="props.indicatorOption?.[props.indexCode]?.label + props.indicatorOption?.[props.indexCode]?.unit"
    class="mt-[30rpx]"
    font-size="34"
    line-color="#3772FF"
    :right="false"
  ></u-section>
  <LineChart :key="state.subVal + Math.random()" :dataSource="chartData" :yAxisConfig="yAxisConfig" />
  <!-- :title="**水位记录`" -->
  <u-section
    :title="`${props.timeRangesOptions?.[state.subVal]?.name}${props.indicatorOption?.[props.indexCode]?.label}记录`"
    class="mt-[30rpx] mb-[20rpx]"
    font-size="34"
    line-color="#3772FF"
    :right="false"
  ></u-section>
  <Table v-if="!!state.tableColumn.length" :tableData="state.list" :column="state.tableColumn" />
</template>

<script setup lang="ts">
  import { reactive, ref, watch, nextTick } from 'vue'
  import dayjs from 'dayjs'
  import LineChart from '@/components/LineChart/index.vue'
  import Table from '@/components/MyTable/index.vue'
  import { indicatorList } from '../../../services'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber'

  interface stateType {
    subVal: number
    showPicker: boolean
    pickType: string
    defaultTime: string
    timeParams: any
    startTime: string
    endTime: string
    loading: boolean
    list: any[]
    tableColumn: any[]
  }

  interface Props {
    title: string
    siteId: string
    siteTerminalData: any[]
    indexCode: string
    indicatorOption: any
    timeRangesOptions: any[]
  }
  const props = withDefaults(defineProps<Props>(), {
    title: '',
    siteId: undefined,
    siteTerminalData: undefined,
    indexCode: '',
    indicatorOption: undefined,
    timeRangesOptions: undefined,
  })

  const state: stateType = reactive({
    subVal: 0,
    showPicker: false,
    pickType: '',
    timeParams: { year: true, month: true, day: true, hour: true, minute: false, second: false },
    defaultTime: '',
    startTime: dayjs().format('YYYY-MM-DD 08:00'),
    endTime: dayjs().format('YYYY-MM-DD HH:00'),
    loading: false,
    list: [],
    tableColumn: [],
  })
  const chartData: any = ref(null)
  const yAxisConfig: any = ref(null)

  watch(
    () => props.siteTerminalData,
    () => {
      nextTick(() => {
        if (Object.keys(props.indicatorOption).length === 0 || !props.indexCode) return

        setColumns()

        if (props.indexCode) {
          getList()
        }
      })
    }
  )

  watch(
    () => props.indexCode,
    () => {
      nextTick(() => {
        setColumns()
        getList()
      })
    },
    { immediate: true }
  )

  const setColumns = () => {
    state.tableColumn = [
      { title: '序号', width: 66 },
      { title: '时间', dataIndex: 'dateTime' },
      {
        title: `${props.indicatorOption[props.indexCode]?.label}${props.indicatorOption[props.indexCode]?.unit}`,
        dataIndex: 'indexValue',
        width: 260,
      },
    ]
  }

  function resetTime() {
    const eightTime = dayjs(`${dayjs().format('YYYY-MM-DD')} 08:00`).valueOf()
    if (dayjs().valueOf() > eightTime) {
      state.startTime = dayjs().format('YYYY-MM-DD 08:00')
    } else {
      state.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:00')
    }
  }
  resetTime()

  const onSubChange = (index: number) => {
    if (index == 2) {
      state.timeParams = { ...state.timeParams, hour: false }
      state.startTime = dayjs().subtract(1, 'months').format('YYYY-MM-DD')
      state.endTime = dayjs().format('YYYY-MM-DD')
    } else {
      state.timeParams = { ...state.timeParams, hour: true }
      state.startTime = dayjs().format('YYYY-MM-DD 08:00')
      state.endTime = dayjs().format('YYYY-MM-DD HH:00')
    }
    nextTick(() => {
      getList()
    })
  }

  const onOpenTimePicker = (type: string) => {
    state.pickType = type
    if (type === 'start') {
      state.defaultTime = state.startTime
      state.showPicker = true
    }
    if (type === 'end') {
      state.defaultTime = state.endTime
      state.showPicker = true
    }
  }
  const onTimeConfirm = (p: any) => {
    if (state.pickType == 'start') {
      state.startTime = `${p.year}-${p.month}-${p.day} ${p.hour}:00`
    }
    if (state.pickType == 'end') {
      state.endTime = `${p.year}-${p.month}-${p.day} ${p.hour}:00`
    }
    getList()
  }

  const getList = () => {
    console.log('props title', props.title)
    console.log('props indexCode', props.indexCode)
    console.log('props timeRangesOptions', props.timeRangesOptions)
    state.loading = true

    const params = {
      // siteId: props.siteId,
      type: props.timeRangesOptions?.[state.subVal]?.value,
      startTime: dayjs(state.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(state.endTime).format('YYYY-MM-DD HH:mm:ss'),
      indexCode: props?.indexCode,
      terminalId: props?.siteTerminalData?.find(el => el.indexCode == props?.indexCode)?.terminals?.[0].terminalId,
    }

    // 逐日
    if (state.subVal == 2) {
      params.startTime = dayjs(state.startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss')
      params.endTime = dayjs(state.endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')
    }

    if (!params.terminalId || !params.indexCode) {
      return
    }

    indicatorList(params).then((res: any) => {
      state.list = res.data?.dwDates
      console.log('测试', res.data, state.list)
      state.loading = false

      // 检查是否所有的 indexValue 都为 null
      const allValuesNull = state.list.every(item => item.indexValue === null)

      // 如果所有值都为 null，设置自定义 y 轴配置
      if (allValuesNull) {
        yAxisConfig.value = {
          max: 3,
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#dddddd'
            }
          }
        }
      } else {
        yAxisConfig.value = null
      }

      chartData.value = [
        {
          name: props.indicatorOption[props.indexCode]?.label, //'水位'
          data: state.list.map(el => [el.dateTime, el.indexValue]),
        },
      ]
    })
  }
</script>

<style lang="scss" scoped>
  .time-bar {
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .time-item {
      padding: 18rpx 28rpx;
      border-radius: 60rpx;
      border: 2rpx solid #f2f3f5;

      font-size: 24rpx;
      color: #4e5969;
      display: flex;
      align-items: center;
    }
  }
</style>
