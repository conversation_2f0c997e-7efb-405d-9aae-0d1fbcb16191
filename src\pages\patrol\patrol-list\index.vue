<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar :title="state.title" :background="{ background: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="search-bar">
      <u-tabs
        class="tab-content"
        :list="tabOptions"
        bg-color="transparent"
        active-color="#165DFF"
        v-model="state.tabCurrent"
        bar-width="80"
        gutter="20"
        @change="onTabClick"
      ></u-tabs>

      <u-dropdown
        v-if="taskTypeOptions.length"
        ref="dropdownRef"
        height="80"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 0"
      >
        <u-dropdown-item
          v-model="state.task.value"
          height="850"
          :title="state.task.title"
          :options="taskTypeOptions"
          @change="onDropdownChange"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <scroll-view class="container" :scroll-y="true" :show-scrollbar="true" v-if="state.list?.length">
    <view class="content">
      <view class="item-panel" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
        <view class="header">
          <text class="text-[12px]">任务编号:{{ el.taskCode }}</text>
          <view :class="`tag tag${el.taskStatus}`">
            {{ tabOptions.find(ele => ele.value == el.taskStatus)?.name }}
          </view>
        </view>

        <view style="padding: 0 32rpx">
          <view class="title">
            <image class="task-icon" :src="el.isTemp ? tempIcon : planIcon" />
            {{ el.taskName }}
          </view>

          <view class="middle">
            <view class="time">
              <view>计划开始时间：{{ el.planStartTime }}</view>
              <view>计划结束时间：{{ el.planEndTime }}</view>
            </view>
            <view
              v-if="el.taskStatus == 1 && dayjs().valueOf() > dayjs(el.planEndTime).valueOf()"
              class="text-[12px] text-[#F99057] mt-[36rpx]"
            >
              任务已超期
            </view>
            <u-button
              style="height: 60rpx; width: 159rpx; padding: 0; margin-right: 0"
              type="primary"
              v-if="el.taskStatus == 1 && dayjs().valueOf() < dayjs(el.planEndTime).valueOf()"
              @click.stop="onStartPatrolClick(el)"
            >
              立即巡检
            </u-button>
          </view>

          <view class="bottom">
            <view class="count-item">
              <view class="label">总数</view>
              <view class="value">{{ el.totalCount }}</view>
            </view>
            <view class="count-item">
              <view class="label">完成</view>
              <view :class="`value ${el.taskStatus != 1 && 'text-[#3772FF]'}`">{{ el.checkedCount }}</view>
            </view>
            <view class="count-item">
              <view class="label">异常</view>
              <view :class="`value ${el.taskStatus != 1 && 'text-[#F76560]'}`">{{ el.abnormalCount }}</view>
            </view>
            <view class="count-item">
              <view class="label">漏检</view>
              <view :class="`value ${el.taskStatus != 1 && 'text-[#F99057]'}`">{{ el.incompleteCount }}</view>
            </view>
            <view class="count-item">
              <view class="label">未检</view>
              <view :class="`value ${el.taskStatus != 1 && 'text-[#62686E]'}`">
                {{ el.totalCount - el.checkedCount }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <u-empty v-else text="暂无数据" style="height: 800rpx"></u-empty>

  <ConfirmPopup
    :show="state.showConfirmStart"
    @update:show="state.showConfirmStart = val"
    @onConfirm="onConfirm"
    popupTitle="开始巡检"
    title="确认是否开始巡检"
    description="确认后将领取巡检任务并开始任务计时"
  />
</template>

<script setup>
  import { reactive, ref, onMounted, watch } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import dayjs from 'dayjs'
  import { useUserStore } from '@/store/modules/user'
  import { usePatrolStore } from '@/store/modules/patrol.ts'
  import { onLoad } from '@dcloudio/uni-app'
  import { getMyTaskList, patrolStart } from './services'
  import tempIcon from '@/static/icons/icon-temp.svg'
  import planIcon from '@/static/icons/icon-plan.svg'
  import { tabOptions, taskTypeOptions } from './configs.ts'
  import ConfirmPopup from '@/components/ConfirmPopup/index.vue'

  const userStore = useUserStore()
  const store = usePatrolStore()
  const router = useRouter()

  const uNotifyRef = ref()

  const state = reactive({
    title: '巡检',
    tabCurrent: 0,
    prevSelectValue: undefined,
    task: { title: '全部任务', value: undefined },
    showConfirmStart: false,
    list: [],
    currentItem: {}
  })

  watch(
    () => router.route.value,
    newVal => {
      if (newVal.query?.patrolType) {
        if (newVal.query.patrolType == 1) {
          state.title = '点位巡检'
        }
        if (newVal.query.patrolType == 2) {
          state.title = '路线巡检'
        }
        store.setPatrolType(newVal.query.patrolType)
        getList()
      }
    },
    { immediate: true }
  )

  const onTabClick = index => {
    state.tabCurrent = index

    getList()
  }

  const onDropdownChange = value => {
    if (value === state.prevSelectValue) {
      state.task = { title: '全部任务', value: undefined }
      state.prevSelectValue = undefined
    } else {
      state.task = { title: taskTypeOptions.find(el => el.value == value)?.label, value: value }
      state.prevSelectValue = value
    }

    getList()
  }

  const getList = () => {
    getMyTaskList({
      taskStatus: state.tabCurrent || undefined,
      isTemp: state.task.value,
      patrolType: store.patrolType
    }).then(res => {
      state.list = res.data || []
    })
  }

  const onItemClick = item => {
    router.push({
      path: '/pages/patrol/patrol-detail/index',
      query: { taskId: item.taskId }
    })
  }

  //开始巡检
  const onStartPatrolClick = el => {
    state.showConfirmStart = true
    state.currentItem = el
  }
  const onConfirm = () => {
    patrolStart({ taskId: state.currentItem.taskId }).then(res => {
      getList()
      state.showConfirmStart = false

      store.setPatrolStart(state.currentItem.taskId, true, res => {})
    })
  }
</script>

<style lang="scss" scoped>
  @import './index.scss';

  .search-bar {
    position: relative;
    height: 80rpx;
    display: flex;
    align-items: center;

    .tab-content {
      position: absolute;
      margin-top: 6rpx;
      width: 550rpx;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 12;
    }

    :deep(.u-dropdown__menu) {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .u-dropdown__menu__item {
        flex: unset;
        margin-right: 32rpx;
      }
    }
  }
</style>
