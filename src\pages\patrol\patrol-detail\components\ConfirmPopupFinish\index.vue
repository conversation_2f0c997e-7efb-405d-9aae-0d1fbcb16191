<template>
  <u-popup
    v-model="props.show"
    mode="bottom"
    class="confirm-popup"
    @close="onClose"
    @cancel="onClose"
    border-radius="20"
  >
    <view class="popup-header">
      <text class="popup-title">{{ props.popupTitle }}</text>
      <view class="popup-close" @click="onClose">×</view>
    </view>
    <view class="popup-content">
      <image class="sigh-img" src="/static/images/confirm-img.png" />
      <view class="text-group">
        <view class="big-title">{{ props.title }}</view>
        <view class="small-title">{{ props.description }}</view>
      </view>
    </view>

    <view class="flex">
      <u-button
        v-if="props.type === 'primary'"
        size="default"
        style="margin: 0 48rpx 48rpx; width: calc(60% - 32rpx)"
        @click="() => emit('onConfirm')"
        class="popup-btn1"
      >
        结束巡检并上报异常
      </u-button>

      <u-button
        v-if="props.type === 'primary'"
        type="primary"
        size="default"
        style="margin: 0 48rpx 48rpx 0; width: 40%"
        @click="() => emit('onConfirm')"
      >
        {{ props.btnText }}
      </u-button>
    </view>

    <u-button
      v-if="props.type === 'warning'"
      style="font-size: 30rpx"
      class="popup-btn"
      @click="() => emit('onConfirm')"
    >
      {{ props.btnText }}
    </u-button>
  </u-popup>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue'

  const emit = defineEmits(['update:show', 'onConfirm'])
  const props = defineProps({
    show: { type: Boolean, default: false },
    popupTitle: { type: String, default: '弹窗标题' },
    title: { type: String, default: '确认是否' },
    description: { type: String, default: '确认是否描述' },
    btnText: { type: String, default: '确定' },
    type: { type: String, default: 'primary' }, // primary warning
  })

  const onClose = () => {
    if (!props.show) return
    emit('update:show', !props.show)
  }
</script>

<style lang="scss" scoped>
  .confirm-popup {
    .popup-header {
      width: 100%;
      padding: 24rpx 0;
      text-align: center;
      position: relative;
      border-bottom: 1px solid #f2f3f5;
      .popup-title {
        font-size: 36rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        color: #1d2129;
        font-weight: 500;
      }
      .popup-close {
        font-size: 60rpx;
        border: none;
        position: absolute;
        top: 0rpx;
        right: 10rpx;
        width: 100rpx;
        color: #4e5969;
      }
    }
    .popup-content {
      margin-top: 20rpx;
      display: flex;
      align-items: center;
      padding: 20rpx 40rpx 40rpx;
      .sigh-img {
        width: 108rpx;
        height: 108rpx;
        display: block;
      }
      .text-group {
        margin-left: 32rpx;
        .big-title {
          font-weight: 600;
          font-size: 34rpx;
          color: #1d2129;
        }
        .small-title {
          margin-top: 12rpx;
          font-size: 28rpx;
          color: #86909c;
        }
      }
    }
    .popup-btn {
      width: 654rpx;
      height: 96rpx;
      color: #fff;
      background: #ff9a2e;
      border-radius: 16rpx;
      margin-bottom: 50rpx;
    }
    .popup-btn1 {
      width: 654rpx;
      height: 96rpx;
      border-radius: 16rpx;
      margin-bottom: 50rpx;
      color: #165dff;
      background: #f2f3f5;
      &::after {
        border: 1px solid #e5e6eb;
      }
    }
  }
</style>
