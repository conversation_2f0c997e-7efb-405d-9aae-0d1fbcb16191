<template>
  <NavBar :title="state.title">
    <template #right>
      <u-button style="margin-right: 32rpx" @click="onRightClick" type="primary" size="mini" :disabled="!state.value">
        确定
      </u-button>
    </template>
  </NavBar>

  <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

  <view class="page-top-nav edit-user-info">
    <view class="com-card">
      <u-input
        v-if="state.type != 'avatar'"
        placeholder="请输入内容"
        :border="false"
        v-model="state.value"
        clearable
      ></u-input>

      <view v-else class="content">
        <image class="current-avatar" :src="state.currentAvatar" />

        <text>替换为</text>

        <MyUpload
          :urls="state.value"
          @update:urls="urls => (state.value = urls)"
          :multiple="false"
          folderName="avatar"
        ></MyUpload>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { reactive, ref } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad } from '@dcloudio/uni-app'
  import defaultPhoto from '@/static/images/user-photo.png'
  import { userSimpleInfoUpdate } from '../../services.ts'
  import { getUserInfo } from '@/api/common.ts'
  import MyUpload from '@/components/MyUpload/index.vue'

  const userStore = useUserStore()
  const router = useRouter()

  const user = userStore.user

  const uNotifyRef = ref()

  const state = reactive({
    title: '',
    value: '',
    type: '',

    currentAvatar: ''
  })

  onLoad(props => {
    if ('avatar' in props) {
      state.title = '头像修改'
      state.currentAvatar = props.avatar
      state.type = 'avatar'
    }
    if ('name' in props) {
      state.title = '姓名修改'
      state.value = props.name
      state.type = 'name'
    }

    if ('email' in props) {
      state.title = '邮箱修改'
      state.value = props.email
      state.type = 'email'
    }
    if ('mobile' in props) {
      state.title = '手机号修改'
      state.value = props.mobile
      state.type = 'mobile'
    }
  })

  const onRightClick = () => {
    if (state.value && state.value?.trim()) {
      if (state.type === 'email') {
        if (!/^([a-zA-Z\d][\w-]{2,})@(\w{2,})\.([a-z]{2,})(\.[a-z]{2,})?$/.test(state.value)) {
          uNotifyRef.value.show({
            type: 'warning',
            title: '请输入正确邮箱'
          })
          return
        }
      }
      if (state.type === 'mobile') {
        if (!/^1[0-9]{10}$/.test(state.value)) {
          uNotifyRef.value.show({
            type: 'warning',
            title: '请输入正确手机号'
          })
          return
        }
      }

      userSimpleInfoUpdate({
        userId: user.userId,
        [state.type]: state.value
      }).then(resp => {
        getUserInfo(user.userId).then(res => {
          userStore.setUserInfo(userStore.token, res.data)
          uNotifyRef.value.show({
            type: 'success',
            title: '修改成功'
          })
          setTimeout(() => {
            router.back()
          }, 800)
        })
      })
    }
  }
</script>

<style lang="scss" scoped>
  .edit-user-info {
    padding-top: 0rpx;
  }

  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .current-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
    }
  }
</style>
