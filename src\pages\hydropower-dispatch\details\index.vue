<template>
  <view class="have-bg-page">
    <view class="details-bg"></view>
    <NavBar title="详情" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="com-card">
      <view class="row first">
        <text class="label">调度类型</text>
        <text class="value">{{ state.details?.dispatchType === 1 ? '日常调度' : '临时调度' }}</text>
      </view>
      <view class="row">
        <text class="label">调度日期</text>
        <text class="value">{{ state.details?.dispatchDate }}</text>
      </view>
      <view class="separation-line">
        <text class="circle"></text>
        <text class="line"></text>
        <text class="circle"></text>
      </view>

      <view class="open-device-title">
        <text class="flex">开启设备</text>
        <text class="flex">开启时长</text>
      </view>

      <view class="open-device-row" v-for="(el, i) in state.details?.detailsVos" :key="i">
        <text class="flex">{{ el.sort === 1 ? '1#发电机' : el.sort === 2 ? '2#发电机' : '3#发电机' }}</text>
        <text class="flex" v-if="state.details?.dispatchType === 1">
          {{ el.hour === null ? '--' : el.hour + '小时' }}
        </text>
        <text class="flex" v-if="state.details?.dispatchType === 2">
          {{ el.startTime === null ? '-' : el.startTime.slice(0, el.startTime.lastIndexOf(':')) }}~{{
            el.endTime === null ? '-' : el.endTime.slice(0, el.endTime.lastIndexOf(':'))
          }}
        </text>
      </view>

      <view class="row remark">
        <text class="label">备注</text>
        <text class="value">{{ state.details?.remark === null ? '--' : state.details?.remark }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { reactive, onMounted } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { getHydropowerDetails } from '../services'

  const state = reactive({
    details: {},
  })

  const props = defineProps(['id'])

  onMounted(() => {
    getDetails()
  })

  const getDetails = () => {
    getHydropowerDetails({
      id: props?.id,
    }).then(res => {
      state.details = res?.data
    })
  }
</script>

<style lang="scss" scoped>
  .have-bg-page {
    margin: -34rpx;
    background: #cdefff url('@/static/images/details-nav-bar-bg.png') no-repeat;
    background-size: 100%;
    padding: 40rpx;
    position: relative;
    .details-bg {
      position: absolute;
      top: 230rpx;
      left: 36rpx;
      width: 90%;
      height: 86%;
      border-radius: 16rpx;
      transform: rotate(-3deg);
      background: rgba(255, 255, 255, 0.3);
    }

    .separation-line {
      height: 30rpx;
      display: flex;
      margin: 0 -36rpx;
      .circle {
        width: 30rpx;
        height: 30rpx;
        border-radius: 30rpx;
        background: #cdefff;
      }
      .line {
        flex: 1;
        margin: 16rpx 10rpx;
        border-top: 1px dashed #e5e6eb;
      }
    }
  }
  .com-card {
    height: calc(100vh - 100px);
    position: relative;
    padding-top: 16rpx;
    .status-img {
      position: absolute;
      right: 16rpx;
      width: 160rpx;
      height: 160rpx;
    }
    .first {
      padding-top: 30rpx;
    }
  }
  .row {
    margin-bottom: 26rpx;
    display: flex;
    .label {
      font-weight: 400;
      font-size: 28rpx;
      color: #4e5969;
      width: 80px;
    }
    .value {
      font-weight: 400;
      font-size: 30rpx;
      color: #1d2129;
      flex: 1;
    }
    .img-box {
      flex: 1;
      display: flex;
      .img,
      :deep(.u-image__image) {
        width: 120rpx !important;
        height: 120rpx !important;
        margin-right: 20rpx;
      }
    }
  }

  .open-device-title,
  .open-device-row {
    width: 100%;
    height: 56rpx;
    line-height: 56rpx;
    background: #e8f3ff;
    color: #1d2129;
    display: flex;
    margin-bottom: 20rpx;
    .flex {
      width: 50%;
      padding-left: 10rpx;
    }
  }
  .open-device-title {
    color: #86909c;
    font-size: 24rpx;
    margin-top: 30rpx;
  }
  .remark {
    margin-top: 26rpx;
  }
</style>
