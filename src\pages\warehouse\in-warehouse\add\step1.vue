<template>
  <scroll-view class="container" scroll-y>
    <u-form :model="state.form" ref="formRef" label-width="150" :error-type="['border-bottom', 'toast']">
      <u-section
        title="基本信息"
        class="mt-[32rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>
      <view class="com-card">
        <u-form-item label="仓库名称" prop="warehouseId" required>
          <u-input
            v-model="state.select.current1.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'warehouseId'
                state.select.list = props.warehouseOptions
              }
            "
            placeholder="请选择仓库"
          />
        </u-form-item>

        <u-form-item label="入库来源" prop="inboundType" required>
          <u-input
            v-model="state.select.current2.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'inboundType'
                state.select.list = inboundTypeOptions
              }
            "
            placeholder="请选择入库来源"
          />
        </u-form-item>

        <u-form-item label="入库日期" prop="inboundTime" required>
          <u-input
            v-model="state.form.inboundTime"
            type="select"
            @click="state.showTimePicker = true"
            placeholder="请选择入库日期"
          />
        </u-form-item>

        <u-form-item label="验收人" prop="stockmanId" required>
          <u-input
            v-model="state.select.current3.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'stockmanId'
                state.select.list = props.checkManList
              }
            "
            placeholder="请选择验收人"
          />
        </u-form-item>

        <u-form-item label="采购人" prop="purchaserId">
          <u-input
            v-model="state.select.current4.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'purchaserId'
                state.select.list = props.checkManList
              }
            "
            placeholder="请选择采购人"
          />
        </u-form-item>

        <u-form-item label="供应商" prop="supplier">
          <u-input
            v-model="state.select.current5.label"
            type="select"
            @click="
              () => {
                state.select.show = true
                state.select.field = 'supplier'
                state.select.list = props.supplierList
              }
            "
            placeholder="请选择供应商"
          />
        </u-form-item>

        <u-form-item label="发票号" prop="invoiceNo">
          <u-input v-model="state.form.invoiceNo" placeholder="请输入发票号" />
        </u-form-item>
      </view>

      <u-section
        title="摘要信息"
        class="mt-[32rpx] ml-[32rpx]"
        font-size="34"
        line-color="#3772FF"
        :right="false"
      ></u-section>
      <view class="com-card" style="padding: 10rpx 28rpx">
        <u-form-item label="入库摘要" prop="remark" label-position="top" :border-bottom="false">
          <u-input type="textarea" v-model="state.form.remark" />
        </u-form-item>
      </view>
    </u-form>
  </scroll-view>

  <u-select
    v-model="state.select.show"
    mode="single-column"
    :list="state.select.list"
    @cancel="state.select.show = false"
    @confirm="onSelectConfirm"
  ></u-select>

  <u-picker
    v-model="state.showTimePicker"
    :default-time="state.defaultTime"
    mode="time"
    :params="{ year: true, month: true, day: true, hour: true, minute: true, second: true }"
    @confirm="onTimeConfirm"
  ></u-picker>

  <view class="bottom-box px-[32rpx] py-[16rpx]">
    <u-button style="height: 80rpx; width: 200rpx; padding: 0; margin-right: 0" type="primary" @click="onNextStep">
      下一步
    </u-button>
  </view>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import dayjs from 'dayjs'
  import { inboundTypeOptions } from '../config.ts'

  const props = defineProps({
    warehouseOptions: { default: [] },
    checkManList: { default: [] },
    supplierList: { default: [] }
  })
  const emits = defineEmits(['update:currentStep', 'onNext'])

  const formRef = ref(null)

  const state = reactive({
    select: {
      show: false,
      field: undefined,
      current1: { label: undefined, value: undefined },
      current2: { label: undefined, value: undefined },
      current3: { label: undefined, value: undefined },
      current4: { label: undefined, value: undefined },
      current5: { label: undefined, value: undefined },
      list: []
    },
    showTimePicker: false,
    defaultTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    form: {
      warehouseId: undefined,
      warehouseName: undefined,
      inboundType: undefined,
      inboundTime: undefined,
      stockmanId: undefined,
      stockmanName: undefined,
      purchaserId: undefined,
      purchaserName: undefined,
      invoiceNo: undefined,
      supplier: undefined,
      remark: undefined
    },
    rules: {
      warehouseId: [{ required: true, message: '请选择仓库' }],
      inboundType: [{ required: true, message: '请选择入库来源' }],
      inboundTime: [{ required: true, message: '请选择入库日期' }],
      stockmanId: [{ required: true, message: '请选择验收人' }]
    }
  })

  onMounted(() => {
    formRef.value.setRules(state.rules)
  })

  const onSelectConfirm = item => {
    switch (state.select.field) {
      case 'warehouseId':
        state.select.current1 = item[0]
        state.form.warehouseId = item[0].value
        state.form.warehouseName = item[0].label
        return
      case 'inboundType':
        state.select.current2 = item[0]
        state.form.inboundType = item[0].value
        return
      case 'stockmanId':
        state.select.current3 = item[0]
        state.form.stockmanId = item[0].value
        state.form.stockmanName = item[0].label
        return
      case 'purchaserId':
        state.select.current4 = item[0]
        state.form.purchaserId = item[0].value
        state.form.purchaserName = item[0].label
        return
      case 'supplier':
        state.select.current5 = item[0]
        state.form.supplier = item[0].value
        return
      default:
        return
    }
  }

  const onTimeConfirm = p => {
    state.form.inboundTime = dayjs(`${p.year}-${p.month}-${p.day} ${p.hour}:${p.minute}:${p.second}`).format(
      'YYYY-MM-DD HH:mm:ss'
    )
  }

  const onNextStep = () => {
    formRef.value.validate(valid => {
      if (valid) {
        emits('onNext', state.form)
        emits('update:currentStep', 1)
      }
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 32rpx;
    max-height: calc(100vh - 44px - 340rpx);
  }
  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background-color: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  }
</style>
