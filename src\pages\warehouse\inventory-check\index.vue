<template>
  <view class="page-nav-top-common">
    <NavBar title="库存盘点" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>
  </view>

  <view class="relative">
    <u-dropdown
      ref="dropdownRef"
      height="60"
      title-size="28"
      menu-icon="arrow-down-fill"
      menu-icon-size="20"
      style="margin: 20rpx 0"
      @open="onDropdownOpen"
    >
      <u-dropdown-item
        v-model="state.warehouseVal.value"
        :title="state.warehouseVal.title"
        :options="state.warehouseOptions"
        @change="onWarehouseValChange"
      ></u-dropdown-item>

      <u-dropdown-item
        v-model="state.checkManVal.value"
        :title="state.checkManVal.title"
        :options="state.checkManList"
        @change="onCheckManValChange"
      ></u-dropdown-item>

      <u-dropdown-item :title="state.timePicker.title"></u-dropdown-item>
    </u-dropdown>
  </view>

  <ZPaging ref="pagingRef" style="margin-top: calc(44px + 160rpx); z-index: 10" v-model="state.list" @query="getList">
    <view class="item" v-for="(el, idx) in state.list" :key="idx" @click="onItemClick(el)">
      <view class="header">
        <text class="header-name">盘点单号: {{ el.checkNo || '-' }}</text>
      </view>

      <view class="content">
        <view class="content-title">
          <view class="title">
            {{ el.warehouseName || '-' }}
          </view>
        </view>

        <view class="indicate-item">盘点时间: {{ el.checkTime || '-' }}</view>

        <view class="mt-[10rpx]">
          <view class="flex item-center justify-between">
            <text class="text-[13px] text-[#86909C]">盘点人&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ el.checkManName || '-' }}</text>
          </view>
          <view class="flex item-center justify-between mt-[10rpx]">
            <text class="text-[13px] text-[#86909C]">盘点摘要&nbsp;</text>
            <text class="text-[13px] text-[#1D2129]">{{ el.remark || '-' }}</text>
          </view>
        </view>
      </view>
    </view>
  </ZPaging>

  <u-picker
    v-model="state.timePicker.showPicker"
    :default-time="state.timePicker.defaultTime"
    mode="time"
    :params="timeParams"
    cancel-text="重置"
    @confirm="onTimeConfirm"
    @cancel="onTimeCancel"
    @close="onTimeClose"
  ></u-picker>

  <LimeFab :offset="{ x: -1, y: 400 }" heightGap="60">
    <view class="add-btn" @click="onAddClick">
      <u-icon name="plus" color="#ffffff" size="36"></u-icon>
    </view>
  </LimeFab>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, nextTick, useAttrs } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { getOptions } from '@/api/common.ts'
  import dayjs from 'dayjs'
  import { getConfigPage, getStockPage, getCheckPage, getCheckManList } from '../services'
  import LimeFab from '@/components/lime-fab/index.vue'

  const timeParams = { year: true, month: true, day: false, hour: false, minute: false, second: false }

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)

  const state = reactive({
    warehouseOptions: [],
    warehouseVal: { value: undefined, title: '盘点仓库', preVal: undefined },
    checkManList: [],
    checkManVal: { value: undefined, title: '盘点人', preVal: undefined },
    timePicker: {
      showPicker: false,
      defaultTime: dayjs().format('YYYY-MM'),
      title: '盘点日期',
      startTime: undefined,
      endTime: undefined,
    },
    list: [],
  })

  onShow(() => {
    getList(1)
  })

  getConfigPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.warehouseOptions = (res.data?.data || []).map(el => ({
      ...el,
      label: el.warehouseName,
      value: el.warehouseId,
    }))
  })

  getCheckManList().then(res => {
    state.checkManList = (res.data || []).map(el => ({ ...el, label: el.checkManName, value: el.checkManId }))
  })

  const getList = (pageNo, pageSize) => {
    getCheckPage({
      warehouseId: state.warehouseVal.value,
      checkManId: state.checkManVal.value,
      startTime: state.timePicker.startTime,
      endTime: state.timePicker.endTime,
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  function onWarehouseValChange(value) {
    if (value === state.warehouseVal.preVal) {
      state.warehouseVal = { title: '盘点仓库', value: undefined, preVal: undefined }
    } else {
      state.warehouseVal = {
        title: state.warehouseOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    pagingRef.value.reload()
    getList(1)
  }

  function onCheckManValChange(value) {
    if (value === state.checkManVal.preVal) {
      state.checkManVal = { title: '盘点人', value: undefined, preVal: undefined }
    } else {
      state.checkManVal = {
        title: state.checkManList.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }
    pagingRef.value.reload()
    getList(1)
  }

  const onTimeConfirm = p => {
    state.timePicker = {
      ...state.timePicker,
      startTime: dayjs(`${p.year}-${p.month}`).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(`${p.year}-${p.month}`).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      title: `${p.year}-${p.month}`,
    }

    pagingRef.value.reload()
    getList(1)
    dropdownRef.value.close()
  }
  const onTimeCancel = () => {
    state.timePicker = {
      ...state.timePicker,
      startTime: undefined,
      endTime: undefined,
      title: '盘点日期',
    }
    pagingRef.value.reload()
    getList(1)
    dropdownRef.value.close()
  }
  const onTimeClose = () => {
    dropdownRef.value.close()
  }

  const onDropdownOpen = index => {
    if (index === 2) {
      nextTick(() => {
        state.timePicker.showPicker = true
      })
    }
  }

  const onItemClick = el => {
    router.push({ path: '/pages/warehouse/inventory-check/detail/index', query: el })
  }

  const onAddClick = () => {
    router.push({ path: '/pages/warehouse/inventory-check/add/index' })
  }
</script>

<style lang="scss" scoped>
  .add-btn {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: linear-gradient(44deg, #3772ff 13%, #5ddcf5 82%);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 32rpx;
      background: #e8f3ff;

      .header-name {
        font-size: 28rpx;
        color: #4e5969;
        line-height: 28rpx;
      }
    }

    .content {
      padding: 0 32rpx 32rpx;
      .content-title {
        font-size: 32rpx;
        color: #1d2129;
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        .title {
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .indicate-item {
        font-size: 28rpx;
        color: #86909c;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
</style>
