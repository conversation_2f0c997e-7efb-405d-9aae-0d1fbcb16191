lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@dcloudio/uni-app':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(@dcloudio/types@3.4.3)(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-app-plus':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@dcloudio/uni-components':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-h5':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-alipay':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-baidu':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-jd':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-kuaishou':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-lark':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-qq':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-toutiao':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-weixin':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-quickapp-webview':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@turf/turf':
        specifier: ^6.5.0
        version: 6.5.0
      dayjs:
        specifier: ^1.11.7
        version: 1.11.10
      echarts:
        specifier: ^5.4.3
        version: 5.4.3
      gcoord:
        specifier: ^1.0.5
        version: 1.0.5
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      mapbox-gl:
        specifier: ^2.15.0
        version: 2.15.0
      md5:
        specifier: ^2.3.0
        version: 2.3.0
      pinia:
        specifier: 2.0.33
        version: 2.0.33(typescript@4.9.5)(vue@3.2.45)
      query-string:
        specifier: ^8.1.0
        version: 8.1.0
      qweather-icons:
        specifier: ^1.6.0
        version: 1.6.0
      uni-mini-router:
        specifier: ^0.1.5
        version: 0.1.5
      uni-parse-pages:
        specifier: ^0.0.1
        version: 0.0.1
      uniapp-axios-adapter:
        specifier: ^0.3.2
        version: 0.3.2(axios@1.6.2)
      vk-uview-ui:
        specifier: ^1.5.2
        version: 1.5.2
      vue:
        specifier: 3.2.45
        version: 3.2.45
      z-paging:
        specifier: ^2.6.3
        version: 2.6.3
    devDependencies:
      '@dcloudio/types':
        specifier: ^3.3.2
        version: 3.4.3
      '@dcloudio/uni-automator':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(jest-environment-node@27.5.1)(jest@27.0.4)(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-cli-shared':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-stacktracey':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001
      '@dcloudio/vite-plugin-uni':
        specifier: 3.0.0-3081220230817001
        version: 3.0.0-3081220230817001(postcss@8.4.32)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@esbuild/darwin-arm64':
        specifier: ^0.19.9
        version: 0.19.12
      '@esbuild/darwin-x64':
        specifier: 0.16.17
        version: 0.16.17
      '@uni-helper/vite-plugin-uni-tailwind':
        specifier: ^0.13.1
        version: 0.13.1(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))
      '@vitejs/plugin-vue-jsx':
        specifier: ^3.1.0
        version: 3.1.0(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@vue/runtime-core':
        specifier: ^3.3.11
        version: 3.3.11
      '@vue/tsconfig':
        specifier: ^0.1.3
        version: 0.1.3(@types/node@20.10.4)
      pinia-plugin-unistorage:
        specifier: ^0.0.17
        version: 0.0.17(typescript@4.9.5)(vue@3.2.45)
      postcss-preset-env:
        specifier: ^9.3.0
        version: 9.3.0(postcss@8.4.32)
      sass:
        specifier: ^1.59.3
        version: 1.69.5
      sass-loader:
        specifier: ^10.5.0
        version: 10.5.0(sass@1.69.5)(webpack@5.89.0)
      tailwindcss:
        specifier: ^3.3.6
        version: 3.3.6
      typescript:
        specifier: ^4.9.4
        version: 4.9.5
      vite:
        specifier: 4.1.4
        version: 4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0)
      vite-plugin-require-transform:
        specifier: ^1.0.18
        version: 1.0.21
      vue-tsc:
        specifier: ^1.8.25
        version: 1.8.25(typescript@4.9.5)

packages:

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  '@ampproject/remapping@2.2.1':
    resolution: {integrity: sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.23.5':
    resolution: {integrity: sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.23.5':
    resolution: {integrity: sha512-uU27kfDRlhfKl+w1U6vp16IuvSLtjAxdArVXPa9BvLkrr7CYIsxH5adpHObeAGY/41+syctUWOZ140a2Rvkgjw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.23.5':
    resolution: {integrity: sha512-Cwc2XjUrG4ilcfOw4wBAK+enbdgwAcAJCfGUItPBKR7Mjw4aEfAFYrLxeRp4jWgtNIKn3n2AlBOfwwafl+42/g==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.23.5':
    resolution: {integrity: sha512-BPssCHrBD+0YrxviOa3QzpqwhNIXKEtOa2jQrm4FlmkC2apYgRnQcmPWiGZDlGxiNtltnUFolMe8497Esry+jA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.22.5':
    resolution: {integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.15':
    resolution: {integrity: sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.22.15':
    resolution: {integrity: sha512-y6EEzULok0Qvz8yyLkCvVX+02ic+By2UdOhylwUOvOn9dvYc9mKICJuuU1n1XBI02YWsNsnrY1kc6DVbjcXbtw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.23.5':
    resolution: {integrity: sha512-QELlRWxSpgdwdJzSJn4WAhKC+hvw/AtHbbrIoncKHkhKKR/luAlKkgBDcri1EzWAo8f8VvYVryEHN4tax/V67A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.22.15':
    resolution: {integrity: sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.4.3':
    resolution: {integrity: sha512-WBrLmuPP47n7PNwsZ57pqam6G/RGo1vw/87b0Blc53tZNGZ4x7YvZ6HgQe2vo1W/FR20OgjeZuGXzudPiXHFug==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-environment-visitor@7.22.20':
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.23.0':
    resolution: {integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.22.5':
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.23.0':
    resolution: {integrity: sha512-6gfrPwh7OuT6gZyJZvd6WbTfrqAo7vm4xCzAXOusKqq/vWdKXphTpj5klHKNmRUU6/QRGlBsyU9mAIPaWHlqJA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.22.15':
    resolution: {integrity: sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.23.3':
    resolution: {integrity: sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.22.5':
    resolution: {integrity: sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.22.5':
    resolution: {integrity: sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.22.20':
    resolution: {integrity: sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.22.20':
    resolution: {integrity: sha512-qsW0In3dbwQUbK8kejJ4R7IHVGwHJlV6lpG6UA7a9hSa2YEiAib+N1T2kr6PEeUT+Fl7najmSOS6SmAwCHK6Tw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-simple-access@7.22.5':
    resolution: {integrity: sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    resolution: {integrity: sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.22.6':
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.23.4':
    resolution: {integrity: sha512-803gmbQdqwdf4olxrX4AJyFBV/RTr3rSmOj0rKwesmzlfhYNDEs+/iOcznzpNWlJlIlTJC2QfPFcHB6DlzdVLQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.22.20':
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.23.5':
    resolution: {integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.22.20':
    resolution: {integrity: sha512-pms/UwkOpnQe/PDAEdV/d7dVCoBbB+R4FvYoHGZz+4VPcg7RtYy2KP7S2lbuWM6FCSgob5wshfGESbC/hzNXZw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.23.5':
    resolution: {integrity: sha512-oO7us8FzTEsG3U6ag9MfdF1iA/7Z6dz+MtFhifZk8C8o453rGJFFWUP1t+ULM9TUIAzC9uxXEiXjOiVMyd7QPg==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.23.4':
    resolution: {integrity: sha512-acGdbYSfp2WheJoJm/EBBBLh/ID8KDc64ISZ9DYtBmC8/Q204PZJLHyzeB5qMzJ5trcOkybd78M4x2KWsUq++A==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.23.5':
    resolution: {integrity: sha512-hOOqoiNXrmGdFbhgCzu6GiURxUgM27Xwd/aPuu8RfHEZPBzL1Z54okAHAQjXfcQNwvrlkAmAp4SlRTZ45vlthQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3':
    resolution: {integrity: sha512-iRkKcCqb7iGnq9+3G6rZ+Ciz5VywC4XNRHe57lKM+jOeYAoR0lVqdeeDRfh0tQcTfw/+vBhHn926FmQhLtlFLQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.23.3':
    resolution: {integrity: sha512-WwlxbfMNdVEpQjZmK5mhm7oSwD3dS6eU+Iwsi4Knl9wAletWem7kaRsGOG+8UEbRyqxY4SS5zvtfXwX+jMxUwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.23.3':
    resolution: {integrity: sha512-XaJak1qcityzrX0/IU5nKHb34VaibwP3saKqG6a/tppelgllOH13LUann4ZCIBcVOeE6H18K4Vx9QKkVww3z/w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-bigint@7.8.3':
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-export-namespace-from@7.8.3':
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.23.3':
    resolution: {integrity: sha512-lPgDSU+SJLK3xmFDTV2ZRQAiM7UuUjGidwBywFavObCiZc1BeAAcMtHJKUya92hPHO+at63JJPLygilZard8jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.23.3':
    resolution: {integrity: sha512-pawnE0P9g10xgoP7yKr6CK63K2FMsTE+FZidZO/1PwRdzmAPVs+HS1mAURUsgaoxammTJvULUdIkEK0gOcU2tA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.23.3':
    resolution: {integrity: sha512-EB2MELswq55OHUoRZLGg/zC7QWUKfNLpE57m/S2yr1uEneIgsTgrSzXP3NXEsMkVn76OlaVVnzN+ugObuYGwhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.23.3':
    resolution: {integrity: sha512-9EiNjVJOMwCO+43TqoTrgQ8jMwcAd0sWyXi9RPfIsLTj4R2MADDDQXELhffaUx/uJv2AYcxBgPwH6j4TIA4ytQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.23.3':
    resolution: {integrity: sha512-NzQcQrzaQPkaEwoTm4Mhyl8jI1huEL/WWIEvudjTCMJ9aBZNpsJbMASx7EQECtQQPS/DcnFpo0FIh3LvEO9cxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.23.4':
    resolution: {integrity: sha512-efdkfPhHYTtn0G6n2ddrESE91fgXxjlqLsnUtPWnJs4a4mZIbUaK7ffqKIIUKXSHwcDvaCVX6GXkaJJFqtX7jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.23.3':
    resolution: {integrity: sha512-A7LFsKi4U4fomjqXJlZg/u0ft/n8/7n7lpffUP/ZULx/DtV9SGlNKZolHH6PE8Xl1ngCc0M11OaeZptXVkfKSw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.23.3':
    resolution: {integrity: sha512-vI+0sIaPIO6CNuM9Kk5VmXcMVRiOpDh7w2zZt9GXzmE/9KD70CUEVhvPR/etAeNK/FAEkhxQtXOzVF3EuRL41A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.23.4':
    resolution: {integrity: sha512-0QqbP6B6HOh7/8iNR4CQU2Th/bbRtBp4KS9vcaZd1fZ0wSh5Fyssg0UCIHwxh+ka+pNDREbVLQnHCMHKZfPwfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.23.3':
    resolution: {integrity: sha512-uM+AN8yCIjDPccsKGlw271xjJtGii+xQIF/uMPS8H15L12jZTsLfF4o5vNO7d/oUguOyfdikHGc/yi9ge4SGIg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.23.4':
    resolution: {integrity: sha512-nsWu/1M+ggti1SOALj3hfx5FXzAY06fwPJsUZD4/A5e1bWi46VUIWtD+kOX6/IdhXGsXBWllLFDSnqSCdUNydQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.23.5':
    resolution: {integrity: sha512-jvOTR4nicqYC9yzOHIhXG5emiFEOpappSJAl73SDSEDcybD+Puuze8Tnpb9p9qEyYup24tq891gkaygIFvWDqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.23.3':
    resolution: {integrity: sha512-dTj83UVTLw/+nbiHqQSFdwO9CbTtwq1DsDqm3CUEtDrZNET5rT5E6bIdTlOftDTDLMYxvxHNEYO4B9SLl8SLZw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.23.3':
    resolution: {integrity: sha512-n225npDqjDIr967cMScVKHXJs7rout1q+tt50inyBCPkyZ8KxeI6d+GIbSBTT/w/9WdlWDOej3V9HE5Lgk57gw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.23.3':
    resolution: {integrity: sha512-vgnFYDHAKzFaTVp+mneDsIEbnJ2Np/9ng9iviHw3P/KVcgONxpNULEW/51Z/BaFojG2GI2GwwXck5uV1+1NOYQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.23.3':
    resolution: {integrity: sha512-RrqQ+BQmU3Oyav3J+7/myfvRCq7Tbz+kKLLshUmMwNlDHExbGL7ARhajvoBJEvc+fCguPPu887N+3RRXBVKZUA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dynamic-import@7.23.4':
    resolution: {integrity: sha512-V6jIbLhdJK86MaLh4Jpghi8ho5fGzt3imHOBu/x0jlBaPYqDoWz4RDXjmMOfnh+JWNaQleEAByZLV0QzBT4YQQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.23.3':
    resolution: {integrity: sha512-5fhCsl1odX96u7ILKHBj4/Y8vipoqwsJMh4csSA8qFfxrZDEA4Ssku2DyNvMJSmZNOEBT750LfFPbtrnTP90BQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.23.4':
    resolution: {integrity: sha512-GzuSBcKkx62dGzZI1WVgTWvkkz84FZO5TC5T8dl/Tht/rAla6Dg/Mz9Yhypg+ezVACf/rgDuQt3kbWEv7LdUDQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.23.3':
    resolution: {integrity: sha512-X8jSm8X1CMwxmK878qsUGJRmbysKNbdpTv/O1/v0LuY/ZkZrng5WYiekYSdg9m09OTmDDUWeEDsTE+17WYbAZw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.23.3':
    resolution: {integrity: sha512-I1QXp1LxIvt8yLaib49dRW5Okt7Q4oaxao6tFVKS/anCdEOMtYwWVKoiOA1p34GOWIZjUK0E+zCp7+l1pfQyiw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.23.4':
    resolution: {integrity: sha512-81nTOqM1dMwZ/aRXQ59zVubN9wHGqk6UtqRK+/q+ciXmRy8fSolhGVvG09HHRGo4l6fr/c4ZhXUQH0uFW7PZbg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.23.3':
    resolution: {integrity: sha512-wZ0PIXRxnwZvl9AYpqNUxpZ5BiTGrYt7kueGQ+N5FiQ7RCOD4cm8iShd6S6ggfVIWaJf2EMk8eRzAh52RfP4rQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.23.4':
    resolution: {integrity: sha512-Mc/ALf1rmZTP4JKKEhUwiORU+vcfarFVLfcFiolKUo6sewoxSEgl36ak5t+4WamRsNr6nzjZXQjM35WsU+9vbg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.23.3':
    resolution: {integrity: sha512-sC3LdDBDi5x96LA+Ytekz2ZPk8i/Ck+DEuDbRAll5rknJ5XRTSaPKEYwomLcs1AA8wg9b3KjIQRsnApj+q51Ag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.23.3':
    resolution: {integrity: sha512-vJYQGxeKM4t8hYCKVBlZX/gtIY2I7mRGFNcm85sgXGMTBcoV3QdVtdpbcWEbzbfUIUZKwvgFT82mRvaQIebZzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.23.3':
    resolution: {integrity: sha512-aVS0F65LKsdNOtcz6FRCpE4OgsP2OFnW46qNxNIX9h3wuzaNcSQsJysuMwqSibC98HPrf2vCgtxKNwS0DAlgcA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.23.3':
    resolution: {integrity: sha512-ZxyKGTkF9xT9YJuKQRo19ewf3pXpopuYQd8cDXqNzc3mUNbOME0RKMoZxviQk74hwzfQsEe66dE92MaZbdHKNQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.23.3':
    resolution: {integrity: sha512-zHsy9iXX2nIsCBFPud3jKn1IRPWg3Ing1qOZgeKV39m1ZgIdpJqvlWVeiHBZC6ITRG0MfskhYe9cLgntfSFPIg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5':
    resolution: {integrity: sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.23.3':
    resolution: {integrity: sha512-YJ3xKqtJMAT5/TIZnpAR3I+K+WaDowYbN3xyxI8zxx/Gsypwf9B9h0VB+1Nh6ACAAPRS5NSRje0uVv5i79HYGQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.23.4':
    resolution: {integrity: sha512-jHE9EVVqHKAQx+VePv5LLGHjmHSJR76vawFPTdlxR/LVJPfOEGxREQwQfjuZEOPTwG92X3LINSh3M40Rv4zpVA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.23.4':
    resolution: {integrity: sha512-mps6auzgwjRrwKEZA05cOwuDc9FAzoyFS4ZsG/8F43bTLf/TgkJg7QXOrPO1JO599iA3qgK9MXdMGOEC8O1h6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.23.4':
    resolution: {integrity: sha512-9x9K1YyeQVw0iOXJlIzwm8ltobIIv7j2iLyP2jIhEbqPRQ7ScNgwQufU2I0Gq11VjyG4gI4yMXt2VFags+1N3g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.23.3':
    resolution: {integrity: sha512-BwQ8q0x2JG+3lxCVFohg+KbQM7plfpBwThdW9A6TMtWwLsbDA01Ek2Zb/AgDN39BiZsExm4qrXxjk+P1/fzGrA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.23.4':
    resolution: {integrity: sha512-XIq8t0rJPHf6Wvmbn9nFxU6ao4c7WhghTR5WyV8SrJfUFzyxhCm4nhC+iAp3HFhbAKLfYpgzhJ6t4XCtVwqO5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.23.4':
    resolution: {integrity: sha512-ZU8y5zWOfjM5vZ+asjgAPwDaBjJzgufjES89Rs4Lpq63O300R/kOz30WCLo6BxxX6QVEilwSlpClnG5cZaikTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.23.3':
    resolution: {integrity: sha512-09lMt6UsUb3/34BbECKVbVwrT9bO6lILWln237z7sLaWnMsTi7Yc9fhX5DLpkJzAGfaReXI22wP41SZmnAA3Vw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.23.3':
    resolution: {integrity: sha512-UzqRcRtWsDMTLrRWFvUBDwmw06tCQH9Rl1uAjfh6ijMSmGYQ+fpdB+cnqRC8EMh5tuuxSv0/TejGL+7vyj+50g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.23.4':
    resolution: {integrity: sha512-9G3K1YqTq3F4Vt88Djx1UZ79PDyj+yKRnUy7cZGSMe+a7jkwD259uKKuUzQlPkGam7R+8RJwh5z4xO27fA1o2A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.23.3':
    resolution: {integrity: sha512-jR3Jn3y7cZp4oEWPFAlRsSWjxKe4PZILGBSd4nis1TsC5qeSpb+nrtihJuDhNI7QHiVbUaiXa0X2RZY3/TI6Nw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.23.3':
    resolution: {integrity: sha512-KP+75h0KghBMcVpuKisx3XTu9Ncut8Q8TuvGO4IhY+9D5DFEckQefOuIsB/gQ2tG71lCke4NMrtIPS8pOj18BQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-reserved-words@7.23.3':
    resolution: {integrity: sha512-QnNTazY54YqgGxwIexMZva9gqbPa15t/x9VS+0fsEFWplwVpXYZivtgl43Z1vMpc1bdPP2PP8siFeVcnFvA3Cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.23.3':
    resolution: {integrity: sha512-ED2fgqZLmexWiN+YNFX26fx4gh5qHDhn1O2gvEhreLW2iI63Sqm4llRLCXALKrCnbN4Jy0VcMQZl/SAzqug/jg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.23.3':
    resolution: {integrity: sha512-VvfVYlrlBVu+77xVTOAoxQ6mZbnIq5FM0aGBSFEcIh03qHf+zNqA4DC/3XMUozTg7bZV3e3mZQ0i13VB6v5yUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.23.3':
    resolution: {integrity: sha512-HZOyN9g+rtvnOU3Yh7kSxXrKbzgrm5X4GncPY1QOquu7epga5MxKHVpYu2hvQnry/H+JjckSYRb93iNfsioAGg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.23.3':
    resolution: {integrity: sha512-Flok06AYNp7GV2oJPZZcP9vZdszev6vPBkHLwxwSpaIqx75wn6mUd3UFWsSsA0l8nXAKkyCmL/sR02m8RYGeHg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.23.3':
    resolution: {integrity: sha512-4t15ViVnaFdrPC74be1gXBSMzXk3B4Us9lP7uLRQHTFpV5Dvt33pn+2MyyNxmN3VTTm3oTrZVMUmuw3oBnQ2oQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.23.5':
    resolution: {integrity: sha512-2fMkXEJkrmwgu2Bsv1Saxgj30IXZdJ+84lQcKKI7sm719oXs0BBw2ZENKdJdR1PjWndgLCEBNXJOri0fk7RYQA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.23.3':
    resolution: {integrity: sha512-OMCUx/bU6ChE3r4+ZdylEqAjaQgHAgipgW8nsCfu5pGqDcFytVd91AwRvUJSBZDz0exPGgnjoqhgRYLRjFZc9Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.23.3':
    resolution: {integrity: sha512-KcLIm+pDZkWZQAFJ9pdfmh89EwVfmNovFBcXko8szpBeF8z68kWIPeKlmSOkT9BXJxs2C0uk+5LxoxIv62MROA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.23.3':
    resolution: {integrity: sha512-wMHpNA4x2cIA32b/ci3AfwNgheiva2W0WUKWTK7vBHBhDKfPsc5cFGNWm69WBqpwd86u1qwZ9PWevKqm1A3yAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.23.3':
    resolution: {integrity: sha512-W7lliA/v9bNR83Qc3q1ip9CQMZ09CcHDbHfbLRDNuAhn1Mvkr1ZNF7hPmztMQvtTGVLJ9m8IZqWsTkXOml8dbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.23.5':
    resolution: {integrity: sha512-0d/uxVD6tFGWXGDSfyMD1p2otoaKmu6+GD+NfAx0tMaH+dxORnp7T9TaVQ6mKyya7iBtCIVxHjWT7MuzzM9z+A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/regjsgen@0.8.0':
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}

  '@babel/runtime@7.23.5':
    resolution: {integrity: sha512-NdUTHcPe4C99WxPub+K9l9tK5/lV4UXIoaHSYgzco9BCyjKAAwzdBI+wWtYqHt7LJdbo74ZjRPJgzVweq1sz0w==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.22.15':
    resolution: {integrity: sha512-QPErUVm4uyJa60rkI73qneDacvdvzxshT3kksGqlGWYdOTIUOwJ7RDUL8sGqslY1uXWSL6xMFKEXDS3ox2uF0w==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.23.5':
    resolution: {integrity: sha512-czx7Xy5a6sapWWRx61m1Ke1Ra4vczu1mCTtJam5zRTBOonfdJ+S/B6HYmGYu3fJtr8GGET3si6IhgWVBhJ/m8w==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.23.5':
    resolution: {integrity: sha512-ON5kSOJwVO6xXVRTvOI0eOnWe7VdUcIpsovGo9U/Br4Ie4UVFQTboO2cYnDhAGU6Fp+UxSiT+pMft0SMHfuq6w==}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@0.2.3':
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==}

  '@csstools/cascade-layer-name-parser@1.0.5':
    resolution: {integrity: sha512-v/5ODKNBMfBl0us/WQjlfsvSlYxfZLhNMVIsuCPib2ulTwGKYbKJbwqw671+qH9Y4wvWVnu7LBChvml/wBKjFg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.3.2
      '@csstools/css-tokenizer': ^2.2.1

  '@csstools/color-helpers@3.0.2':
    resolution: {integrity: sha512-NMVs/l7Y9eIKL5XjbCHEgGcG8LOUT2qVcRjX6EzkCdlvftHVKr2tHIPzHavfrULRZ5Q2gxrJ9f44dAlj6fX97Q==}
    engines: {node: ^14 || ^16 || >=18}

  '@csstools/css-calc@1.1.4':
    resolution: {integrity: sha512-ZV1TSmToiNcQL1P3hfzlzZzA02mmVkVmXGaUDUqpYUG84PmLhVSZpKX+KfxAuOcK7de04UXSQPBrAvaya6iiGg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.3.2
      '@csstools/css-tokenizer': ^2.2.1

  '@csstools/css-color-parser@1.4.0':
    resolution: {integrity: sha512-SlGd8E6ron24JYQPQAIzu5tvmWi1H4sDKTdA7UDnwF45oJv7AVESbOlOO1YjfBhrQFuvLWUgKiOY9DwGoAxwTA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.3.2
      '@csstools/css-tokenizer': ^2.2.1

  '@csstools/css-parser-algorithms@2.3.2':
    resolution: {integrity: sha512-sLYGdAdEY2x7TSw9FtmdaTrh2wFtRJO5VMbBrA8tEqEod7GEggFmxTSK9XqExib3yMuYNcvcTdCZIP6ukdjAIA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-tokenizer': ^2.2.1

  '@csstools/css-tokenizer@2.2.1':
    resolution: {integrity: sha512-Zmsf2f/CaEPWEVgw29odOj+WEVoiJy9s9NOv5GgNY9mZ1CZ7394By6wONrONrTsnNDv6F9hR02nvFihrGVGHBg==}
    engines: {node: ^14 || ^16 || >=18}

  '@csstools/media-query-list-parser@2.1.5':
    resolution: {integrity: sha512-IxVBdYzR8pYe89JiyXQuYk4aVVoCPhMJkz6ElRwlVysjwURTsTk/bmY/z4FfeRE+CRBMlykPwXEVUg8lThv7AQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.3.2
      '@csstools/css-tokenizer': ^2.2.1

  '@csstools/postcss-cascade-layers@4.0.1':
    resolution: {integrity: sha512-UYFuFL9GgVnftg9v7tBvVEBRLaBeAD66euD+yYy5fYCUld9ZIWTJNCE30hm6STMEdt6FL5xzeVw1lAZ1tpvUEg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-color-function@3.0.7':
    resolution: {integrity: sha512-/PIB20G1TPCXmQlaJLWIYzTZRZpj6csT4ijgnshIj/kcmniIRroAfDa0xSWnfuO1eNo0NptIaPU7jzUukWn55Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-color-mix-function@2.0.7':
    resolution: {integrity: sha512-57/g8aGo5eKFjEeJMiRKh8Qq43K2rCyk5ZZTvJ34TNl4zUtYU5DvLkIkOnhCtL8/a4z9oMA42aOnFPddRrScUQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-exponential-functions@1.0.1':
    resolution: {integrity: sha512-ZLK2iSK4DUxeypGce2PnQSdYugUqDTwxnhNiq1o6OyKMNYgYs4eKbvEhFG8JKr1sJWbeqBi5jRr0017l2EWVvg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-font-format-keywords@3.0.0':
    resolution: {integrity: sha512-ntkGj+1uDa/u6lpjPxnkPcjJn7ChO/Kcy08YxctOZI7vwtrdYvFhmE476dq8bj1yna306+jQ9gzXIG/SWfOaRg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-gamut-mapping@1.0.0':
    resolution: {integrity: sha512-6UQyK8l9YaG5Ao5rBDcCnKHrLsHiQ1E0zeFQuqDJqEtinVzAPb/MwSw3TenZXL1Rnd7th3tb+4CBFHBXdW5tbQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-gradients-interpolation-method@4.0.7':
    resolution: {integrity: sha512-GT1CzE/Tyr/ei4j5BwKESkHAgg+Gzys/0mAY7W+UiR+XrcYk5hDbOrE/YJIx1rflfO/7La1bDoZtA0YnLl4qNA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-hwb-function@3.0.6':
    resolution: {integrity: sha512-uQgWt2Ho2yy2S6qthWY7mD5v57NKxi6dD1NB8nAybU5bJSsm+hLXRGm3/zbOH4xNrqO3Cl60DFSNlSrUME3Xjg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-ic-unit@3.0.2':
    resolution: {integrity: sha512-n28Er7W9qc48zNjJnvTKuVHY26/+6YlA9WzJRksIHiAWOMxSH5IksXkw7FpkIOd+jLi59BMrX/BWrZMgjkLBHg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-initial@1.0.0':
    resolution: {integrity: sha512-1l7iHHjIl5qmVeGItugr4ZOlCREDP71mNKqoEyxlosIoiu3Os1nPWMHpuCvDLCLiWI/ONTOg3nzJh7gwHOrqUA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-is-pseudo-class@4.0.3':
    resolution: {integrity: sha512-/dt5M9Ty/x3Yiq0Nm/5PJJzwkVFchJgdjKVnryBPtoMCb9ohb/nDIJOwr/Wr3hK3FDs1EA1GE6PyRYsUmQPS8Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-float-and-clear@2.0.0':
    resolution: {integrity: sha512-Wki4vxsF6icRvRz8eF9bPpAvwaAt0RHwhVOyzfoFg52XiIMjb6jcbHkGxwpJXP4DVrnFEwpwmrz5aTRqOW82kg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-overflow@1.0.0':
    resolution: {integrity: sha512-cIrZ8f7bGGvr+W53nEuMspcwaeaI2YTmz6LZ4yiAO5z14/PQgOOv+Pn+qjvPOPoadeY2BmpaoTzZKvdAQuM17w==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-overscroll-behavior@1.0.0':
    resolution: {integrity: sha512-e89S2LWjnxf0SB2wNUAbqDyFb/Fow/tlOe1XqOLbNx4rf3LrQokM9qldVx7sarnddml3ORE5LDUmlKpPOOeJTA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-resize@2.0.0':
    resolution: {integrity: sha512-lCQ1aX8c5+WI4t5EoYf3alTzJNNocMqTb+u1J9CINdDhFh1fjovqK+0aHalUHsNstZmzFPNzIkU4Mb3eM9U8SA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-viewport-units@2.0.3':
    resolution: {integrity: sha512-xeVxqND5rlQyqLGdH7rX34sIm/JbbQKxpKQP8oD1YQqUHHCLQR9NUS57WqJKajxKN6AcNAMWJhb5LUH5RfPcyA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-media-minmax@1.1.0':
    resolution: {integrity: sha512-t5Li/DPC5QmW/6VFLfUvsw/4dNYYseWR0tOXDeJg/9EKUodBgNawz5tuk5vYKtNvoj+Q08odMuXcpS5YJj0AFA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-media-queries-aspect-ratio-number-values@2.0.3':
    resolution: {integrity: sha512-IPL8AvnwMYW+cWtp+j8cW3MFN0RyXNT4hLOvs6Rf2N+NcbvXhSyKxZuE3W9Cv4KjaNoNoGx1d0UhT6tktq6tUw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-nested-calc@3.0.0':
    resolution: {integrity: sha512-HsB66aDWAouOwD/GcfDTS0a7wCuVWaTpXcjl5VKP0XvFxDiU+r0T8FG7xgb6ovZNZ+qzvGIwRM+CLHhDgXrYgQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-normalize-display-values@3.0.1':
    resolution: {integrity: sha512-nUvRxI+ALJwkxZdPU4EDyuM380vP91sAGvI3jAOHs/sr3jfcCOzLkY6xKI1Mr526kZ3RivmMoYM/xq+XFyE/bw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-oklab-function@3.0.7':
    resolution: {integrity: sha512-vBFTQD3CARB3u/XIGO44wWbcO7xG/4GsYqJlcPuUGRSK8mtxes6n4vvNFlIByyAZy2k4d4RY63nyvTbMpeNTaQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-progressive-custom-properties@3.0.2':
    resolution: {integrity: sha512-YEvTozk1SxnV/PGL5DllBVDuLQ+jiQhyCSQiZJ6CwBMU5JQ9hFde3i1qqzZHuclZfptjrU0JjlX4ePsOhxNzHw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-relative-color-syntax@2.0.7':
    resolution: {integrity: sha512-2AiFbJSVF4EyymLxme4JzSrbXykHolx8DdZECHjYKMhoulhKLltx5ccYgtrK3BmXGd3v3nJrWFCc8JM8bjuiOg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-scope-pseudo-class@3.0.0':
    resolution: {integrity: sha512-GFNVsD97OuEcfHmcT0/DAZWAvTM/FFBDQndIOLawNc1Wq8YqpZwBdHa063Lq+Irk7azygTT+Iinyg3Lt76p7rg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-stepped-value-functions@3.0.2':
    resolution: {integrity: sha512-I3wX44MZVv+tDuWfrd3BTvRB/YRIM2F5v1MBtTI89sxpFn47mNpTwpPYUOGPVCgKlRDfZSlxIUYhUQmqRQZZFQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-text-decoration-shorthand@3.0.3':
    resolution: {integrity: sha512-d5J9m49HhqXRcw1S6vTZuviHi/iknUKGjBpChiNK1ARg9sSa3b8m5lsWz5Izs8ISORZdv2bZRwbw5Z2R6gQ9kQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-trigonometric-functions@3.0.2':
    resolution: {integrity: sha512-AwzNhF4QOKaLOKvMljwwFkeYXwufhRO15G+kKohHkyoNOL75xWkN+W2Y9ik9tSeAyDv+cYNlYaF+o/a79WjVjg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-unset-value@3.0.0':
    resolution: {integrity: sha512-P0JD1WHh3avVyKKRKjd0dZIjCEeaBer8t1BbwGMUDtSZaLhXlLNBqZ8KkqHzYWXOJgHleXAny2/sx8LYl6qhEA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  '@csstools/selector-specificity@3.0.0':
    resolution: {integrity: sha512-hBI9tfBtuPIi885ZsZ32IMEU/5nlZH/KOVYJCOh7gyMxaVLGmLedYqFN6Ui1LXkI8JlC8IsuC0rF0btcRZKd5g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.13

  '@dcloudio/types@3.4.3':
    resolution: {integrity: sha512-WzQGX06z2+HU3HnOO+/DxX37jyUECSXCuI7GVjFXs10+ZExTbdouwQXZvH8hR7k/FjuXFjQKHV9fuvfexyXluw==}

  '@dcloudio/uni-app-plus@3.0.0-3081220230817001':
    resolution: {integrity: sha512-GpQobx3WbcyOZ49f8JM+febzKvlthFBAxcs4xrbtNVzmkj0jLJoh9GoH37oa7m5DI1k4l5wUuwvg4/JxBbhOSA==}

  '@dcloudio/uni-app-uts@3.0.0-3081220230817001':
    resolution: {integrity: sha512-snMFEpTw5T0TT5uFwp0piytLRB0FBRHDM5ML00E1nrzfts0XlaFKoB4UsvLySG5sE5+Xt8F5/uzKxid3mcgTLQ==}

  '@dcloudio/uni-app-vite@3.0.0-3081220230817001':
    resolution: {integrity: sha512-iHmKh6hjPGh+FC6Fi4qqwT5N31QiWv9JymCLdbkOJRi71A3byDjxoH7ScSEEEg+A5AAPky6LHbmFmznPMeSwAg==}

  '@dcloudio/uni-app-vue@3.0.0-3081220230817001':
    resolution: {integrity: sha512-08ZUIn2a+OZWjqvjTbLygWZOY9YXbf2J1ns2letBI9aoMZDx+Sgx2lUFWHDksmOhvApPLKrRjJM0Hmmxi2mjJw==}

  '@dcloudio/uni-app@3.0.0-3081220230817001':
    resolution: {integrity: sha512-sf8PzXHSd/VFCn7X9+QbD4nrY21FnIwjrhyX2hFit7PZK9kpyQluqCmG7YuotuXJzsNML8c8u2O9q5FaeOyTHg==}
    peerDependencies:
      '@dcloudio/types': ^3.3.2

  '@dcloudio/uni-automator@3.0.0-3081220230817001':
    resolution: {integrity: sha512-boaRlUAAXsEh4aN5PbtMVvzdmvXqoEWa8eQBD4oCH36lyGgK8q75vsjLRi4LBFFwNlCNMrYepTNbBCjP+Sgt0g==}
    peerDependencies:
      jest: 27.0.4
      jest-environment-node: 27.5.1

  '@dcloudio/uni-cli-shared@3.0.0-3081220230817001':
    resolution: {integrity: sha512-FOeydfIdKZg+MnQsHSFLXBzzmXtgCWnmEyjw3MHNN5mGwQB6+f7vK8sLw+yjizD/j71eMnh8XXCk+dV3nD/Vzg==}
    engines: {node: ^14.18.0 || >=16.0.0}

  '@dcloudio/uni-cloud@3.0.0-3081220230817001':
    resolution: {integrity: sha512-rrHN4PvvylrhmivAoSv4h8ZcQ3ZWHnMRXhQWPC9FBR1TInrrxrrAHuMqSZn6pTIXhCiRA3Kuy6aDNVCbt6VA+g==}

  '@dcloudio/uni-components@3.0.0-3081220230817001':
    resolution: {integrity: sha512-FlIrTgR/9Yp8FWYfhXfLPd0jJtBwJwRBAWWmLNaufkTojm6X7rX8wQOd13n+7gJGKzfXcEV8npOYpVou4P46Pw==}

  '@dcloudio/uni-h5-vite@3.0.0-3081220230817001':
    resolution: {integrity: sha512-x7F6mUTjHiOx7+lj5yIrzhD7gr0HTNJaku5gV3O8bFjxKLcIkFkAyliKPbqJ4QZCmCCmEfJpOGKrDhk35tlibg==}

  '@dcloudio/uni-h5-vue@3.0.0-3081220230817001':
    resolution: {integrity: sha512-q1eFoXb5/4whNIJ1CumKmeypPjZofw8xDQRHZeGZ7NVRQ0/MYnEm6A0n/vkmEo1IgTZIY83m/jr6RNg7a9Ko/A==}

  '@dcloudio/uni-h5@3.0.0-3081220230817001':
    resolution: {integrity: sha512-m2RCZpYxuAGnXlQKSQ8FGy4OlnUJJ8XMx2HNQWriDwtjDpAGDU8I2LZQZ7MHCNPIUd503y+mgU2NPlSQHnrlkQ==}

  '@dcloudio/uni-i18n@3.0.0-3081220230817001':
    resolution: {integrity: sha512-ooAIoNCy+DPlMtA4k9eoaJJsYQtici85le+ietIIzLdJoa2YUnudYv9CvG9Mw/RsKQI+MLl96ADAPgUpGzounw==}

  '@dcloudio/uni-mp-alipay@3.0.0-3081220230817001':
    resolution: {integrity: sha512-lZR2u8sxJ/ZaDqaxEmGzgWWKHcjnLGedDTrMh8kOEPLHO6THVNe5TP6XeiTcE0HKJfWEXv6X13erjn0FCstz3A==}

  '@dcloudio/uni-mp-baidu@3.0.0-3081220230817001':
    resolution: {integrity: sha512-FYwK1AqSc+NvtvaCia/fOP57ELx5O5icHjuwPKMsJv5eGtqlwhXzyvSCoGEFyYQcsWP9ZqYSb3fTuxVlbiEYdA==}

  '@dcloudio/uni-mp-compiler@3.0.0-3081220230817001':
    resolution: {integrity: sha512-KDfwEr9jFNSkNbYmdDc+zfGuZwXGkz3bW3c4cuV44IncTf/UW2m594Z2UzhJkx/p662aQ2W9M4BFZ0o5QSfQDw==}

  '@dcloudio/uni-mp-jd@3.0.0-3081220230817001':
    resolution: {integrity: sha512-sKDppXhk7k8Euce9v7ItQ2cs3+XpQV/DX8FjXzfO9DfjQEKiR/Pyfis7e3ANQrF4V/W3uwK5wgN/PyI3/NRloQ==}

  '@dcloudio/uni-mp-kuaishou@3.0.0-3081220230817001':
    resolution: {integrity: sha512-wYXwoxcdms8PviNsKg2Y9oqHawKqseoD97sYs94RLDVSnyO9c6NOoUMsYjXwIZXt05E56bJ9EbxDqYDwwJhEKw==}

  '@dcloudio/uni-mp-lark@3.0.0-3081220230817001':
    resolution: {integrity: sha512-4aHwULp+Nx55BoJvj1jCXWnVJrzl0ACEbDlXVvxmNUI5tkqn2kYL4AZgrXP1vCx8MHKVPI4E34Z+lrBTfSfKMA==}

  '@dcloudio/uni-mp-qq@3.0.0-3081220230817001':
    resolution: {integrity: sha512-StSsxCCKMdZCghUZ+wdX1otCmlYkIV+O2Z/AxMv772HfIKlpDp44PPaxlKBG4L6+X7h2JsCvyZW1iSznM5A3CQ==}

  '@dcloudio/uni-mp-toutiao@3.0.0-3081220230817001':
    resolution: {integrity: sha512-ZMSNNjQpCV4z+76OA8rDKKhqr+vQmTvKNUOzKfND5KPYhgWbqviEBZ8ptiDT5gaWXMpkUHNfWz32hdi5CLbj5Q==}

  '@dcloudio/uni-mp-vite@3.0.0-3081220230817001':
    resolution: {integrity: sha512-jmEbsVOJ1dlj46QiSFMB2C3zbgGivkGf1KXoPTAprv0WZrkmjF1uNLj0pHFOOUnnyHwHPViEApsMOPS4twIjIQ==}

  '@dcloudio/uni-mp-vue@3.0.0-3081220230817001':
    resolution: {integrity: sha512-EfdTH6+qGXRh3402+psl8X4K34/gHfBPaMfxbwMsne1rGnB8ZVXiN73gVXKfCWeznvJqdWuTWmmjqaLoBhjOjA==}

  '@dcloudio/uni-mp-weixin@3.0.0-3081220230817001':
    resolution: {integrity: sha512-vo2k8OaWFOvoXuRX83UCR0AOOKXXwjRDIOX0y/jUC9TX63Ne5OTdRKH9253D9109FrwtrtsUWqnpfn1ynKXl9g==}

  '@dcloudio/uni-nvue-styler@3.0.0-3081220230817001':
    resolution: {integrity: sha512-EImnyrYHmAc7ibKfIMkBG1uMavS5sPdrtQj52m40o/QNU5kcsqUk2Od9c1ULkQloxhmK2E4FZvXo4cgszE/Txw==}

  '@dcloudio/uni-push@3.0.0-3081220230817001':
    resolution: {integrity: sha512-1IyVJlBnZVVvY49SQKpZG5Oxak5Wl51NvYEozuYoeBaaTeZObm7B6eyd722sXi0Dd6N1sE3kIdm/XReprK53eg==}

  '@dcloudio/uni-quickapp-webview@3.0.0-3081220230817001':
    resolution: {integrity: sha512-w6yitVyCKEFSO+UDsJPjc5RfAstIdOEbBHgim0gInTfOl90odmyUPKDxWhwQgS5vqezBXpCj0/6ywVMHvZu+7A==}

  '@dcloudio/uni-shared@3.0.0-3081220230817001':
    resolution: {integrity: sha512-NI1pBO40VqvnWjwNXad3CqrUYvr4ffGjiDMgJGMP13rgOEAqamU7ozBimoASDVPKyyfSHTeuYuh0gtaaLu4CsQ==}

  '@dcloudio/uni-stacktracey@3.0.0-3081220230817001':
    resolution: {integrity: sha512-DUTTN8JWXxjO3ryz7uhD3+vazHVMflln15+r0ycWBjXO0p0MepRY3gX5gKCyiq9D3NUIxazcFAUJ6agFVUbDog==}

  '@dcloudio/uni-stat@3.0.0-3081220230817001':
    resolution: {integrity: sha512-xpbsor5WKU1eCbo0dmQ0hBUX6RrdG7DNDzCSD6oimndIXVGLkFhWrIC3brQ7S1G2GQvXdXnXJpUVskI3qNe/uw==}

  '@dcloudio/vite-plugin-uni@3.0.0-3081220230817001':
    resolution: {integrity: sha512-dLeqqyrNNUBTZfI0ppPk4YoD+DBI0gJLeTjpN6bK5i3nFKkju9g2K6IWl62YMObjTiXwQF8qAEDb+fyWoe4Ksw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      vite: ^4.0.0

  '@esbuild/android-arm64@0.16.17':
    resolution: {integrity: sha512-MIGl6p5sc3RDTLLkYL1MyL8BMRN4tLMRCn+yRJJmEDvYZ2M7tmAf80hx1kbNEUX2KJ50RRtxZ4JHLvCfuB6kBg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm64@0.17.19':
    resolution: {integrity: sha512-KBMWvEZooR7+kzY0BtbTQn0OAYY7CsiydT63pVEaPtVYF0hXbUaOyZog37DKxK7NF3XacBJOpYT4adIJh+avxA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.16.17':
    resolution: {integrity: sha512-N9x1CMXVhtWEAMS7pNNONyA14f71VPQN9Cnavj1XQh6T7bskqiLLrSca4O0Vr8Wdcga943eThxnVp3JLnBMYtw==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-arm@0.17.19':
    resolution: {integrity: sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.16.17':
    resolution: {integrity: sha512-a3kTv3m0Ghh4z1DaFEuEDfz3OLONKuFvI4Xqczqx4BqLyuFaFkuaG4j2MtA6fuWEFeC5x9IvqnX7drmRq/fyAQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/android-x64@0.17.19':
    resolution: {integrity: sha512-uUTTc4xGNDT7YSArp/zbtmbhO0uEEK9/ETW29Wk1thYUJBz3IVnvgEiEwEa9IeLyvnpKrWK64Utw2bgUmDveww==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.16.17':
    resolution: {integrity: sha512-/2agbUEfmxWHi9ARTX6OQ/KgXnOWfsNlTeLcoV7HSuSTv63E4DqtAc+2XqGw1KHxKMHGZgbVCZge7HXWX9Vn+w==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.17.19':
    resolution: {integrity: sha512-80wEoCfF/hFKM6WE1FyBHc9SfUblloAWx6FJkFWTWiCoht9Mc0ARGEM47e67W9rI09YoUxJL68WHfDRYEAvOhg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.19.12':
    resolution: {integrity: sha512-B6IeSgZgtEzGC42jsI+YYu9Z3HKRxp8ZT3cqhvliEHovq8HSX2YX8lNocDn79gCKJXOSaEot9MVYky7AKjCs8g==}
    engines: {node: '>=12'}
    os: [darwin]

  '@esbuild/darwin-x64@0.16.17':
    resolution: {integrity: sha512-2By45OBHulkd9Svy5IOCZt376Aa2oOkiE9QWUK9fe6Tb+WDr8hXL3dpqi+DeLiMed8tVXspzsTAvd0jUl96wmg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/darwin-x64@0.17.19':
    resolution: {integrity: sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.16.17':
    resolution: {integrity: sha512-mt+cxZe1tVx489VTb4mBAOo2aKSnJ33L9fr25JXpqQqzbUIw/yzIzi+NHwAXK2qYV1lEFp4OoVeThGjUbmWmdw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-arm64@0.17.19':
    resolution: {integrity: sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.16.17':
    resolution: {integrity: sha512-8ScTdNJl5idAKjH8zGAsN7RuWcyHG3BAvMNpKOBaqqR7EbUhhVHOqXRdL7oZvz8WNHL2pr5+eIT5c65kA6NHug==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.17.19':
    resolution: {integrity: sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.16.17':
    resolution: {integrity: sha512-7S8gJnSlqKGVJunnMCrXHU9Q8Q/tQIxk/xL8BqAP64wchPCTzuM6W3Ra8cIa1HIflAvDnNOt2jaL17vaW+1V0g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm64@0.17.19':
    resolution: {integrity: sha512-ct1Tg3WGwd3P+oZYqic+YZF4snNl2bsnMKRkb3ozHmnM0dGWuxcPTTntAF6bOP0Sp4x0PjSF+4uHQ1xvxfRKqg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.16.17':
    resolution: {integrity: sha512-iihzrWbD4gIT7j3caMzKb/RsFFHCwqqbrbH9SqUSRrdXkXaygSZCZg1FybsZz57Ju7N/SHEgPyaR0LZ8Zbe9gQ==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-arm@0.17.19':
    resolution: {integrity: sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.16.17':
    resolution: {integrity: sha512-kiX69+wcPAdgl3Lonh1VI7MBr16nktEvOfViszBSxygRQqSpzv7BffMKRPMFwzeJGPxcio0pdD3kYQGpqQ2SSg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-ia32@0.17.19':
    resolution: {integrity: sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.16.17':
    resolution: {integrity: sha512-dTzNnQwembNDhd654cA4QhbS9uDdXC3TKqMJjgOWsC0yNCbpzfWoXdZvp0mY7HU6nzk5E0zpRGGx3qoQg8T2DQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-loong64@0.17.19':
    resolution: {integrity: sha512-2iAngUbBPMq439a+z//gE+9WBldoMp1s5GWsUSgqHLzLJ9WoZLZhpwWuym0u0u/4XmZ3gpHmzV84PonE+9IIdQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.16.17':
    resolution: {integrity: sha512-ezbDkp2nDl0PfIUn0CsQ30kxfcLTlcx4Foz2kYv8qdC6ia2oX5Q3E/8m6lq84Dj/6b0FrkgD582fJMIfHhJfSw==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-mips64el@0.17.19':
    resolution: {integrity: sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.16.17':
    resolution: {integrity: sha512-dzS678gYD1lJsW73zrFhDApLVdM3cUF2MvAa1D8K8KtcSKdLBPP4zZSLy6LFZ0jYqQdQ29bjAHJDgz0rVbLB3g==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-ppc64@0.17.19':
    resolution: {integrity: sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.16.17':
    resolution: {integrity: sha512-ylNlVsxuFjZK8DQtNUwiMskh6nT0vI7kYl/4fZgV1llP5d6+HIeL/vmmm3jpuoo8+NuXjQVZxmKuhDApK0/cKw==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-riscv64@0.17.19':
    resolution: {integrity: sha512-FC3nUAWhvFoutlhAkgHf8f5HwFWUL6bYdvLc/TTuxKlvLi3+pPzdZiFKSWz/PF30TB1K19SuCxDTI5KcqASJqA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.16.17':
    resolution: {integrity: sha512-gzy7nUTO4UA4oZ2wAMXPNBGTzZFP7mss3aKR2hH+/4UUkCOyqmjXiKpzGrY2TlEUhbbejzXVKKGazYcQTZWA/w==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-s390x@0.17.19':
    resolution: {integrity: sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.16.17':
    resolution: {integrity: sha512-mdPjPxfnmoqhgpiEArqi4egmBAMYvaObgn4poorpUaqmvzzbvqbowRllQ+ZgzGVMGKaPkqUmPDOOFQRUFDmeUw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/linux-x64@0.17.19':
    resolution: {integrity: sha512-68ngA9lg2H6zkZcyp22tsVt38mlhWde8l3eJLWkyLrp4HwMUr3c1s/M2t7+kHIhvMjglIBrFpncX1SzMckomGw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.16.17':
    resolution: {integrity: sha512-/PzmzD/zyAeTUsduZa32bn0ORug+Jd1EGGAUJvqfeixoEISYpGnAezN6lnJoskauoai0Jrs+XSyvDhppCPoKOA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.17.19':
    resolution: {integrity: sha512-CwFq42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.16.17':
    resolution: {integrity: sha512-2yaWJhvxGEz2RiftSk0UObqJa/b+rIAjnODJgv2GbGGpRwAfpgzyrg1WLK8rqA24mfZa9GvpjLcBBg8JHkoodg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.17.19':
    resolution: {integrity: sha512-cnq5brJYrSZ2CF6c35eCmviIN3k3RczmHz8eYaVlNasVqsNY+JKohZU5MKmaOI+KkllCdzOKKdPs762VCPC20g==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.16.17':
    resolution: {integrity: sha512-xtVUiev38tN0R3g8VhRfN7Zl42YCJvyBhRKw1RJjwE1d2emWTVToPLNEQj/5Qxc6lVFATDiy6LjVHYhIPrLxzw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/sunos-x64@0.17.19':
    resolution: {integrity: sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.16.17':
    resolution: {integrity: sha512-ga8+JqBDHY4b6fQAmOgtJJue36scANy4l/rL97W+0wYmijhxKetzZdKOJI7olaBaMhWt8Pac2McJdZLxXWUEQw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-arm64@0.17.19':
    resolution: {integrity: sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.16.17':
    resolution: {integrity: sha512-WnsKaf46uSSF/sZhwnqE4L/F89AYNMiD4YtEcYekBt9Q7nj0DiId2XH2Ng2PHM54qi5oPrQ8luuzGszqi/veig==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-ia32@0.17.19':
    resolution: {integrity: sha512-eggDKanJszUtCdlVs0RB+h35wNlb5v4TWEkq4vZcmVt5u/HiDZrTXe2bWFQUez3RgNHwx/x4sk5++4NSSicKkw==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.16.17':
    resolution: {integrity: sha512-y+EHuSchhL7FjHgvQL/0fnnFmO4T1bhvWANX6gcnqTjtnKWbTvUMCpGnv2+t+31d7RzyEAYAd4u2fnIhHL6N/Q==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@esbuild/win32-x64@0.17.19':
    resolution: {integrity: sha512-lAhycmKnVOuRYNtRtatQR1LPQf2oYCkRGkSFnseDAKPl8lu5SOsK/e1sXe5a0Pc5kHIHe6P2I/ilntNv2xf3cA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@intlify/core-base@9.1.9':
    resolution: {integrity: sha512-x5T0p/Ja0S8hs5xs+ImKyYckVkL4CzcEXykVYYV6rcbXxJTe2o58IquSqX9bdncVKbRZP7GlBU1EcRaQEEJ+vw==}
    engines: {node: '>= 10'}

  '@intlify/devtools-if@9.1.9':
    resolution: {integrity: sha512-oKSMKjttG3Ut/1UGEZjSdghuP3fwA15zpDPcjkf/1FjlOIm6uIBGMNS5jXzsZy593u+P/YcnrZD6cD3IVFz9vQ==}
    engines: {node: '>= 10'}

  '@intlify/message-compiler@9.1.9':
    resolution: {integrity: sha512-6YgCMF46Xd0IH2hMRLCssZI3gFG4aywidoWQ3QP4RGYQXQYYfFC54DxhSgfIPpVoPLQ+4AD29eoYmhiHZ+qLFQ==}
    engines: {node: '>= 10'}

  '@intlify/message-resolver@9.1.9':
    resolution: {integrity: sha512-Lx/DBpigeK0sz2BBbzv5mu9/dAlt98HxwbG7xLawC3O2xMF9MNWU5FtOziwYG6TDIjNq0O/3ZbOJAxwITIWXEA==}
    engines: {node: '>= 10'}

  '@intlify/runtime@9.1.9':
    resolution: {integrity: sha512-XgPw8+UlHCiie3fI41HPVa/VDJb3/aSH7bLhY1hJvlvNV713PFtb4p4Jo+rlE0gAoMsMCGcsiT982fImolSltg==}
    engines: {node: '>= 10'}

  '@intlify/shared@9.1.9':
    resolution: {integrity: sha512-xKGM1d0EAxdDFCWedcYXOm6V5Pfw/TMudd6/qCdEb4tv0hk9EKeg7lwQF1azE0dP2phvx0yXxrt7UQK+IZjNdw==}
    engines: {node: '>= 10'}

  '@intlify/vue-devtools@9.1.9':
    resolution: {integrity: sha512-YPehH9uL4vZcGXky4Ev5qQIITnHKIvsD2GKGXgqf+05osMUI6WSEQHaN9USRa318Rs8RyyPCiDfmA0hRu3k7og==}
    engines: {node: '>= 10'}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jest/console@27.5.1':
    resolution: {integrity: sha512-kZ/tNpS3NXn0mlXXXPNuDZnb4c0oZ20r4K5eemM2k30ZC3G0T02nXUvyhf5YdbXWHPEJLc9qGLxEZ216MdL+Zg==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/core@27.5.1':
    resolution: {integrity: sha512-AK6/UTrvQD0Cd24NSqmIA6rKsu0tKIxfiCducZvqxYdmMisOYAsdItspT+fQDQYARPf8XgjAFZi0ogW2agH5nQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/environment@27.5.1':
    resolution: {integrity: sha512-/WQjhPJe3/ghaol/4Bq480JKXV/Rfw8nQdN7f41fM8VDHLcxKXou6QyXAh3EFr9/bVG3x74z1NWDkP87EiY8gA==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/fake-timers@27.5.1':
    resolution: {integrity: sha512-/aPowoolwa07k7/oM3aASneNeBGCmGQsc3ugN4u6s4C/+s5M64MFo/+djTdiwcbQlRfFElGuDXWzaWj6QgKObQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/globals@27.5.1':
    resolution: {integrity: sha512-ZEJNB41OBQQgGzgyInAv0UUfDDj3upmHydjieSxFvTRuZElrx7tXg/uVQ5hYVEwiXs3+aMsAeEc9X7xiSKCm4Q==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/reporters@27.5.1':
    resolution: {integrity: sha512-cPXh9hWIlVJMQkVk84aIvXuBB4uQQmFqZiacloFuGiP3ah1sbCxCosidXFDfqG8+6fO1oR2dTJTlsOy4VFmUfw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/source-map@27.5.1':
    resolution: {integrity: sha512-y9NIHUYF3PJRlHk98NdC/N1gl88BL08aQQgu4k4ZopQkCw9t9cV8mtl3TV8b/YCB8XaVTFrmUTAJvjsntDireg==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/test-result@27.5.1':
    resolution: {integrity: sha512-EW35l2RYFUcUQxFJz5Cv5MTOxlJIQs4I7gxzi2zVU7PJhOwfYq1MdC5nhSmYjX1gmMmLPvB3sIaC+BkcHRBfag==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/test-sequencer@27.5.1':
    resolution: {integrity: sha512-LCheJF7WB2+9JuCS7VB/EmGIdQuhtqjRNI9A43idHv3E4KltCTsPsLxvdaubFHSYwY/fNjMWjl6vNRhDiN7vpQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/transform@27.5.1':
    resolution: {integrity: sha512-ipON6WtYgl/1329g5AIJVbUuEh0wZVbdpGwC99Jw4LwuoBNS95MVphU6zOeD9pDkon+LLbFL7lOQRapbB8SCHw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jest/types@27.5.1':
    resolution: {integrity: sha512-Cx46iJ9QpwQTjIdq5VJu2QTMMs3QlEjI0x1QbBP5W1+nMzyc2XmimiRR/CbX9TO0cPTeUlxWMOu8mslYsJ8DEw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  '@jimp/bmp@0.10.3':
    resolution: {integrity: sha512-keMOc5woiDmONXsB/6aXLR4Z5Q+v8lFq3EY2rcj2FmstbDMhRuGbmcBxlEgOqfRjwvtf/wOtJ3Of37oAWtVfLg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/core@0.10.3':
    resolution: {integrity: sha512-Gd5IpL3U2bFIO57Fh/OA3HCpWm4uW/pU01E75rI03BXfTdz3T+J7TwvyG1XaqsQ7/DSlS99GXtLQPlfFIe28UA==}

  '@jimp/custom@0.10.3':
    resolution: {integrity: sha512-nZmSI+jwTi5IRyNLbKSXQovoeqsw+D0Jn0SxW08wYQvdkiWA8bTlDQFgQ7HVwCAKBm8oKkDB/ZEo9qvHJ+1gAQ==}

  '@jimp/gif@0.10.3':
    resolution: {integrity: sha512-vjlRodSfz1CrUvvrnUuD/DsLK1GHB/yDZXHthVdZu23zYJIW7/WrIiD1IgQ5wOMV7NocfrvPn2iqUfBP81/WWA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/jpeg@0.10.3':
    resolution: {integrity: sha512-AAANwgUZOt6f6P7LZxY9lyJ9xclqutYJlsxt3JbriXUGJgrrFAIkcKcqv1nObgmQASSAQKYaMV9KdHjMlWFKlQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-blit@0.10.3':
    resolution: {integrity: sha512-5zlKlCfx4JWw9qUVC7GI4DzXyxDWyFvgZLaoGFoT00mlXlN75SarlDwc9iZ/2e2kp4bJWxz3cGgG4G/WXrbg3Q==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-blur@0.10.3':
    resolution: {integrity: sha512-cTOK3rjh1Yjh23jSfA6EHCHjsPJDEGLC8K2y9gM7dnTUK1y9NNmkFS23uHpyjgsWFIoH9oRh2SpEs3INjCpZhQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-circle@0.10.3':
    resolution: {integrity: sha512-51GAPIVelqAcfuUpaM5JWJ0iWl4vEjNXB7p4P7SX5udugK5bxXUjO6KA2qgWmdpHuCKtoNgkzWU9fNSuYp7tCA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-color@0.10.3':
    resolution: {integrity: sha512-RgeHUElmlTH7vpI4WyQrz6u59spiKfVQbsG/XUzfWGamFSixa24ZDwX/yV/Ts+eNaz7pZeIuv533qmKPvw2ujg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-contain@0.10.3':
    resolution: {integrity: sha512-bYJKW9dqzcB0Ihc6u7jSyKa3juStzbLs2LFr6fu8TzA2WkMS/R8h+ddkiO36+F9ILTWHP0CIA3HFe5OdOGcigw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
      '@jimp/plugin-scale': '>=0.3.5'

  '@jimp/plugin-cover@0.10.3':
    resolution: {integrity: sha512-pOxu0cM0BRPzdV468n4dMocJXoMbTnARDY/EpC3ZW15SpMuc/dr1KhWQHgoQX5kVW1Wt8zgqREAJJCQ5KuPKDA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-crop': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'
      '@jimp/plugin-scale': '>=0.3.5'

  '@jimp/plugin-crop@0.10.3':
    resolution: {integrity: sha512-nB7HgOjjl9PgdHr076xZ3Sr6qHYzeBYBs9qvs3tfEEUeYMNnvzgCCGtUl6eMakazZFCMk3mhKmcB9zQuHFOvkg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-displace@0.10.3':
    resolution: {integrity: sha512-8t3fVKCH5IVqI4lewe4lFFjpxxr69SQCz5/tlpDLQZsrNScNJivHdQ09zljTrVTCSgeCqQJIKgH2Q7Sk/pAZ0w==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-dither@0.10.3':
    resolution: {integrity: sha512-JCX/oNSnEg1kGQ8ffZ66bEgQOLCY3Rn+lrd6v1jjLy/mn9YVZTMsxLtGCXpiCDC2wG/KTmi4862ysmP9do9dAQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-fisheye@0.10.3':
    resolution: {integrity: sha512-RRZb1wqe+xdocGcFtj2xHU7sF7xmEZmIa6BmrfSchjyA2b32TGPWKnP3qyj7p6LWEsXn+19hRYbjfyzyebPElQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-flip@0.10.3':
    resolution: {integrity: sha512-0epbi8XEzp0wmSjoW9IB0iMu0yNF17aZOxLdURCN3Zr+8nWPs5VNIMqSVa1Y62GSyiMDpVpKF/ITiXre+EqrPg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-rotate': '>=0.3.5'

  '@jimp/plugin-gaussian@0.10.3':
    resolution: {integrity: sha512-25eHlFbHUDnMMGpgRBBeQ2AMI4wsqCg46sue0KklI+c2BaZ+dGXmJA5uT8RTOrt64/K9Wz5E+2n7eBnny4dfpQ==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-invert@0.10.3':
    resolution: {integrity: sha512-effYSApWY/FbtlzqsKXlTLkgloKUiHBKjkQnqh5RL4oQxh/33j6aX+HFdDyQKtsXb8CMd4xd7wyiD2YYabTa0g==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-mask@0.10.3':
    resolution: {integrity: sha512-twrg8q8TIhM9Z6Jcu9/5f+OCAPaECb0eKrrbbIajJqJ3bCUlj5zbfgIhiQIzjPJ6KjpnFPSqHQfHkU1Vvk/nVw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-normalize@0.10.3':
    resolution: {integrity: sha512-xkb5eZI/mMlbwKkDN79+1/t/+DBo8bBXZUMsT4gkFgMRKNRZ6NQPxlv1d3QpRzlocsl6UMxrHnhgnXdLAcgrXw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-print@0.10.3':
    resolution: {integrity: sha512-wjRiI6yjXsAgMe6kVjizP+RgleUCLkH256dskjoNvJzmzbEfO7xQw9g6M02VET+emnbY0CO83IkrGm2q43VRyg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'

  '@jimp/plugin-resize@0.10.3':
    resolution: {integrity: sha512-rf8YmEB1d7Sg+g4LpqF0Mp+dfXfb6JFJkwlAIWPUOR7lGsPWALavEwTW91c0etEdnp0+JB9AFpy6zqq7Lwkq6w==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/plugin-rotate@0.10.3':
    resolution: {integrity: sha512-YXLlRjm18fkW9MOHUaVAxWjvgZM851ofOipytz5FyKp4KZWDLk+dZK1JNmVmK7MyVmAzZ5jsgSLhIgj+GgN0Eg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blit': '>=0.3.5'
      '@jimp/plugin-crop': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'

  '@jimp/plugin-scale@0.10.3':
    resolution: {integrity: sha512-5DXD7x7WVcX1gUgnlFXQa8F+Q3ThRYwJm+aesgrYvDOY+xzRoRSdQvhmdd4JEEue3lyX44DvBSgCIHPtGcEPaw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'

  '@jimp/plugin-shadow@0.10.3':
    resolution: {integrity: sha512-/nkFXpt2zVcdP4ETdkAUL0fSzyrC5ZFxdcphbYBodqD7fXNqChS/Un1eD4xCXWEpW8cnG9dixZgQgStjywH0Mg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-blur': '>=0.3.5'
      '@jimp/plugin-resize': '>=0.3.5'

  '@jimp/plugin-threshold@0.10.3':
    resolution: {integrity: sha512-Dzh0Yq2wXP2SOnxcbbiyA4LJ2luwrdf1MghNIt9H+NX7B+IWw/N8qA2GuSm9n4BPGSLluuhdAWJqHcTiREriVA==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'
      '@jimp/plugin-color': '>=0.8.0'
      '@jimp/plugin-resize': '>=0.8.0'

  '@jimp/plugins@0.10.3':
    resolution: {integrity: sha512-jTT3/7hOScf0EIKiAXmxwayHhryhc1wWuIe3FrchjDjr9wgIGNN2a7XwCgPl3fML17DXK1x8EzDneCdh261bkw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/png@0.10.3':
    resolution: {integrity: sha512-YKqk/dkl+nGZxSYIDQrqhmaP8tC3IK8H7dFPnnzFVvbhDnyYunqBZZO3SaZUKTichClRw8k/CjBhbc+hifSGWg==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/tiff@0.10.3':
    resolution: {integrity: sha512-7EsJzZ5Y/EtinkBGuwX3Bi4S+zgbKouxjt9c82VJTRJOQgLWsE/RHqcyRCOQBhHAZ9QexYmDz34medfLKdoX0g==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/types@0.10.3':
    resolution: {integrity: sha512-XGmBakiHZqseSWr/puGN+CHzx0IKBSpsKlmEmsNV96HKDiP6eu8NSnwdGCEq2mmIHe0JNcg1hqg59hpwtQ7Tiw==}
    peerDependencies:
      '@jimp/custom': '>=0.3.5'

  '@jimp/utils@0.10.3':
    resolution: {integrity: sha512-VcSlQhkil4ReYmg1KkN+WqHyYfZ2XfZxDsKAHSfST1GEz/RQHxKZbX+KhFKtKflnL0F4e6DlNQj3vznMNXCR2w==}

  '@jridgewell/gen-mapping@0.3.3':
    resolution: {integrity: sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.1':
    resolution: {integrity: sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.1.2':
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.5':
    resolution: {integrity: sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.20':
    resolution: {integrity: sha512-R8LcPeWZol2zR8mmH3JeKQ6QRCFb7XgUhV9ZlGhHLGyg4wpPiPZNQOOWhFZhxKw8u//yTbNGI42Bx/3paXEQ+Q==}

  '@mapbox/geojson-rewind@0.5.2':
    resolution: {integrity: sha512-tJaT+RbYGJYStt7wI3cq4Nl4SXxG8W7JDG5DMJu97V25RnbNg3QtQtf+KD+VLjNpWKYsRvXDNmNrBgEETr1ifA==}
    hasBin: true

  '@mapbox/jsonlint-lines-primitives@2.0.2':
    resolution: {integrity: sha512-rY0o9A5ECsTQRVhv7tL/OyDpGAoUB4tTvLiW1DSzQGq4bvTPhNw1VpSNjDJc5GFZ2XuyOtSWSVN05qOtcD71qQ==}
    engines: {node: '>= 0.6'}

  '@mapbox/mapbox-gl-supported@2.0.1':
    resolution: {integrity: sha512-HP6XvfNIzfoMVfyGjBckjiAOQK9WfX0ywdLubuPMPv+Vqf5fj0uCbgBQYpiqcWZT6cbyyRnTSXDheT1ugvF6UQ==}

  '@mapbox/point-geometry@0.1.0':
    resolution: {integrity: sha512-6j56HdLTwWGO0fJPlrZtdU/B13q8Uwmo18Ck2GnGgN9PCFyKTZ3UbXeEdRFh18i9XQ92eH2VdtpJHpBD3aripQ==}

  '@mapbox/tiny-sdf@2.0.6':
    resolution: {integrity: sha512-qMqa27TLw+ZQz5Jk+RcwZGH7BQf5G/TrutJhspsca/3SHwmgKQ1iq+d3Jxz5oysPVYTGP6aXxCo5Lk9Er6YBAA==}

  '@mapbox/unitbezier@0.0.1':
    resolution: {integrity: sha512-nMkuDXFv60aBr9soUG5q+GvZYL+2KZHVvsqFCzqnkGEf46U2fvmytHaEVc1/YZbiLn8X+eR3QzX1+dwDO1lxlw==}

  '@mapbox/vector-tile@1.3.1':
    resolution: {integrity: sha512-MCEddb8u44/xfQ3oD+Srl/tNcQoqTw3goGk2oLsrFxOTc3dUp+kAnby3PvAeeBYSMSjSPD1nd1AJA6W49WnoUw==}

  '@mapbox/whoots-js@3.1.0':
    resolution: {integrity: sha512-Es6WcD0nO5l+2BOQS4uLfNPYQaNDfbot3X1XUoloz+x0mPDS3eeORZJl06HXjwBG1fOGwCRnzK88LMdxKRrd6Q==}
    engines: {node: '>=6.0.0'}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@rollup/pluginutils@4.2.1':
    resolution: {integrity: sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==}
    engines: {node: '>= 8.0.0'}

  '@sinonjs/commons@1.8.6':
    resolution: {integrity: sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==}

  '@sinonjs/fake-timers@8.1.0':
    resolution: {integrity: sha512-OAPJUAtgeINhh/TAlUID4QTs53Njm7xzddaVlEs/SXwgtiD1tW22zAB/W1wdqfrpmikgaWQ9Fw6Ws+hsiRm5Vg==}

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==}
    engines: {node: '>= 6'}

  '@turf/along@6.5.0':
    resolution: {integrity: sha512-LLyWQ0AARqJCmMcIEAXF4GEu8usmd4Kbz3qk1Oy5HoRNpZX47+i5exQtmIWKdqJ1MMhW26fCTXgpsEs5zgJ5gw==}

  '@turf/angle@6.5.0':
    resolution: {integrity: sha512-4pXMbWhFofJJAOvTMCns6N4C8CMd5Ih4O2jSAG9b3dDHakj3O4yN1+Zbm+NUei+eVEZ9gFeVp9svE3aMDenIkw==}

  '@turf/area@6.5.0':
    resolution: {integrity: sha512-xCZdiuojokLbQ+29qR6qoMD89hv+JAgWjLrwSEWL+3JV8IXKeNFl6XkEJz9HGkVpnXvQKJoRz4/liT+8ZZ5Jyg==}

  '@turf/bbox-clip@6.5.0':
    resolution: {integrity: sha512-F6PaIRF8WMp8EmgU/Ke5B1Y6/pia14UAYB5TiBC668w5rVVjy5L8rTm/m2lEkkDMHlzoP9vNY4pxpNthE7rLcQ==}

  '@turf/bbox-polygon@6.5.0':
    resolution: {integrity: sha512-+/r0NyL1lOG3zKZmmf6L8ommU07HliP4dgYToMoTxqzsWzyLjaj/OzgQ8rBmv703WJX+aS6yCmLuIhYqyufyuw==}

  '@turf/bbox@6.5.0':
    resolution: {integrity: sha512-RBbLaao5hXTYyyg577iuMtDB8ehxMlUqHEJiMs8jT1GHkFhr6sYre3lmLsPeYEi/ZKj5TP5tt7fkzNdJ4GIVyw==}

  '@turf/bearing@6.5.0':
    resolution: {integrity: sha512-dxINYhIEMzgDOztyMZc20I7ssYVNEpSv04VbMo5YPQsqa80KO3TFvbuCahMsCAW5z8Tncc8dwBlEFrmRjJG33A==}

  '@turf/bezier-spline@6.5.0':
    resolution: {integrity: sha512-vokPaurTd4PF96rRgGVm6zYYC5r1u98ZsG+wZEv9y3kJTuJRX/O3xIY2QnTGTdbVmAJN1ouOsD0RoZYaVoXORQ==}

  '@turf/boolean-clockwise@6.5.0':
    resolution: {integrity: sha512-45+C7LC5RMbRWrxh3Z0Eihsc8db1VGBO5d9BLTOAwU4jR6SgsunTfRWR16X7JUwIDYlCVEmnjcXJNi/kIU3VIw==}

  '@turf/boolean-contains@6.5.0':
    resolution: {integrity: sha512-4m8cJpbw+YQcKVGi8y0cHhBUnYT+QRfx6wzM4GI1IdtYH3p4oh/DOBJKrepQyiDzFDaNIjxuWXBh0ai1zVwOQQ==}

  '@turf/boolean-crosses@6.5.0':
    resolution: {integrity: sha512-gvshbTPhAHporTlQwBJqyfW+2yV8q/mOTxG6PzRVl6ARsqNoqYQWkd4MLug7OmAqVyBzLK3201uAeBjxbGw0Ng==}

  '@turf/boolean-disjoint@6.5.0':
    resolution: {integrity: sha512-rZ2ozlrRLIAGo2bjQ/ZUu4oZ/+ZjGvLkN5CKXSKBcu6xFO6k2bgqeM8a1836tAW+Pqp/ZFsTA5fZHsJZvP2D5g==}

  '@turf/boolean-equal@6.5.0':
    resolution: {integrity: sha512-cY0M3yoLC26mhAnjv1gyYNQjn7wxIXmL2hBmI/qs8g5uKuC2hRWi13ydufE3k4x0aNRjFGlg41fjoYLwaVF+9Q==}

  '@turf/boolean-intersects@6.5.0':
    resolution: {integrity: sha512-nIxkizjRdjKCYFQMnml6cjPsDOBCThrt+nkqtSEcxkKMhAQj5OO7o2CecioNTaX8EayqwMGVKcsz27oP4mKPTw==}

  '@turf/boolean-overlap@6.5.0':
    resolution: {integrity: sha512-8btMIdnbXVWUa1M7D4shyaSGxLRw6NjMcqKBcsTXcZdnaixl22k7ar7BvIzkaRYN3SFECk9VGXfLncNS3ckQUw==}

  '@turf/boolean-parallel@6.5.0':
    resolution: {integrity: sha512-aSHJsr1nq9e5TthZGZ9CZYeXklJyRgR5kCLm5X4urz7+MotMOp/LsGOsvKvK9NeUl9+8OUmfMn8EFTT8LkcvIQ==}

  '@turf/boolean-point-in-polygon@6.5.0':
    resolution: {integrity: sha512-DtSuVFB26SI+hj0SjrvXowGTUCHlgevPAIsukssW6BG5MlNSBQAo70wpICBNJL6RjukXg8d2eXaAWuD/CqL00A==}

  '@turf/boolean-point-on-line@6.5.0':
    resolution: {integrity: sha512-A1BbuQ0LceLHvq7F/P7w3QvfpmZqbmViIUPHdNLvZimFNLo4e6IQunmzbe+8aSStH9QRZm3VOflyvNeXvvpZEQ==}

  '@turf/boolean-within@6.5.0':
    resolution: {integrity: sha512-YQB3oU18Inx35C/LU930D36RAVe7LDXk1kWsQ8mLmuqYn9YdPsDQTMTkLJMhoQ8EbN7QTdy333xRQ4MYgToteQ==}

  '@turf/buffer@6.5.0':
    resolution: {integrity: sha512-qeX4N6+PPWbKqp1AVkBVWFerGjMYMUyencwfnkCesoznU6qvfugFHNAngNqIBVnJjZ5n8IFyOf+akcxnrt9sNg==}

  '@turf/center-mean@6.5.0':
    resolution: {integrity: sha512-AAX6f4bVn12pTVrMUiB9KrnV94BgeBKpyg3YpfnEbBpkN/znfVhL8dG8IxMAxAoSZ61Zt9WLY34HfENveuOZ7Q==}

  '@turf/center-median@6.5.0':
    resolution: {integrity: sha512-dT8Ndu5CiZkPrj15PBvslpuf01ky41DEYEPxS01LOxp5HOUHXp1oJxsPxvc+i/wK4BwccPNzU1vzJ0S4emd1KQ==}

  '@turf/center-of-mass@6.5.0':
    resolution: {integrity: sha512-EWrriU6LraOfPN7m1jZi+1NLTKNkuIsGLZc2+Y8zbGruvUW+QV7K0nhf7iZWutlxHXTBqEXHbKue/o79IumAsQ==}

  '@turf/center@6.5.0':
    resolution: {integrity: sha512-T8KtMTfSATWcAX088rEDKjyvQCBkUsLnK/Txb6/8WUXIeOZyHu42G7MkdkHRoHtwieLdduDdmPLFyTdG5/e7ZQ==}

  '@turf/centroid@6.5.0':
    resolution: {integrity: sha512-MwE1oq5E3isewPprEClbfU5pXljIK/GUOMbn22UM3IFPDJX0KeoyLNwghszkdmFp/qMGL/M13MMWvU+GNLXP/A==}

  '@turf/circle@6.5.0':
    resolution: {integrity: sha512-oU1+Kq9DgRnoSbWFHKnnUdTmtcRUMmHoV9DjTXu9vOLNV5OWtAAh1VZ+mzsioGGzoDNT/V5igbFOkMfBQc0B6A==}

  '@turf/clean-coords@6.5.0':
    resolution: {integrity: sha512-EMX7gyZz0WTH/ET7xV8MyrExywfm9qUi0/MY89yNffzGIEHuFfqwhcCqZ8O00rZIPZHUTxpmsxQSTfzJJA1CPw==}

  '@turf/clone@6.5.0':
    resolution: {integrity: sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw==}

  '@turf/clusters-dbscan@6.5.0':
    resolution: {integrity: sha512-SxZEE4kADU9DqLRiT53QZBBhu8EP9skviSyl+FGj08Y01xfICM/RR9ACUdM0aEQimhpu+ZpRVcUK+2jtiCGrYQ==}

  '@turf/clusters-kmeans@6.5.0':
    resolution: {integrity: sha512-DwacD5+YO8kwDPKaXwT9DV46tMBVNsbi1IzdajZu1JDSWoN7yc7N9Qt88oi+p30583O0UPVkAK+A10WAQv4mUw==}

  '@turf/clusters@6.5.0':
    resolution: {integrity: sha512-Y6gfnTJzQ1hdLfCsyd5zApNbfLIxYEpmDibHUqR5z03Lpe02pa78JtgrgUNt1seeO/aJ4TG1NLN8V5gOrHk04g==}

  '@turf/collect@6.5.0':
    resolution: {integrity: sha512-4dN/T6LNnRg099m97BJeOcTA5fSI8cu87Ydgfibewd2KQwBexO69AnjEFqfPX3Wj+Zvisj1uAVIZbPmSSrZkjg==}

  '@turf/combine@6.5.0':
    resolution: {integrity: sha512-Q8EIC4OtAcHiJB3C4R+FpB4LANiT90t17uOd851qkM2/o6m39bfN5Mv0PWqMZIHWrrosZqRqoY9dJnzz/rJxYQ==}

  '@turf/concave@6.5.0':
    resolution: {integrity: sha512-I/sUmUC8TC5h/E2vPwxVht+nRt+TnXIPRoztDFvS8/Y0+cBDple9inLSo9nnPXMXidrBlGXZ9vQx/BjZUJgsRQ==}

  '@turf/convex@6.5.0':
    resolution: {integrity: sha512-x7ZwC5z7PJB0SBwNh7JCeCNx7Iu+QSrH7fYgK0RhhNop13TqUlvHMirMLRgf2db1DqUetrAO2qHJeIuasquUWg==}

  '@turf/destination@6.5.0':
    resolution: {integrity: sha512-4cnWQlNC8d1tItOz9B4pmJdWpXqS0vEvv65bI/Pj/genJnsL7evI0/Xw42RvEGROS481MPiU80xzvwxEvhQiMQ==}

  '@turf/difference@6.5.0':
    resolution: {integrity: sha512-l8iR5uJqvI+5Fs6leNbhPY5t/a3vipUF/3AeVLpwPQcgmedNXyheYuy07PcMGH5Jdpi5gItOiTqwiU/bUH4b3A==}

  '@turf/dissolve@6.5.0':
    resolution: {integrity: sha512-WBVbpm9zLTp0Bl9CE35NomTaOL1c4TQCtEoO43YaAhNEWJOOIhZMFJyr8mbvYruKl817KinT3x7aYjjCMjTAsQ==}

  '@turf/distance-weight@6.5.0':
    resolution: {integrity: sha512-a8qBKkgVNvPKBfZfEJZnC3DV7dfIsC3UIdpRci/iap/wZLH41EmS90nM+BokAJflUHYy8PqE44wySGWHN1FXrQ==}

  '@turf/distance@6.5.0':
    resolution: {integrity: sha512-xzykSLfoURec5qvQJcfifw/1mJa+5UwByZZ5TZ8iaqjGYN0vomhV9aiSLeYdUGtYRESZ+DYC/OzY+4RclZYgMg==}

  '@turf/ellipse@6.5.0':
    resolution: {integrity: sha512-kuXtwFviw/JqnyJXF1mrR/cb496zDTSbGKtSiolWMNImYzGGkbsAsFTjwJYgD7+4FixHjp0uQPzo70KDf3AIBw==}

  '@turf/envelope@6.5.0':
    resolution: {integrity: sha512-9Z+FnBWvOGOU4X+fMZxYFs1HjFlkKqsddLuMknRaqcJd6t+NIv5DWvPtDL8ATD2GEExYDiFLwMdckfr1yqJgHA==}

  '@turf/explode@6.5.0':
    resolution: {integrity: sha512-6cSvMrnHm2qAsace6pw9cDmK2buAlw8+tjeJVXMfMyY+w7ZUi1rprWMsY92J7s2Dar63Bv09n56/1V7+tcj52Q==}

  '@turf/flatten@6.5.0':
    resolution: {integrity: sha512-IBZVwoNLVNT6U/bcUUllubgElzpMsNoCw8tLqBw6dfYg9ObGmpEjf9BIYLr7a2Yn5ZR4l7YIj2T7kD5uJjZADQ==}

  '@turf/flip@6.5.0':
    resolution: {integrity: sha512-oyikJFNjt2LmIXQqgOGLvt70RgE2lyzPMloYWM7OR5oIFGRiBvqVD2hA6MNw6JewIm30fWZ8DQJw1NHXJTJPbg==}

  '@turf/great-circle@6.5.0':
    resolution: {integrity: sha512-7ovyi3HaKOXdFyN7yy1yOMa8IyOvV46RC1QOQTT+RYUN8ke10eyqExwBpL9RFUPvlpoTzoYbM/+lWPogQlFncg==}

  '@turf/helpers@6.5.0':
    resolution: {integrity: sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw==}

  '@turf/hex-grid@6.5.0':
    resolution: {integrity: sha512-Ln3tc2tgZT8etDOldgc6e741Smg1CsMKAz1/Mlel+MEL5Ynv2mhx3m0q4J9IB1F3a4MNjDeVvm8drAaf9SF33g==}

  '@turf/interpolate@6.5.0':
    resolution: {integrity: sha512-LSH5fMeiGyuDZ4WrDJNgh81d2DnNDUVJtuFryJFup8PV8jbs46lQGfI3r1DJ2p1IlEJIz3pmAZYeTfMMoeeohw==}

  '@turf/intersect@6.5.0':
    resolution: {integrity: sha512-2legGJeKrfFkzntcd4GouPugoqPUjexPZnOvfez+3SfIMrHvulw8qV8u7pfVyn2Yqs53yoVCEjS5sEpvQ5YRQg==}

  '@turf/invariant@6.5.0':
    resolution: {integrity: sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg==}

  '@turf/isobands@6.5.0':
    resolution: {integrity: sha512-4h6sjBPhRwMVuFaVBv70YB7eGz+iw0bhPRnp+8JBdX1UPJSXhoi/ZF2rACemRUr0HkdVB/a1r9gC32vn5IAEkw==}

  '@turf/isolines@6.5.0':
    resolution: {integrity: sha512-6ElhiLCopxWlv4tPoxiCzASWt/jMRvmp6mRYrpzOm3EUl75OhHKa/Pu6Y9nWtCMmVC/RcWtiiweUocbPLZLm0A==}

  '@turf/kinks@6.5.0':
    resolution: {integrity: sha512-ViCngdPt1eEL7hYUHR2eHR662GvCgTc35ZJFaNR6kRtr6D8plLaDju0FILeFFWSc+o8e3fwxZEJKmFj9IzPiIQ==}

  '@turf/length@6.5.0':
    resolution: {integrity: sha512-5pL5/pnw52fck3oRsHDcSGrj9HibvtlrZ0QNy2OcW8qBFDNgZ4jtl6U7eATVoyWPKBHszW3dWETW+iLV7UARig==}

  '@turf/line-arc@6.5.0':
    resolution: {integrity: sha512-I6c+V6mIyEwbtg9P9zSFF89T7QPe1DPTG3MJJ6Cm1MrAY0MdejwQKOpsvNl8LDU2ekHOlz2kHpPVR7VJsoMllA==}

  '@turf/line-chunk@6.5.0':
    resolution: {integrity: sha512-i1FGE6YJaaYa+IJesTfyRRQZP31QouS+wh/pa6O3CC0q4T7LtHigyBSYjrbjSLfn2EVPYGlPCMFEqNWCOkC6zg==}

  '@turf/line-intersect@6.5.0':
    resolution: {integrity: sha512-CS6R1tZvVQD390G9Ea4pmpM6mJGPWoL82jD46y0q1KSor9s6HupMIo1kY4Ny+AEYQl9jd21V3Scz20eldpbTVA==}

  '@turf/line-offset@6.5.0':
    resolution: {integrity: sha512-CEXZbKgyz8r72qRvPchK0dxqsq8IQBdH275FE6o4MrBkzMcoZsfSjghtXzKaz9vvro+HfIXal0sTk2mqV1lQTw==}

  '@turf/line-overlap@6.5.0':
    resolution: {integrity: sha512-xHOaWLd0hkaC/1OLcStCpfq55lPHpPNadZySDXYiYjEz5HXr1oKmtMYpn0wGizsLwrOixRdEp+j7bL8dPt4ojQ==}

  '@turf/line-segment@6.5.0':
    resolution: {integrity: sha512-jI625Ho4jSuJESNq66Mmi290ZJ5pPZiQZruPVpmHkUw257Pew0alMmb6YrqYNnLUuiVVONxAAKXUVeeUGtycfw==}

  '@turf/line-slice-along@6.5.0':
    resolution: {integrity: sha512-KHJRU6KpHrAj+BTgTNqby6VCTnDzG6a1sJx/I3hNvqMBLvWVA2IrkR9L9DtsQsVY63IBwVdQDqiwCuZLDQh4Ng==}

  '@turf/line-slice@6.5.0':
    resolution: {integrity: sha512-vDqJxve9tBHhOaVVFXqVjF5qDzGtKWviyjbyi2QnSnxyFAmLlLnBfMX8TLQCAf2GxHibB95RO5FBE6I2KVPRuw==}

  '@turf/line-split@6.5.0':
    resolution: {integrity: sha512-/rwUMVr9OI2ccJjw7/6eTN53URtGThNSD5I0GgxyFXMtxWiloRJ9MTff8jBbtPWrRka/Sh2GkwucVRAEakx9Sw==}

  '@turf/line-to-polygon@6.5.0':
    resolution: {integrity: sha512-qYBuRCJJL8Gx27OwCD1TMijM/9XjRgXH/m/TyuND4OXedBpIWlK5VbTIO2gJ8OCfznBBddpjiObLBrkuxTpN4Q==}

  '@turf/mask@6.5.0':
    resolution: {integrity: sha512-RQha4aU8LpBrmrkH8CPaaoAfk0Egj5OuXtv6HuCQnHeGNOQt3TQVibTA3Sh4iduq4EPxnZfDjgsOeKtrCA19lg==}

  '@turf/meta@6.5.0':
    resolution: {integrity: sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA==}

  '@turf/midpoint@6.5.0':
    resolution: {integrity: sha512-MyTzV44IwmVI6ec9fB2OgZ53JGNlgOpaYl9ArKoF49rXpL84F9rNATndbe0+MQIhdkw8IlzA6xVP4lZzfMNVCw==}

  '@turf/moran-index@6.5.0':
    resolution: {integrity: sha512-ItsnhrU2XYtTtTudrM8so4afBCYWNaB0Mfy28NZwLjB5jWuAsvyV+YW+J88+neK/ougKMTawkmjQqodNJaBeLQ==}

  '@turf/nearest-point-on-line@6.5.0':
    resolution: {integrity: sha512-WthrvddddvmymnC+Vf7BrkHGbDOUu6Z3/6bFYUGv1kxw8tiZ6n83/VG6kHz4poHOfS0RaNflzXSkmCi64fLBlg==}

  '@turf/nearest-point-to-line@6.5.0':
    resolution: {integrity: sha512-PXV7cN0BVzUZdjj6oeb/ESnzXSfWmEMrsfZSDRgqyZ9ytdiIj/eRsnOXLR13LkTdXVOJYDBuf7xt1mLhM4p6+Q==}

  '@turf/nearest-point@6.5.0':
    resolution: {integrity: sha512-fguV09QxilZv/p94s8SMsXILIAMiaXI5PATq9d7YWijLxWUj6Q/r43kxyoi78Zmwwh1Zfqz9w+bCYUAxZ5+euA==}

  '@turf/planepoint@6.5.0':
    resolution: {integrity: sha512-R3AahA6DUvtFbka1kcJHqZ7DMHmPXDEQpbU5WaglNn7NaCQg9HB0XM0ZfqWcd5u92YXV+Gg8QhC8x5XojfcM4Q==}

  '@turf/point-grid@6.5.0':
    resolution: {integrity: sha512-Iq38lFokNNtQJnOj/RBKmyt6dlof0yhaHEDELaWHuECm1lIZLY3ZbVMwbs+nXkwTAHjKfS/OtMheUBkw+ee49w==}

  '@turf/point-on-feature@6.5.0':
    resolution: {integrity: sha512-bDpuIlvugJhfcF/0awAQ+QI6Om1Y1FFYE8Y/YdxGRongivix850dTeXCo0mDylFdWFPGDo7Mmh9Vo4VxNwW/TA==}

  '@turf/point-to-line-distance@6.5.0':
    resolution: {integrity: sha512-opHVQ4vjUhNBly1bob6RWy+F+hsZDH9SA0UW36pIRzfpu27qipU18xup0XXEePfY6+wvhF6yL/WgCO2IbrLqEA==}

  '@turf/points-within-polygon@6.5.0':
    resolution: {integrity: sha512-YyuheKqjliDsBDt3Ho73QVZk1VXX1+zIA2gwWvuz8bR1HXOkcuwk/1J76HuFMOQI3WK78wyAi+xbkx268PkQzQ==}

  '@turf/polygon-smooth@6.5.0':
    resolution: {integrity: sha512-LO/X/5hfh/Rk4EfkDBpLlVwt3i6IXdtQccDT9rMjXEP32tRgy0VMFmdkNaXoGlSSKf/1mGqLl4y4wHd86DqKbg==}

  '@turf/polygon-tangents@6.5.0':
    resolution: {integrity: sha512-sB4/IUqJMYRQH9jVBwqS/XDitkEfbyqRy+EH/cMRJURTg78eHunvJ708x5r6umXsbiUyQU4eqgPzEylWEQiunw==}

  '@turf/polygon-to-line@6.5.0':
    resolution: {integrity: sha512-5p4n/ij97EIttAq+ewSnKt0ruvuM+LIDzuczSzuHTpq4oS7Oq8yqg5TQ4nzMVuK41r/tALCk7nAoBuw3Su4Gcw==}

  '@turf/polygonize@6.5.0':
    resolution: {integrity: sha512-a/3GzHRaCyzg7tVYHo43QUChCspa99oK4yPqooVIwTC61npFzdrmnywMv0S+WZjHZwK37BrFJGFrZGf6ocmY5w==}

  '@turf/projection@6.5.0':
    resolution: {integrity: sha512-/Pgh9mDvQWWu8HRxqpM+tKz8OzgauV+DiOcr3FCjD6ubDnrrmMJlsf6fFJmggw93mtVPrZRL6yyi9aYCQBOIvg==}

  '@turf/random@6.5.0':
    resolution: {integrity: sha512-8Q25gQ/XbA7HJAe+eXp4UhcXM9aOOJFaxZ02+XSNwMvY8gtWSCBLVqRcW4OhqilgZ8PeuQDWgBxeo+BIqqFWFQ==}

  '@turf/rectangle-grid@6.5.0':
    resolution: {integrity: sha512-yQZ/1vbW68O2KsSB3OZYK+72aWz/Adnf7m2CMKcC+aq6TwjxZjAvlbCOsNUnMAuldRUVN1ph6RXMG4e9KEvKvg==}

  '@turf/rewind@6.5.0':
    resolution: {integrity: sha512-IoUAMcHWotBWYwSYuYypw/LlqZmO+wcBpn8ysrBNbazkFNkLf3btSDZMkKJO/bvOzl55imr/Xj4fi3DdsLsbzQ==}

  '@turf/rhumb-bearing@6.5.0':
    resolution: {integrity: sha512-jMyqiMRK4hzREjQmnLXmkJ+VTNTx1ii8vuqRwJPcTlKbNWfjDz/5JqJlb5NaFDcdMpftWovkW5GevfnuzHnOYA==}

  '@turf/rhumb-destination@6.5.0':
    resolution: {integrity: sha512-RHNP1Oy+7xTTdRrTt375jOZeHceFbjwohPHlr9Hf68VdHHPMAWgAKqiX2YgSWDcvECVmiGaBKWus1Df+N7eE4Q==}

  '@turf/rhumb-distance@6.5.0':
    resolution: {integrity: sha512-oKp8KFE8E4huC2Z1a1KNcFwjVOqa99isxNOwfo4g3SUABQ6NezjKDDrnvC4yI5YZ3/huDjULLBvhed45xdCrzg==}

  '@turf/sample@6.5.0':
    resolution: {integrity: sha512-kSdCwY7el15xQjnXYW520heKUrHwRvnzx8ka4eYxX9NFeOxaFITLW2G7UtXb6LJK8mmPXI8Aexv23F2ERqzGFg==}

  '@turf/sector@6.5.0':
    resolution: {integrity: sha512-cYUOkgCTWqa23SOJBqxoFAc/yGCUsPRdn/ovbRTn1zNTm/Spmk6hVB84LCKOgHqvSF25i0d2kWqpZDzLDdAPbw==}

  '@turf/shortest-path@6.5.0':
    resolution: {integrity: sha512-4de5+G7+P4hgSoPwn+SO9QSi9HY5NEV/xRJ+cmoFVRwv2CDsuOPDheHKeuIAhKyeKDvPvPt04XYWbac4insJMg==}

  '@turf/simplify@6.5.0':
    resolution: {integrity: sha512-USas3QqffPHUY184dwQdP8qsvcVH/PWBYdXY5am7YTBACaQOMAlf6AKJs9FT8jiO6fQpxfgxuEtwmox+pBtlOg==}

  '@turf/square-grid@6.5.0':
    resolution: {integrity: sha512-mlR0ayUdA+L4c9h7p4k3pX6gPWHNGuZkt2c5II1TJRmhLkW2557d6b/Vjfd1z9OVaajb1HinIs1FMSAPXuuUrA==}

  '@turf/square@6.5.0':
    resolution: {integrity: sha512-BM2UyWDmiuHCadVhHXKIx5CQQbNCpOxB6S/aCNOCLbhCeypKX5Q0Aosc5YcmCJgkwO5BERCC6Ee7NMbNB2vHmQ==}

  '@turf/standard-deviational-ellipse@6.5.0':
    resolution: {integrity: sha512-02CAlz8POvGPFK2BKK8uHGUk/LXb0MK459JVjKxLC2yJYieOBTqEbjP0qaWhiBhGzIxSMaqe8WxZ0KvqdnstHA==}

  '@turf/tag@6.5.0':
    resolution: {integrity: sha512-XwlBvrOV38CQsrNfrxvBaAPBQgXMljeU0DV8ExOyGM7/hvuGHJw3y8kKnQ4lmEQcmcrycjDQhP7JqoRv8vFssg==}

  '@turf/tesselate@6.5.0':
    resolution: {integrity: sha512-M1HXuyZFCfEIIKkglh/r5L9H3c5QTEsnMBoZOFQiRnGPGmJWcaBissGb7mTFX2+DKE7FNWXh4TDnZlaLABB0dQ==}

  '@turf/tin@6.5.0':
    resolution: {integrity: sha512-YLYikRzKisfwj7+F+Tmyy/LE3d2H7D4kajajIfc9mlik2+esG7IolsX/+oUz1biguDYsG0DUA8kVYXDkobukfg==}

  '@turf/transform-rotate@6.5.0':
    resolution: {integrity: sha512-A2Ip1v4246ZmpssxpcL0hhiVBEf4L8lGnSPWTgSv5bWBEoya2fa/0SnFX9xJgP40rMP+ZzRaCN37vLHbv1Guag==}

  '@turf/transform-scale@6.5.0':
    resolution: {integrity: sha512-VsATGXC9rYM8qTjbQJ/P7BswKWXHdnSJ35JlV4OsZyHBMxJQHftvmZJsFbOqVtQnIQIzf2OAly6rfzVV9QLr7g==}

  '@turf/transform-translate@6.5.0':
    resolution: {integrity: sha512-NABLw5VdtJt/9vSstChp93pc6oel4qXEos56RBMsPlYB8hzNTEKYtC146XJvyF4twJeeYS8RVe1u7KhoFwEM5w==}

  '@turf/triangle-grid@6.5.0':
    resolution: {integrity: sha512-2jToUSAS1R1htq4TyLQYPTIsoy6wg3e3BQXjm2rANzw4wPQCXGOxrur1Fy9RtzwqwljlC7DF4tg0OnWr8RjmfA==}

  '@turf/truncate@6.5.0':
    resolution: {integrity: sha512-pFxg71pLk+eJj134Z9yUoRhIi8vqnnKvCYwdT4x/DQl/19RVdq1tV3yqOT3gcTQNfniteylL5qV1uTBDV5sgrg==}

  '@turf/turf@6.5.0':
    resolution: {integrity: sha512-ipMCPnhu59bh92MNt8+pr1VZQhHVuTMHklciQURo54heoxRzt1neNYZOBR6jdL+hNsbDGAECMuIpAutX+a3Y+w==}

  '@turf/union@6.5.0':
    resolution: {integrity: sha512-igYWCwP/f0RFHIlC2c0SKDuM/ObBaqSljI3IdV/x71805QbIvY/BYGcJdyNcgEA6cylIGl/0VSlIbpJHZ9ldhw==}

  '@turf/unkink-polygon@6.5.0':
    resolution: {integrity: sha512-8QswkzC0UqKmN1DT6HpA9upfa1HdAA5n6bbuzHy8NJOX8oVizVAqfEPY0wqqTgboDjmBR4yyImsdPGUl3gZ8JQ==}

  '@turf/voronoi@6.5.0':
    resolution: {integrity: sha512-C/xUsywYX+7h1UyNqnydHXiun4UPjK88VDghtoRypR9cLlb7qozkiLRphQxxsCM0KxyxpVPHBVQXdAL3+Yurow==}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.6.7':
    resolution: {integrity: sha512-6Sfsq+EaaLrw4RmdFWE9Onp63TOUue71AWb4Gpa6JxzgTYtimbM086WnYTy2U67AofR++QKCo08ZP6pwx8YFHQ==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.20.4':
    resolution: {integrity: sha512-mSM/iKUk5fDDrEV/e83qY+Cr3I1+Q3qqTuEn++HAWYjEa1+NxZr6CNrcJGf2ZTnq4HoFGC3zaTPZTobCzCFukA==}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}

  '@types/eslint@8.44.8':
    resolution: {integrity: sha512-4K8GavROwhrYl2QXDXm0Rv9epkA8GBFu0EI+XrrnnuCl7u8CWBRusX7fXJfanhZTDWSAL24gDI/UqXyUM0Injw==}

  '@types/estree@1.0.5':
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}

  '@types/geojson@7946.0.8':
    resolution: {integrity: sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA==}

  '@types/graceful-fs@4.1.9':
    resolution: {integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/node@20.10.4':
    resolution: {integrity: sha512-D08YG6rr8X90YB56tSIuBaddy/UXAA9RKJoFvrsnogAum/0pmjkgi4+2nx96A330FmioegBWmEYQ+syqCFaveg==}

  '@types/prettier@2.7.3':
    resolution: {integrity: sha512-+68kP9yzs4LMp7VNh8gdzMSPZFL44MLGqiHWvttYJe+6qnuVr4Ek9wSBQoveqY/r+LwjCcU29kNVkidwim+kYA==}

  '@types/stack-utils@2.0.3':
    resolution: {integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==}

  '@types/yargs@16.0.9':
    resolution: {integrity: sha512-tHhzvkFXZQeTECenFoRljLBYPZJ7jAVxqqtEI0qTLOmuultnFp4I9yKE17vTuhf7BkhCu7I4XuemPgikDVuYqA==}

  '@uni-helper/uni-env@0.0.3':
    resolution: {integrity: sha512-K6MEnmN7Dg+NnEkfUUhjaKc/rPVY7tcGdsVUKOlC1/z2E6H6zjSSTdJg8z+sVJtZ03Ff1G/MHz2PYDyAS6gjQQ==}

  '@uni-helper/vite-plugin-uni-tailwind@0.13.1':
    resolution: {integrity: sha512-rwDyCF2V+l4x18T5dt85FzzwUT9vML0nKvz9jXAZlBz8M4mmLNgDxrAWwUrS/ha8JkapV/lF6o5Fa3zx3uh/ew==}
    engines: {node: '>=14.18'}
    peerDependencies:
      vite: ^2.0.0 || ^3.0.0 || ^4.0.0

  '@vitejs/plugin-legacy@4.1.1':
    resolution: {integrity: sha512-um3gbVouD2Q/g19C0qpDfHwveXDCAHzs8OC3e9g6aXpKoD1H14himgs7wkMnhAynBJy7QqUoZNAXDuqN8zLR2g==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      terser: ^5.4.0
      vite: ^4.0.0

  '@vitejs/plugin-vue-jsx@3.1.0':
    resolution: {integrity: sha512-w9M6F3LSEU5kszVb9An2/MmXNxocAnUb3WhRr8bHlimhDrXNt6n6D2nJQR3UXpGlZHh/EsgouOHCsM8V3Ln+WA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0 || ^5.0.0
      vue: ^3.0.0

  '@vitejs/plugin-vue@4.5.2':
    resolution: {integrity: sha512-UGR3DlzLi/SaVBPX0cnSyE37vqxU3O6chn8l0HJNzQzDia6/Au2A4xKv+iIJW8w2daf80G7TYHhi1pAUjdZ0bQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0 || ^5.0.0
      vue: ^3.2.25

  '@vivaxy/wxml@2.1.0':
    resolution: {integrity: sha512-xqnfAB58tl5SPyfkQjrn7D7nuvq7XV9um7QUsjCrvA4X6aZBxcau9sAM47LsQTOpJIVPzjaKIMvq3ul07yZlOA==}

  '@volar/language-core@1.11.1':
    resolution: {integrity: sha512-dOcNn3i9GgZAcJt43wuaEykSluAuOkQgzni1cuxLxTV0nJKanQztp7FxyswdRILaKH+P2XZMPRp2S4MV/pElCw==}

  '@volar/source-map@1.11.1':
    resolution: {integrity: sha512-hJnOnwZ4+WT5iupLRnuzbULZ42L7BWWPMmruzwtLhJfpDVoZLjNBxHDi2sY2bgZXCKlpU5XcsMFoYrsQmPhfZg==}

  '@volar/typescript@1.11.1':
    resolution: {integrity: sha512-iU+t2mas/4lYierSnoFOeRFQUhAEMgsFuQxoxvwn5EdQopw43j+J27a4lt9LMInx1gLJBC6qL14WYGlgymaSMQ==}

  '@vue/babel-helper-vue-transform-on@1.1.5':
    resolution: {integrity: sha512-SgUymFpMoAyWeYWLAY+MkCK3QEROsiUnfaw5zxOVD/M64KQs8D/4oK6Q5omVA2hnvEOE0SCkH2TZxs/jnnUj7w==}

  '@vue/babel-plugin-jsx@1.1.5':
    resolution: {integrity: sha512-nKs1/Bg9U1n3qSWnsHhCVQtAzI6aQXqua8j/bZrau8ywT1ilXQbK4FwEJGmU8fV7tcpuFvWmmN7TMmV1OBma1g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/compiler-core@3.2.45':
    resolution: {integrity: sha512-rcMj7H+PYe5wBV3iYeUgbCglC+pbpN8hBLTJvRiK2eKQiWqu+fG9F+8sW99JdL4LQi7Re178UOxn09puSXvn4A==}

  '@vue/compiler-core@3.2.47':
    resolution: {integrity: sha512-p4D7FDnQb7+YJmO2iPEv0SQNeNzcbHdGByJDsT4lynf63AFkOTFN07HsiRSvjGo0QrxR/o3d0hUyNCUnBU2Tig==}

  '@vue/compiler-core@3.3.11':
    resolution: {integrity: sha512-h97/TGWBilnLuRaj58sxNrsUU66fwdRKLOLQ9N/5iNDfp+DZhYH9Obhe0bXxhedl8fjAgpRANpiZfbgWyruQ0w==}

  '@vue/compiler-dom@3.2.45':
    resolution: {integrity: sha512-tyYeUEuKqqZO137WrZkpwfPCdiiIeXYCcJ8L4gWz9vqaxzIQRccTSwSWZ/Axx5YR2z+LvpUbmPNXxuBU45lyRw==}

  '@vue/compiler-dom@3.2.47':
    resolution: {integrity: sha512-dBBnEHEPoftUiS03a4ggEig74J2YBZ2UIeyfpcRM2tavgMWo4bsEfgCGsu+uJIL/vax9S+JztH8NmQerUo7shQ==}

  '@vue/compiler-dom@3.3.11':
    resolution: {integrity: sha512-zoAiUIqSKqAJ81WhfPXYmFGwDRuO+loqLxvXmfUdR5fOitPoUiIeFI9cTTyv9MU5O1+ZZglJVTusWzy+wfk5hw==}

  '@vue/compiler-sfc@3.2.45':
    resolution: {integrity: sha512-1jXDuWah1ggsnSAOGsec8cFjT/K6TMZ0sPL3o3d84Ft2AYZi2jWJgRMjw4iaK0rBfA89L5gw427H4n1RZQBu6Q==}

  '@vue/compiler-sfc@3.2.47':
    resolution: {integrity: sha512-rog05W+2IFfxjMcFw10tM9+f7i/+FFpZJJ5XHX72NP9eC2uRD+42M3pYcQqDXVYoj74kHMSEdQ/WmCjt8JFksQ==}

  '@vue/compiler-ssr@3.2.45':
    resolution: {integrity: sha512-6BRaggEGqhWht3lt24CrIbQSRD5O07MTmd+LjAn5fJj568+R9eUD2F7wMQJjX859seSlrYog7sUtrZSd7feqrQ==}

  '@vue/compiler-ssr@3.2.47':
    resolution: {integrity: sha512-wVXC+gszhulcMD8wpxMsqSOpvDZ6xKXSVWkf50Guf/S+28hTAXPDYRTbLQ3EDkOP5Xz/+SY37YiwDquKbJOgZw==}

  '@vue/devtools-api@6.5.1':
    resolution: {integrity: sha512-+KpckaAQyfbvshdDW5xQylLni1asvNSGme1JFs8I1+/H5pHEhqUKMEQD/qn3Nx5+/nycBq11qAEi8lk+LXI2dA==}

  '@vue/language-core@1.8.25':
    resolution: {integrity: sha512-NJk/5DnAZlpvXX8BdWmHI45bWGLViUaS3R/RMrmFSvFMSbJKuEODpM4kR0F0Ofv5SFzCWuNiMhxameWpVdQsnA==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/reactivity-transform@3.2.45':
    resolution: {integrity: sha512-BHVmzYAvM7vcU5WmuYqXpwaBHjsS8T63jlKGWVtHxAHIoMIlmaMyurUSEs1Zcg46M4AYT5MtB1U274/2aNzjJQ==}

  '@vue/reactivity-transform@3.2.47':
    resolution: {integrity: sha512-m8lGXw8rdnPVVIdIFhf0LeQ/ixyHkH5plYuS83yop5n7ggVJU+z5v0zecwEnX7fa7HNLBhh2qngJJkxpwEEmYA==}

  '@vue/reactivity@3.2.45':
    resolution: {integrity: sha512-PRvhCcQcyEVohW0P8iQ7HDcIOXRjZfAsOds3N99X/Dzewy8TVhTCT4uXpAHfoKjVTJRA0O0K+6QNkDIZAxNi3A==}

  '@vue/reactivity@3.3.11':
    resolution: {integrity: sha512-D5tcw091f0nuu+hXq5XANofD0OXnBmaRqMYl5B3fCR+mX+cXJIGNw/VNawBqkjLNWETrFW0i+xH9NvDbTPVh7g==}

  '@vue/runtime-core@3.2.45':
    resolution: {integrity: sha512-gzJiTA3f74cgARptqzYswmoQx0fIA+gGYBfokYVhF8YSXjWTUA2SngRzZRku2HbGbjzB6LBYSbKGIaK8IW+s0A==}

  '@vue/runtime-core@3.3.11':
    resolution: {integrity: sha512-g9ztHGwEbS5RyWaOpXuyIVFTschclnwhqEbdy5AwGhYOgc7m/q3NFwr50MirZwTTzX55JY8pSkeib9BX04NIpw==}

  '@vue/runtime-dom@3.2.45':
    resolution: {integrity: sha512-cy88YpfP5Ue2bDBbj75Cb4bIEZUMM/mAkDMfqDTpUYVgTf/kuQ2VQ8LebuZ8k6EudgH8pYhsGWHlY0lcxlvTwA==}

  '@vue/server-renderer@3.2.45':
    resolution: {integrity: sha512-ebiMq7q24WBU1D6uhPK//2OTR1iRIyxjF5iVq/1a5I1SDMDyDu4Ts6fJaMnjrvD3MqnaiFkKQj+LKAgz5WIK3g==}
    peerDependencies:
      vue: 3.2.45

  '@vue/server-renderer@3.2.47':
    resolution: {integrity: sha512-dN9gc1i8EvmP9RCzvneONXsKfBRgqFeFZLurmHOveL7oH6HiFXJw5OGu294n1nHc/HMgTy6LulU/tv5/A7f/LA==}
    peerDependencies:
      vue: 3.2.47

  '@vue/shared@3.2.45':
    resolution: {integrity: sha512-Ewzq5Yhimg7pSztDV+RH1UDKBzmtqieXQlpTVm2AwraoRL/Rks96mvd8Vgi7Lj+h+TH8dv7mXD3FRZR3TUvbSg==}

  '@vue/shared@3.2.47':
    resolution: {integrity: sha512-BHGyyGN3Q97EZx0taMQ+OLNuZcW3d37ZEVmEAyeoA9ERdGvm9Irc/0Fua8SNyOtV1w6BS4q25wbMzJujO9HIfQ==}

  '@vue/shared@3.3.11':
    resolution: {integrity: sha512-u2G8ZQ9IhMWTMXaWqZycnK4UthG1fA238CD+DP4Dm4WJi5hdUKKLg0RMRaRpDPNMdkTwIDkp7WtD0Rd9BH9fLw==}

  '@vue/tsconfig@0.1.3':
    resolution: {integrity: sha512-kQVsh8yyWPvHpb8gIc9l/HIDiiVUy1amynLNpCy8p+FoCiZXCo6fQos5/097MmnNZc9AtseDsCrfkhqCrJ8Olg==}
    peerDependencies:
      '@types/node': '*'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@webassemblyjs/ast@1.11.6':
    resolution: {integrity: sha512-IN1xI7PwOvLPgjcf180gC1bqn3q/QaOCwYUahIOhbYUu8KA/3tw2RT/T0Gidi1l7Hhj5D/INhJxiICObqpMu4Q==}

  '@webassemblyjs/floating-point-hex-parser@1.11.6':
    resolution: {integrity: sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==}

  '@webassemblyjs/helper-api-error@1.11.6':
    resolution: {integrity: sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==}

  '@webassemblyjs/helper-buffer@1.11.6':
    resolution: {integrity: sha512-z3nFzdcp1mb8nEOFFk8DrYLpHvhKC3grJD2ardfKOzmbmJvEf/tPIqCY+sNcwZIY8ZD7IkB2l7/pqhUhqm7hLA==}

  '@webassemblyjs/helper-numbers@1.11.6':
    resolution: {integrity: sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==}

  '@webassemblyjs/helper-wasm-bytecode@1.11.6':
    resolution: {integrity: sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==}

  '@webassemblyjs/helper-wasm-section@1.11.6':
    resolution: {integrity: sha512-LPpZbSOwTpEC2cgn4hTydySy1Ke+XEu+ETXuoyvuyezHO3Kjdu90KK95Sh9xTbmjrCsUwvWwCOQQNta37VrS9g==}

  '@webassemblyjs/ieee754@1.11.6':
    resolution: {integrity: sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==}

  '@webassemblyjs/leb128@1.11.6':
    resolution: {integrity: sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==}

  '@webassemblyjs/utf8@1.11.6':
    resolution: {integrity: sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==}

  '@webassemblyjs/wasm-edit@1.11.6':
    resolution: {integrity: sha512-Ybn2I6fnfIGuCR+Faaz7YcvtBKxvoLV3Lebn1tM4o/IAJzmi9AWYIPWpyBfU8cC+JxAO57bk4+zdsTjJR+VTOw==}

  '@webassemblyjs/wasm-gen@1.11.6':
    resolution: {integrity: sha512-3XOqkZP/y6B4F0PBAXvI1/bky7GryoogUtfwExeP/v7Nzwo1QLcq5oQmpKlftZLbT+ERUOAZVQjuNVak6UXjPA==}

  '@webassemblyjs/wasm-opt@1.11.6':
    resolution: {integrity: sha512-cOrKuLRE7PCe6AsOVl7WasYf3wbSo4CeOk6PkrjS7g57MFfVUF9u6ysQBBODX0LdgSvQqRiGz3CXvIDKcPNy4g==}

  '@webassemblyjs/wasm-parser@1.11.6':
    resolution: {integrity: sha512-6ZwPeGzMJM3Dqp3hCsLgESxBGtT/OeCvCZ4TA1JUPYgmhAx38tTPR9JaKy0S5H3evQpO/h2uWs2j6Yc/fjkpTQ==}

  '@webassemblyjs/wast-printer@1.11.6':
    resolution: {integrity: sha512-JM7AhRcE+yW2GWYaKeHL5vt4xqee5N2WcezptmgyhNS+ScggqcT1OtXykhAb13Sn5Yas0j2uv9tHgrjwvzAP4A==}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  abab@2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}
    deprecated: Use your platform's native atob() and btoa() methods instead

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  acorn-globals@6.0.0:
    resolution: {integrity: sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg==}

  acorn-import-assertions@1.9.0:
    resolution: {integrity: sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==}
    deprecated: package has been renamed to acorn-import-attributes
    peerDependencies:
      acorn: ^8

  acorn-walk@7.2.0:
    resolution: {integrity: sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==}
    engines: {node: '>=0.4.0'}

  acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.11.2:
    resolution: {integrity: sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  address@1.2.2:
    resolution: {integrity: sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==}
    engines: {node: '>= 10.0.0'}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  ajv-keywords@3.5.2:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  any-base@1.1.0:
    resolution: {integrity: sha512-uMgjozySS8adZZYePpaWs8cxB9/kdzmpX6SgJZ+wbz1K5eYk5QMYDVJaZKhxyIHUdnnJkfR7SVgStgH7LkGUyg==}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  autoprefixer@10.4.16:
    resolution: {integrity: sha512-7vd3UC6xKp0HLfua5IjZlcXvGAGy7cBAXTg2lyQ/8WpNhd6SiZ8Be+xm3FyBSYJx5GKcpRCzBh7RH4/0dnY+uQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  axios@1.6.2:
    resolution: {integrity: sha512-7i24Ri4pmDRfJTR7LDBhsOTtcm+9kjX5WiY1X3wIisx6G9So3pfMkEiU7emUBe46oceVImccTEM3k6C5dbVW8A==}

  babel-jest@27.5.1:
    resolution: {integrity: sha512-cdQ5dXjGRd0IBRATiQ4mZGlGlRE8kJpjPOixdNRdT+m3UcNqmYWN6rK6nvtXYfY3D76cb8s/O1Ss8ea24PIwcg==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}

  babel-plugin-jest-hoist@27.5.1:
    resolution: {integrity: sha512-50wCwD5EMNW4aRpOwtqzyZHIewTYNxLA4nhB+09d8BIssfNfzBRhkBIHiaPv1Si226TQSvp8gxAJm2iY2qs2hQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  babel-plugin-polyfill-corejs2@0.4.6:
    resolution: {integrity: sha512-jhHiWVZIlnPbEUKSSNb9YoWcQGdlTLq7z1GHL4AjFxaoOUMuuEVJ+Y4pAaQUGOGk93YsVCKPbqbfw3m0SM6H8Q==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.8.6:
    resolution: {integrity: sha512-leDIc4l4tUgU7str5BWLS2h8q2N4Nf6lGZP6UrNDxdtfF2g69eJ5L0H7S8A5Ln/arfFAfHor5InAdZuIOwZdgQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.5.3:
    resolution: {integrity: sha512-8sHeDOmXC8csczMrYEOf0UTNa4yE2SxV5JGeT/LP1n0OYVDUUFPxG9vdk2AlDlIit4t+Kf0xCtpgXPBwnn/9pw==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-preset-current-node-syntax@1.0.1:
    resolution: {integrity: sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-jest@27.5.1:
    resolution: {integrity: sha512-Nptf2FzlPCWYuJg41HBqXVT8ym6bXOevuCTbhxlUpjwtysGaIWFvDEjp4y+G7fl13FgOdjs7P/DmErqH7da0Ag==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  base64url@3.0.1:
    resolution: {integrity: sha512-ir1UPr3dkwexU7FdV8qBBbNDRUhMmIekYMFZfi+C/sLNnRESKPl23nB9b2pltqfOQNnGzsDdId90AEtG5tCx4A==}
    engines: {node: '>=6.0.0'}

  big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}

  binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}

  bmp-js@0.1.0:
    resolution: {integrity: sha512-vHdS19CnY3hwiNdkaqk93DvjVLfbEcI8mys4UjuWrlX1haDmroo8o4xCzh4wD6DGV6HxRCyauwhHRqMTfERtjw==}

  body-parser@1.20.1:
    resolution: {integrity: sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}

  browser-process-hrtime@1.0.0:
    resolution: {integrity: sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==}

  browserslist@4.22.2:
    resolution: {integrity: sha512-0UgcrvQmBDvZHFGdYUehrCNIazki7/lUP3kkoi/r3YB2amZbFM9J43ZRkJTXBUZK4gmx56+Sqk9+Vs9mwZx9+A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bser@2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}

  buffer-equal@0.0.1:
    resolution: {integrity: sha512-RgSV6InVQ9ODPdLWJ5UAqBqJBOg370Nz6ZQtRzpt6nUjc8v0St97uJ4PYC6NztqIScrAXafKM3mZPMygSe1ggA==}
    engines: {node: '>=0.4.0'}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  cac@6.7.9:
    resolution: {integrity: sha512-XN5qEpfNQCJ8jRaZgitSkkukjMRCGio+X3Ks5KUbGGlPbV+pSem1l9VuzooCBXOiMFshUZgyYqg6rgN8rjkb/w==}
    engines: {node: '>=8'}

  call-bind@1.0.5:
    resolution: {integrity: sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ==}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  caniuse-lite@1.0.30001568:
    resolution: {integrity: sha512-vSUkH84HontZJ88MiNrOau1EBrCqEQYgkC5gIySiDlpsm8sGVrhU7Kx4V6h0tnqaHzIHZv08HlJIwPbL4XL9+A==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  char-regex@1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==}
    engines: {node: '>=10'}

  charenc@0.0.2:
    resolution: {integrity: sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==}

  chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}

  chrome-trace-event@1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==}
    engines: {node: '>=6.0'}

  ci-info@3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==}
    engines: {node: '>=8'}

  cjs-module-lexer@1.2.3:
    resolution: {integrity: sha512-0TNiGstbQmCFwt4akjjBg5pLRTSyj/PkWQ1ZoO2zntmg9yLqSRxwEa4iCfQLGjqhiqBfOJa7W/E8wfGrTDmlZQ==}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  collect-v8-coverage@1.0.2:
    resolution: {integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  compare-versions@3.6.0:
    resolution: {integrity: sha512-W6Af2Iw1z4CB7q4uU4hv646dW9GQuBM+YpC0UvUCWSD8w90SJjp+ujJuXaEMtAXBtSqGfMPuFOVn4/+FlaqfBA==}

  computeds@0.0.1:
    resolution: {integrity: sha512-7CEBgcMjVmitjYo5q8JTJVra6X5mQ20uTThdK+0kR7UEaDrAWEQcRiBtWJzga4eRpP6afNwwLsX2SET2JhVB1Q==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  concaveman@1.2.1:
    resolution: {integrity: sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  core-js-compat@3.34.0:
    resolution: {integrity: sha512-4ZIyeNbW/Cn1wkMMDy+mvrRUxrwFNjKwbhCfQpDd+eLgYipDqp8oGFGtLmhh18EDPKA0g3VUBYOxQGGwvWLVpA==}

  core-js@3.34.0:
    resolution: {integrity: sha512-aDdvlDder8QmY91H88GzNi9EtQi2TjvQhpCX6B1v/dAZHU1AuLgHvRh54RiOerpEhEW46Tkf+vgAViB/CWC0ag==}

  cross-env@7.0.3:
    resolution: {integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  crypt@0.0.2:
    resolution: {integrity: sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==}

  css-blank-pseudo@6.0.0:
    resolution: {integrity: sha512-VbfLlOWO7sBHBTn6pwDQzc07Z0SDydgDBfNfCE0nvrehdBNv9RKsuupIRa/qal0+fBZhAALyQDPMKz5lnvcchw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  css-font-size-keywords@1.0.0:
    resolution: {integrity: sha512-Q+svMDbMlelgCfH/RVDKtTDaf5021O486ZThQPIpahnIjUkMUslC+WuOQSWTgGSrNCH08Y7tYNEmmy0hkfMI8Q==}

  css-font-stretch-keywords@1.0.1:
    resolution: {integrity: sha512-KmugPO2BNqoyp9zmBIUGwt58UQSfyk1X5DbOlkb2pckDXFSAfjsD5wenb88fNrD6fvS+vu90a/tsPpb9vb0SLg==}

  css-font-style-keywords@1.0.1:
    resolution: {integrity: sha512-0Fn0aTpcDktnR1RzaBYorIxQily85M2KXRpzmxQPgh8pxUN9Fcn00I8u9I3grNr1QXVgCl9T5Imx0ZwKU973Vg==}

  css-font-weight-keywords@1.0.0:
    resolution: {integrity: sha512-5So8/NH+oDD+EzsnF4iaG4ZFHQ3vaViePkL1ZbZ5iC/KrsCY+WHq/lvOgrtmuOQ9pBBZ1ADGpaf+A4lj1Z9eYA==}

  css-has-pseudo@6.0.0:
    resolution: {integrity: sha512-X+r+JBuoO37FBOWVNhVJhxtSBUFHgHbrcc0CjFT28JEdOw1qaDwABv/uunyodUuSy2hMPe9j/HjssxSlvUmKjg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  css-list-helpers@2.0.0:
    resolution: {integrity: sha512-9Bj8tZ0jWbAM3u/U6m/boAzAwLPwtjzFvwivr2piSvyVa3K3rChJzQy4RIHkNkKiZCHrEMWDJWtTR8UyVhdDnQ==}

  css-prefers-color-scheme@9.0.0:
    resolution: {integrity: sha512-03QGAk/FXIRseDdLb7XAiu6gidQ0Nd8945xuM7VFVPpc6goJsG9uIO8xQjTxwbPdPIIV4o4AJoOJyt8gwDl67g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  css-system-font-keywords@1.0.0:
    resolution: {integrity: sha512-1umTtVd/fXS25ftfjB71eASCrYhilmEsvDEI6wG/QplnmlfmVM5HkZ/ZX46DT5K3eblFPgLUHt5BRCb0YXkSFA==}

  csscolorparser@1.0.3:
    resolution: {integrity: sha512-umPSgYwZkdFoUrH5hIq5kf0wPSXiro51nPw0j2K/c83KflkPSTBGMz6NJvMB+07VlL0y7VPo6QJcDjcgKTTm3w==}

  cssdb@7.9.0:
    resolution: {integrity: sha512-WPMT9seTQq6fPAa1yN4zjgZZeoTriSN2LqW9C+otjar12DQIWA4LuSfFrvFJiKp4oD0xIk1vumDLw8K9ur4NBw==}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  cssom@0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}

  cssom@0.4.4:
    resolution: {integrity: sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==}

  cssstyle@2.3.0:
    resolution: {integrity: sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==}
    engines: {node: '>=8'}

  csstype@2.6.21:
    resolution: {integrity: sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==}

  d3-array@1.2.4:
    resolution: {integrity: sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==}

  d3-geo@1.7.1:
    resolution: {integrity: sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==}

  d3-voronoi@1.1.2:
    resolution: {integrity: sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw==}

  data-urls@2.0.0:
    resolution: {integrity: sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ==}
    engines: {node: '>=10'}

  dayjs@1.11.10:
    resolution: {integrity: sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==}

  de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.4.3:
    resolution: {integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==}

  decode-uri-component@0.4.1:
    resolution: {integrity: sha512-+8VxcR21HhTy8nOt6jf20w0c9CADrw1O8d+VZ/YzzCt4bJ3uBjw+D1q2osAB8RnpwwaeYBxy0HyKQxD5JBMuuQ==}
    engines: {node: '>=14.16'}

  dedent@0.7.0:
    resolution: {integrity: sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA==}

  deep-equal@1.1.2:
    resolution: {integrity: sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==}
    engines: {node: '>= 0.4'}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  default-gateway@6.0.3:
    resolution: {integrity: sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==}
    engines: {node: '>= 10'}

  define-data-property@1.1.1:
    resolution: {integrity: sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  density-clustering@1.3.0:
    resolution: {integrity: sha512-icpmBubVTwLnsaor9qH/4tG5+7+f61VcqMN3V3pm9sxxSCt2Jcs0zWOgwZW9ARJYaKD3FumIgHiMOcIMRRAzFQ==}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-newline@3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==}
    engines: {node: '>=8'}

  didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  diff-sequences@27.5.1:
    resolution: {integrity: sha512-k1gCAXAsNgLwEL+Y8Wvl+M6oEFj5bgazfZULpS5CneoPPXRaCCW7dm+q21Ky2VEE5X+VeRDBVg1Pcvvsr4TtNQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  dom-walk@0.1.2:
    resolution: {integrity: sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==}

  domexception@2.0.1:
    resolution: {integrity: sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg==}
    engines: {node: '>=8'}
    deprecated: Use your platform's native DOMException instead

  earcut@2.2.4:
    resolution: {integrity: sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==}

  echarts@5.4.3:
    resolution: {integrity: sha512-mYKxLxhzy6zyTi/FaEbJMOZU1ULGEQHaeIeuMR5L+JnJTpz+YR03mnnpBhbR4+UYJAgiXgpyTVLffPAjOTLkZA==}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  electron-to-chromium@1.4.609:
    resolution: {integrity: sha512-ihiCP7PJmjoGNuLpl7TjNA8pCQWu09vGyjlPYw1Rqww4gvNuCcmvl+44G+2QyJ6S2K4o+wbTS++Xz0YN8Q9ERw==}

  emittery@0.8.1:
    resolution: {integrity: sha512-uDfvUjVrfGJJhymx/kz6prltenw1u7WrCg1oa94zYY8xxVpLLUu045LAT0dhDZdXG58/EpPL/5kA180fQ/qudg==}
    engines: {node: '>=10'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  enhanced-resolve@5.15.0:
    resolution: {integrity: sha512-LXYT42KJ7lpIKECr2mAXIaMldcNCh/7E0KBKOu4KSfkHmP+mZmSs+8V5gBAqisWBy0OO4W5Oyys0GO1Y8KtdKg==}
    engines: {node: '>=10.13.0'}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  es-module-lexer@1.4.1:
    resolution: {integrity: sha512-cXLGjP0c4T3flZJKQSuziYoq7MlT+rnvfZjfp7h+I7K9BNX54kP9nyWvdbwjQ4u1iWbOL4u96fgeZLToQlZC7w==}

  esbuild@0.16.17:
    resolution: {integrity: sha512-G8LEkV0XzDMNwXKgM0Jwu3nY3lSTwSGY6XbxM9cr9+s0T/qSV1q1JVPBGzm3dcjhCic9+emZDmMffkwgPeOeLg==}
    engines: {node: '>=12'}
    hasBin: true

  esbuild@0.17.19:
    resolution: {integrity: sha512-XQ0jAPFkK/u3LcVRcvVHQcTIqD6E2H1fvZMA5dQPSOWb3suUbWbfbRf94pjc0bNzRYLfIrDRQXr7X+LHIm5oHw==}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}

  escodegen@2.1.0:
    resolution: {integrity: sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==}
    engines: {node: '>=6.0'}
    hasBin: true

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  exif-parser@0.1.12:
    resolution: {integrity: sha512-c2bQfLNbMzLPmzQuOr8fy0csy84WmwnER81W88DzTp9CYNPJ6yzOj2EZAh9pywYpqHnshVLHQJ8WzldAyfY+Iw==}

  exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}

  expect@27.5.1:
    resolution: {integrity: sha512-E1q5hSUG2AmYQwQJ041nvgpkODHQvB+RKlB4IYdru6uJsyFTRyZAP463M+1lINorwbqAmUggi6+WwkD8lCS/Dw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  express@4.18.2:
    resolution: {integrity: sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==}
    engines: {node: '>= 0.10.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}

  fb-watchman@2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}

  file-type@9.0.0:
    resolution: {integrity: sha512-Qe/5NJrgIOlwijpq3B7BEpzPFcgzggOTagZmkXQY4LA6bsXKTUstK7Wp12lEJ/mLKTpvIZxmIuRcLYWT6ov9lw==}
    engines: {node: '>=6'}

  fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}

  filter-obj@5.1.0:
    resolution: {integrity: sha512-qWeTREPoT7I0bifpPUXtxkZJ1XJzxWtfoWWkdVGqa+eCr3SHW/Ocp89o8vLvbUuQnadybJpjOKu4V+RwO6sGng==}
    engines: {node: '>=14.16'}

  finalhandler@1.2.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==}
    engines: {node: '>= 0.8'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  follow-redirects@1.15.3:
    resolution: {integrity: sha512-1VzOtuEM8pC9SFU1E+8KfTjZyMztRsgEfwQl44z8A25uy13jSzTj6dyK2Df52iV0vgHCfBwLhDWevLn95w5v6Q==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@3.0.1:
    resolution: {integrity: sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==}
    engines: {node: '>= 6'}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gcoord@1.0.5:
    resolution: {integrity: sha512-IJjyWd7Q57K36SzoEAZ5LEnhFVXVR3996WJp/3NlleXVBzb/GcORfNhCw8JuZPvSGloHgG0yhPFK4Aqf0t/ohg==}
    engines: {node: '>=16.11.0'}

  generic-names@4.0.0:
    resolution: {integrity: sha512-ySFolZQfw9FoDb3ed9d80Cm9f0+r7qj+HJkWjeD9RBfpxEVTlVhol+gvaQB/78WbwYfbnNh8nWHHBSlg072y6A==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  geojson-equality@0.1.6:
    resolution: {integrity: sha512-TqG8YbqizP3EfwP5Uw4aLu6pKkg6JQK9uq/XZ1lXQntvTHD1BBKJWhNpJ2M0ax6TuWMP3oyx6Oq7FCIfznrgpQ==}

  geojson-rbush@3.2.0:
    resolution: {integrity: sha512-oVltQTXolxvsz1sZnutlSuLDEcQAKYC/uXt9zDzJJ6bu0W+baTI8LZBaTup5afzibEH4N3jlq2p+a152wlBJ7w==}

  geojson-vt@3.2.1:
    resolution: {integrity: sha512-EvGQQi/zPrDA6zr6BnJD/YhwAkBP8nnJ9emh3EnHQKVMfg/MRVtPbMYdgVy/IaEmn4UfagD2a6fafPDL5hbtwg==}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.2.2:
    resolution: {integrity: sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA==}

  get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  gl-matrix@3.4.3:
    resolution: {integrity: sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  glob@7.1.6:
    resolution: {integrity: sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==}

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  global@4.4.0:
    resolution: {integrity: sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  grid-index@1.1.0:
    resolution: {integrity: sha512-HZRwumpOGUrHyxO5bqKZL0B0GlUpwtCAzZ42sgxUPniu33R1LSFH5yrIcBCHjkctCAh3mtWKcKd9J4vDDdeVHA==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.1:
    resolution: {integrity: sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg==}

  has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}

  hash-sum@2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==}

  hasown@2.0.0:
    resolution: {integrity: sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  html-encoding-sniffer@2.0.1:
    resolution: {integrity: sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ==}
    engines: {node: '>=10'}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  html-tags@3.3.1:
    resolution: {integrity: sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==}
    engines: {node: '>=8'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  icss-replace-symbols@1.1.0:
    resolution: {integrity: sha512-chIaY3Vh2mh2Q3RGXttaDIzeiPvaVXJ+C4DAh/w3c37SKZ/U6PGMmuicR2EQQp9bKG8zLMCl7I+PtIoOOPp8Gg==}

  icss-utils@5.1.0:
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  immutable@4.3.4:
    resolution: {integrity: sha512-fsXeu4J4i6WNWSikpI88v/PcVflZz+6kMhUfIwc5SY+poQRPnaf5V7qds6SUyUN3cVxEzuCab7QIoLOQ+DQ1wA==}

  import-local@3.1.0:
    resolution: {integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  invert-kv@3.0.1:
    resolution: {integrity: sha512-CYdFeFexxhv/Bcny+Q0BfOV+ltRlJcd4BBZBYFX/O0u4npJrgZtIcjokegtiSMAvlMTJ+Koq0GBCc//3bueQxw==}
    engines: {node: '>=8'}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}

  is-core-module@2.13.1:
    resolution: {integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==}

  is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-function@1.0.2:
    resolution: {integrity: sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==}

  is-generator-fn@2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==}

  is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}

  istanbul-reports@3.1.6:
    resolution: {integrity: sha512-TLgnMkKg3iTDsQ9PbPTdpfAK2DzjF9mqUG7RMgcQl8oFjad8ob4laGxv5XV5U9MAfx8D6tSJiUyuAwzLicaxlg==}
    engines: {node: '>=8'}

  jest-changed-files@27.5.1:
    resolution: {integrity: sha512-buBLMiByfWGCoMsLLzGUUSpAmIAGnbR2KJoMN10ziLhOLvP4e0SlypHnAel8iqQXTrcbmfEY9sSqae5sgUsTvw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-circus@27.5.1:
    resolution: {integrity: sha512-D95R7x5UtlMA5iBYsOHFFbMD/GVA4R/Kdq15f7xYWUfWHBto9NYRsOvnSauTgdF+ogCpJ4tyKOXhUifxS65gdw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-cli@27.5.1:
    resolution: {integrity: sha512-Hc6HOOwYq4/74/c62dEE3r5elx8wjYqxY0r0G/nFrLDPMFRu6RA/u8qINOIkvhxG7mMQ5EJsOGfRpI8L6eFUVw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@27.5.1:
    resolution: {integrity: sha512-5sAsjm6tGdsVbW9ahcChPAFCk4IlkQUknH5AvKjuLTSlcO/wCZKyFdn7Rg0EkC+OGgWODEy2hDpWB1PgzH0JNA==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    peerDependencies:
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      ts-node:
        optional: true

  jest-diff@27.5.1:
    resolution: {integrity: sha512-m0NvkX55LDt9T4mctTEgnZk3fmEg3NRYutvMPWM/0iPnkFj2wIeF45O1718cMSOFO1vINkqmxqD8vE37uTEbqw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-docblock@27.5.1:
    resolution: {integrity: sha512-rl7hlABeTsRYxKiUfpHrQrG4e2obOiTQWfMEH3PxPjOtdsfLQO4ReWSZaQ7DETm4xu07rl4q/h4zcKXyU0/OzQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-each@27.5.1:
    resolution: {integrity: sha512-1Ff6p+FbhT/bXQnEouYy00bkNSY7OUpfIcmdl8vZ31A1UUaurOLPA8a8BbJOF2RDUElwJhmeaV7LnagI+5UwNQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-environment-jsdom@27.5.1:
    resolution: {integrity: sha512-TFBvkTC1Hnnnrka/fUb56atfDtJ9VMZ94JkjTbggl1PEpwrYtUBKMezB3inLmWqQsXYLcMwNoDQwoBTAvFfsfw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-environment-node@27.5.1:
    resolution: {integrity: sha512-Jt4ZUnxdOsTGwSRAfKEnE6BcwsSPNOijjwifq5sDFSA2kesnXTvNqKHYgM0hDq3549Uf/KzdXNYn4wMZJPlFLw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-get-type@27.5.1:
    resolution: {integrity: sha512-2KY95ksYSaK7DMBWQn6dQz3kqAf3BB64y2udeG+hv4KfSOb9qwcYQstTJc1KCbsix+wLZWZYN8t7nwX3GOBLRw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-haste-map@27.5.1:
    resolution: {integrity: sha512-7GgkZ4Fw4NFbMSDSpZwXeBiIbx+t/46nJ2QitkOjvwPYyZmqttu2TDSimMHP1EkPOi4xUZAN1doE5Vd25H4Jng==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-jasmine2@27.5.1:
    resolution: {integrity: sha512-jtq7VVyG8SqAorDpApwiJJImd0V2wv1xzdheGHRGyuT7gZm6gG47QEskOlzsN1PG/6WNaCo5pmwMHDf3AkG2pQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-leak-detector@27.5.1:
    resolution: {integrity: sha512-POXfWAMvfU6WMUXftV4HolnJfnPOGEu10fscNCA76KBpRRhcMN2c8d3iT2pxQS3HLbA+5X4sOUPzYO2NUyIlHQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-matcher-utils@27.5.1:
    resolution: {integrity: sha512-z2uTx/T6LBaCoNWNFWwChLBKYxTMcGBRjAt+2SbP929/Fflb9aa5LGma654Rz8z9HLxsrUaYzxE9T/EFIL/PAw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-message-util@27.5.1:
    resolution: {integrity: sha512-rMyFe1+jnyAAf+NHwTclDz0eAaLkVDdKVHHBFWsBWHnnh5YeJMNWWsv7AbFYXfK3oTqvL7VTWkhNLu1jX24D+g==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-mock@27.5.1:
    resolution: {integrity: sha512-K4jKbY1d4ENhbrG2zuPWaQBvDly+iZ2yAW+T1fATN78hc0sInwn7wZB8XtlNnvHug5RMwV897Xm4LqmPM4e2Og==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-pnp-resolver@1.2.3:
    resolution: {integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@27.5.1:
    resolution: {integrity: sha512-4bfKq2zie+x16okqDXjXn9ql2B0dScQu+vcwe4TvFVhkVyuWLqpZrZtXxLLWoXYgn0E87I6r6GRYHF7wFZBUvg==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-resolve-dependencies@27.5.1:
    resolution: {integrity: sha512-QQOOdY4PE39iawDn5rzbIePNigfe5B9Z91GDD1ae/xNDlu9kaat8QQ5EKnNmVWPV54hUdxCVwwj6YMgR2O7IOg==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-resolve@27.5.1:
    resolution: {integrity: sha512-FFDy8/9E6CV83IMbDpcjOhumAQPDyETnU2KZ1O98DwTnz8AOBsW/Xv3GySr1mOZdItLR+zDZ7I/UdTFbgSOVCw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-runner@27.5.1:
    resolution: {integrity: sha512-g4NPsM4mFCOwFKXO4p/H/kWGdJp9V8kURY2lX8Me2drgXqG7rrZAx5kv+5H7wtt/cdFIjhqYx1HrlqWHaOvDaQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-runtime@27.5.1:
    resolution: {integrity: sha512-o7gxw3Gf+H2IGt8fv0RiyE1+r83FJBRruoA+FXrlHw6xEyBsU8ugA6IPfTdVyA0w8HClpbK+DGJxH59UrNMx8A==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-serializer@27.5.1:
    resolution: {integrity: sha512-jZCyo6iIxO1aqUxpuBlwTDMkzOAJS4a3eYz3YzgxxVQFwLeSA7Jfq5cbqCY+JLvTDrWirgusI/0KwxKMgrdf7w==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-snapshot@27.5.1:
    resolution: {integrity: sha512-yYykXI5a0I31xX67mgeLw1DZ0bJB+gpq5IpSuCAoyDi0+BhgU/RIrL+RTzDmkNTchvDFWKP8lp+w/42Z3us5sA==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-util@27.5.1:
    resolution: {integrity: sha512-Kv2o/8jNvX1MQ0KGtw480E/w4fBCDOnH6+6DmeKi6LZUIlKA5kwY0YNdlzaWTiVgxqAqik11QyxDOKk543aKXw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-validate@27.5.1:
    resolution: {integrity: sha512-thkNli0LYTmOI1tDB3FI1S1RTp/Bqyd9pTarJwL87OIBFuqEb5Apv5EaApEudYg4g86e3CT6kM0RowkhtEnCBQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-watcher@27.5.1:
    resolution: {integrity: sha512-z676SuD6Z8o8qbmEGhoEUFOM1+jfEiL3DXHK/xgEiG2EyNYfFG60jluWcupY6dATjfEsKQuibReS1djInQnoVw==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}

  jest@27.0.4:
    resolution: {integrity: sha512-Px1iKFooXgGSkk1H8dJxxBIrM3tsc5SIuI4kfKYK2J+4rvCvPGr/cXktxh0e9zIPQ5g09kOMNfHQEmusBUf/ZA==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jimp@0.10.3:
    resolution: {integrity: sha512-meVWmDMtyUG5uYjFkmzu0zBgnCvvxwWNi27c4cg55vWNVC9ES4Lcwb+ogx+uBBQE3Q+dLKjXaLl0JVW+nUNwbQ==}

  jiti@1.21.0:
    resolution: {integrity: sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==}
    hasBin: true

  jpeg-js@0.3.7:
    resolution: {integrity: sha512-9IXdWudL61npZjvLuVe/ktHiA41iE8qFyLB+4VDTblEsWBzeg8WQTlktdUK4CdncUqtUgUg0bbOmTE2bKBKaBQ==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  jsdom@16.7.0:
    resolution: {integrity: sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw==}
    engines: {node: '>=10'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-parser@3.2.0:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  kdbush@4.0.2:
    resolution: {integrity: sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA==}

  kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}

  klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}

  lcid@3.1.1:
    resolution: {integrity: sha512-M6T051+5QCGLBQb8id3hdvIW8+zeFV2FyBGFS9IEK5H9Wt4MueD4bW1eWikpHgZp+5xR3l5c8pZUkQsIA0BFZg==}
    engines: {node: '>=8'}

  leven@3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}

  licia@1.39.1:
    resolution: {integrity: sha512-JB/+RU6dDfine8cgOMTodtFImjwAws3YxqkM4PM9KRfBpxlE6miq61Ul3bysn07Pt/a0GmcQgd0Ruz41VkdgGA==}

  lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  lilconfig@3.0.0:
    resolution: {integrity: sha512-K2U4W2Ff5ibV7j7ydLr+zLAkIg5JJ4lPn1Ltsdt+Tz/IjQ8buJ55pZAxoP34lqIiwtF9iAvtLv3JGv7CAyAg+g==}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  load-bmfont@1.4.1:
    resolution: {integrity: sha512-8UyQoYmdRDy81Brz6aLAUhfZLwr5zV0L3taTQ4hju7m6biuwiWiJXjPhBJxbUQJA8PrkvJ/7Enqmwk2sM14soA==}

  loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}

  loader-utils@3.2.1:
    resolution: {integrity: sha512-ZvFw1KWS3GVyYBYb7qkmRM/WwL2TQQBxgCK62rlvm4WpVQ23Nb4tYjApUlfjrEGvOs7KHEsmyUn75OHZrJMWPw==}
    engines: {node: '>= 12.13.0'}

  localstorage-polyfill@1.0.1:
    resolution: {integrity: sha512-m4iHVZxFH5734oQcPKU08025gIz2+4bjWR9lulP8ZYxEJR0BpA0w32oJmkzh8y3UI9ci7xCBehQDc3oA1X+VHw==}
    engines: {node: '>=6'}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  magic-string@0.25.9:
    resolution: {integrity: sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==}

  magic-string@0.30.5:
    resolution: {integrity: sha512-7xlpfBaQaP/T6Vh8MO/EqXSW5En6INHEvEXQiuff7Gku0PWjU3uf6w/j9o7O+SpB5fOAkrI5HeoNgwjEO0pFsA==}
    engines: {node: '>=12'}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  makeerror@1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==}

  mapbox-gl@2.15.0:
    resolution: {integrity: sha512-fjv+aYrd5TIHiL7wRa+W7KjtUqKWziJMZUkK5hm8TvJ3OLeNPx4NmW/DgfYhd/jHej8wWL+QJBDbdMMAKvNC0A==}

  md5@2.3.0:
    resolution: {integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  merge-descriptors@1.0.1:
    resolution: {integrity: sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  merge@2.1.1:
    resolution: {integrity: sha512-jz+Cfrg9GWOZbQAnDQ4hlVnQky+341Yk5ru8bZSe6sIDTCIg8n9i/u7hSQGSVOF3C7lH6mGtqjkiT9G4wFLL0w==}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mime@3.0.0:
    resolution: {integrity: sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  min-document@2.19.0:
    resolution: {integrity: sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  module-alias@2.2.3:
    resolution: {integrity: sha512-23g5BFj4zdQL/b6tor7Ji+QY4pEfNH784BMslY9Qb0UnJWRAt+lQGLYmRaM0KDBwIG23ffEBELhZDP2rhi9f/Q==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  muggle-string@0.3.1:
    resolution: {integrity: sha512-ckmWDJjphvd/FvZawgygcUeQCxzvohjFO5RxTjj4eq8kw359gFF3E1brjfI+viLMxss5JrHTDRHZvu2/tuy0Qg==}

  murmurhash-js@1.0.0:
    resolution: {integrity: sha512-TvmkNhkv8yct0SVBSy+o8wYzXjE4Zz3PCesbfs8HiCXXdcTuocApFv11UWlNFWKYsP2okqrhb7JNlSm9InBhIw==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  node-int64@0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==}

  node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  nwsapi@2.2.7:
    resolution: {integrity: sha512-ub5E4+FBPKwAZx0UwIQOjYWGHTEq5sPqHQNRN8Z9e4A7u3Tj1weLJsL59yH9vmvqEtBHaOmT6cYQKIZOxp35FQ==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  object-inspect@1.13.1:
    resolution: {integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==}

  object-is@1.1.5:
    resolution: {integrity: sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  omggif@1.0.10:
    resolution: {integrity: sha512-LMJTtvgc/nugXj0Vcrrs68Mn2D1r0zf630VNtqtpI1FEO7e+O9FP4gqs9AcnBaSEeoHIPm28u6qgPR0oyEpGSw==}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  os-locale-s-fix@1.0.8-fix-1:
    resolution: {integrity: sha512-Sv0OvhPiMutICiwORAUefv02DCPb62IelBmo8ZsSrRHyI3FStqIWZvjqDkvtjU+lcujo7UNir+dCwKSqlEQ/5w==}
    engines: {node: '>=10', yarn: ^1.22.4}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  parse-bmfont-ascii@1.0.6:
    resolution: {integrity: sha512-U4RrVsUFCleIOBsIGYOMKjn9PavsGOXxbvYGtMOEfnId0SVNsgehXh1DxUdVPLoxd5mvcEtvmKs2Mmf0Mpa1ZA==}

  parse-bmfont-binary@1.0.6:
    resolution: {integrity: sha512-GxmsRea0wdGdYthjuUeWTMWPqm2+FAd4GI8vCvhgJsFnoGhTrLhXDDupwTo7rXVAgaLIGoVHDZS9p/5XbSqeWA==}

  parse-bmfont-xml@1.1.4:
    resolution: {integrity: sha512-bjnliEOmGv3y1aMEfREMBJ9tfL3WR0i0CKPj61DnSLaoxWR3nLrsQrEbCId/8rF4NyRF0cCqisSVXyQYWM+mCQ==}

  parse-css-font@4.0.0:
    resolution: {integrity: sha512-lnY7dTUfjRXsSo5G5C639L8RaBBaVSgL+5hacIFKsNHzeCJQ5SFSZv1DZmc7+wZv/22PFGOq2YbaEHLdaCS/mQ==}

  parse-headers@2.0.5:
    resolution: {integrity: sha512-ft3iAoLOB/MlwbNXgzy43SWGP6sQki2jQvAyBg/zDFAgr9bfNWZIUj42Kw2eJIl8kEi4PbgE6U1Zau/HwI75HA==}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-to-regexp@0.1.7:
    resolution: {integrity: sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==}

  pbf@3.2.1:
    resolution: {integrity: sha512-ClrV7pNOn7rtmoQVF4TS1vyU0WhYRnP92fzbfF75jAIwpnzdJXf8iTd4CMEqO4yUenH6NDqLiwjqlh6QgZzgLQ==}
    hasBin: true

  phin@2.9.3:
    resolution: {integrity: sha512-CzFr90qM24ju5f88quFC/6qohjC144rehe5n6DH900lgXmUe86+xCKc10ev56gRKC4/BkHUoG4uSiQgBiIXwDA==}

  picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  pinia-plugin-unistorage@0.0.17:
    resolution: {integrity: sha512-Oo4KTWyZP+7/fRRGG5ACoFIBZznEgFt8SPSOcg99t5NHzFP1wSTAfgl25eRr7Tv11pkWyzgZSa3oMGVIBBs1MA==}

  pinia@2.0.33:
    resolution: {integrity: sha512-HOj1yVV2itw6rNIrR2f7+MirGNxhORjrULL8GWgRwXsGSvEqIQ+SE0MYt6cwtpegzCda3i+rVTZM+AM7CG+kRg==}
    peerDependencies:
      '@vue/composition-api': ^1.4.0
      typescript: '>=4.4.4'
      vue: ^2.6.14 || ^3.2.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      typescript:
        optional: true

  pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  pixelmatch@4.0.2:
    resolution: {integrity: sha512-J8B6xqiO37sU/gkcMglv6h5Jbd9xNER7aHzpfRdNmV4IbQBzBpe4l9XmbG+xPF/znacgu2jfEw+wHffaq/YkXA==}
    hasBin: true

  pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}

  pngjs@3.4.0:
    resolution: {integrity: sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==}
    engines: {node: '>=4.0.0'}

  point-in-polygon@1.1.0:
    resolution: {integrity: sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==}

  polygon-clipping@0.15.3:
    resolution: {integrity: sha512-ho0Xx5DLkgxRx/+n4O74XyJ67DcyN3Tu9bGYKsnTukGAW6ssnuak6Mwcyb1wHy9MZc9xsUWqIoiazkZB5weECg==}

  postcss-attribute-case-insensitive@6.0.2:
    resolution: {integrity: sha512-IRuCwwAAQbgaLhxQdQcIIK0dCVXg3XDUnzgKD8iwdiYdwU4rMWRWyl/W9/0nA4ihVpq5pyALiHB2veBJ0292pw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-clamp@4.1.0:
    resolution: {integrity: sha512-ry4b1Llo/9zz+PKC+030KUnPITTJAHeOwjfAyyB60eT0AorGLdzp52s31OsPRHRf8NchkgFoG2y6fCfn1IV1Ow==}
    engines: {node: '>=7.6.0'}
    peerDependencies:
      postcss: ^8.4.6

  postcss-color-functional-notation@6.0.2:
    resolution: {integrity: sha512-FsjSmlSufuiFBsIqQ++VxFmvX7zKndZpBkHmfXr4wqhvzM92FTEkAh703iqWTl1U3faTgqioIqCbfqdWiFVwtw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-color-hex-alpha@9.0.2:
    resolution: {integrity: sha512-SfPjgr//VQ/DOCf80STIAsdAs7sbIbxATvVmd+Ec7JvR8onz9pjawhq3BJM3Pie40EE3TyB0P6hft16D33Nlyg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-color-rebeccapurple@9.0.1:
    resolution: {integrity: sha512-ds4cq5BjRieizVb2PnvbJ0omg9VCo2/KzluvoFZbxuGpsGJ5BQSD93CHBooinEtangCM5YqUOerGDl4xGmOb6Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-custom-media@10.0.2:
    resolution: {integrity: sha512-zcEFNRmDm2fZvTPdI1pIW3W//UruMcLosmMiCdpQnrCsTRzWlKQPYMa1ud9auL0BmrryKK1+JjIGn19K0UjO/w==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-custom-properties@13.3.2:
    resolution: {integrity: sha512-2Coszybpo8lpLY24vy2CYv9AasiZ39/bs8Imv0pWMq55Gl8NWzfc24OAo3zIX7rc6uUJAqESnVOMZ6V6lpMjJA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-custom-selectors@7.1.6:
    resolution: {integrity: sha512-svsjWRaxqL3vAzv71dV0/65P24/FB8TbPX+lWyyf9SZ7aZm4S4NhCn7N3Bg+Z5sZunG3FS8xQ80LrCU9hb37cw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-dir-pseudo-class@8.0.0:
    resolution: {integrity: sha512-Oy5BBi0dWPwij/IA+yDYj+/OBMQ9EPqAzTHeSNUYrUWdll/PRJmcbiUj0MNcsBi681I1gcSTLvMERPaXzdbvJg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-double-position-gradients@5.0.2:
    resolution: {integrity: sha512-KTbvdOOy8z8zb0BTkEg4/1vqlRlApdvjw8/pFoehgQl0WVO+fezDGlvo0B8xRA+XccA7ohkQCULKNsiNOx70Cw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-visible@9.0.0:
    resolution: {integrity: sha512-zA4TbVaIaT8npZBEROhZmlc+GBKE8AELPHXE7i4TmIUEQhw/P/mSJfY9t6tBzpQ1rABeGtEOHYrW4SboQeONMQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-within@8.0.0:
    resolution: {integrity: sha512-E7+J9nuQzZaA37D/MUZMX1K817RZGDab8qw6pFwzAkDd/QtlWJ9/WTKmzewNiuxzeq6WWY7ATiRePVoDKp+DnA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-font-variant@5.0.0:
    resolution: {integrity: sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA==}
    peerDependencies:
      postcss: ^8.1.0

  postcss-gap-properties@5.0.0:
    resolution: {integrity: sha512-YjsEEL6890P7MCv6fch6Am1yq0EhQCJMXyT4LBohiu87+4/WqR7y5W3RIv53WdA901hhytgRvjlrAhibhW4qsA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-image-set-function@6.0.1:
    resolution: {integrity: sha512-VlZncC9hhZ5tg0JllY4g6Z28BeoPO8DIkelioEEkXL0AA0IORlqYpTi2L8TUnl4YQrlwvBgxVy+mdZJw5R/cIQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-import@14.1.0:
    resolution: {integrity: sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-import@15.1.0:
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-lab-function@6.0.7:
    resolution: {integrity: sha512-4d1lhDVPukHFqkMv4G5vVcK+tgY52vwb5uR1SWKOaO5389r2q8fMxBWuXSW+YtbCOEGP0/X9KERi9E9le2pJuw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-load-config@3.1.4:
    resolution: {integrity: sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==}
    engines: {node: '>= 10'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-load-config@4.0.2:
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-logical@7.0.0:
    resolution: {integrity: sha512-zYf3vHkoW82f5UZTEXChTJvH49Yl9X37axTZsJGxrCG2kOUwtaAoz9E7tqYg0lsIoJLybaL8fk/2mOi81zVIUw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-modules-extract-imports@3.0.0:
    resolution: {integrity: sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-local-by-default@4.0.3:
    resolution: {integrity: sha512-2/u2zraspoACtrbFRnTijMiQtb4GW4BvatjaG/bCjYQo8kLTdevCUlwuBHx2sCnSyrI3x3qj4ZK1j5LQBgzmwA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-scope@3.0.0:
    resolution: {integrity: sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-values@4.0.0:
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules@4.3.1:
    resolution: {integrity: sha512-ItUhSUxBBdNamkT3KzIZwYNNRFKmkJrofvC2nWab3CPKhYBQ1f27XXh1PAPE27Psx58jeelPsxWB/+og+KEH0Q==}
    peerDependencies:
      postcss: ^8.0.0

  postcss-nested@6.0.1:
    resolution: {integrity: sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-nesting@12.0.1:
    resolution: {integrity: sha512-6LCqCWP9pqwXw/njMvNK0hGY44Fxc4B2EsGbn6xDcxbNRzP8GYoxT7yabVVMLrX3quqOJ9hg2jYMsnkedOf8pA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-opacity-percentage@2.0.0:
    resolution: {integrity: sha512-lyDrCOtntq5Y1JZpBFzIWm2wG9kbEdujpNt4NLannF+J9c8CgFIzPa80YQfdza+Y+yFfzbYj/rfoOsYsooUWTQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.2

  postcss-overflow-shorthand@5.0.0:
    resolution: {integrity: sha512-2rlxDyeSics/hC2FuMdPnWiP9WUPZ5x7FTuArXLFVpaSQ2woPSfZS4RD59HuEokbZhs/wPUQJ1E3MT6zVv94MQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-page-break@3.0.4:
    resolution: {integrity: sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ==}
    peerDependencies:
      postcss: ^8

  postcss-place@9.0.0:
    resolution: {integrity: sha512-qLEPD9VPH5opDVemwmRaujODF9nExn24VOC3ghgVLEvfYN7VZLwJHes0q/C9YR5hI2UC3VgBE8Wkdp1TxCXhtg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-preset-env@9.3.0:
    resolution: {integrity: sha512-ycw6doPrqV6QxDCtgiyGDef61bEfiSc59HGM4gOw/wxQxmKnhuEery61oOC/5ViENz/ycpRsuhTexs1kUBTvVw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-pseudo-class-any-link@9.0.0:
    resolution: {integrity: sha512-QNCYIL98VKFKY6HGDEJpF6+K/sg9bxcUYnOmNHJxZS5wsFDFaVoPeG68WAuhsqwbIBSo/b9fjEnTwY2mTSD+uA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-replace-overflow-wrap@4.0.0:
    resolution: {integrity: sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw==}
    peerDependencies:
      postcss: ^8.0.3

  postcss-selector-not@7.0.1:
    resolution: {integrity: sha512-1zT5C27b/zeJhchN7fP0kBr16Cc61mu7Si9uWWLoA3Px/D9tIJPKchJCkUH3tPO5D0pCFmGeApAv8XpXBQJ8SQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4

  postcss-selector-parser@6.0.13:
    resolution: {integrity: sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.32:
    resolution: {integrity: sha512-D/kj5JNu6oo2EIy+XL/26JEDTlIbB8hw85G8StOE6L74RQAVVP5rej6wxCNqyMbR4RkPfqvezVbPw81Ngd6Kcw==}
    engines: {node: ^10 || ^12 || >=14}

  potpack@2.0.0:
    resolution: {integrity: sha512-Q+/tYsFU9r7xoOJ+y/ZTtdVQwTWfzjbiXBDMM/JKUux3+QPP02iUuIoeBQ+Ot6oEDlC+/PGjB/5A3K7KKb7hcw==}

  pretty-format@27.5.1:
    resolution: {integrity: sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}

  protocol-buffers-schema@3.6.0:
    resolution: {integrity: sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw==}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qrcode-reader@1.0.4:
    resolution: {integrity: sha512-rRjALGNh9zVqvweg1j5OKIQKNsw3bLC+7qwlnead5K/9cb1cEIAGkwikt/09U0K+2IDWGD9CC6SP7tHAjUeqvQ==}

  qrcode-terminal@0.12.0:
    resolution: {integrity: sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==}
    hasBin: true

  qs@6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}

  query-string@8.1.0:
    resolution: {integrity: sha512-BFQeWxJOZxZGix7y+SByG3F36dA0AbTy9o6pSmKFcFz7DAj0re9Frkty3saBn3nHo3D0oZJ/+rx3r8H8r8Jbpw==}
    engines: {node: '>=14.16'}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quickselect@1.1.1:
    resolution: {integrity: sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==}

  quickselect@2.0.0:
    resolution: {integrity: sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==}

  qweather-icons@1.6.0:
    resolution: {integrity: sha512-uINrSOteHHarEeHRpP37aBnuuwYnWc1eyZ2gbnujoEqOVabIPDiEseF7a9eIOnBn7GZBlo5nYj29eOEfLH/bEA==}
    engines: {node: '>=10'}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.1:
    resolution: {integrity: sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==}
    engines: {node: '>= 0.8'}

  rbush@2.0.2:
    resolution: {integrity: sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==}

  rbush@3.0.1:
    resolution: {integrity: sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  regenerate-unicode-properties@10.1.1:
    resolution: {integrity: sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-runtime@0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  regenerator-runtime@0.14.0:
    resolution: {integrity: sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==}

  regexp.prototype.flags@1.5.1:
    resolution: {integrity: sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg==}
    engines: {node: '>= 0.4'}

  regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}

  regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve-protobuf-schema@2.1.0:
    resolution: {integrity: sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ==}

  resolve.exports@1.1.1:
    resolution: {integrity: sha512-/NtpHNDN7jWhAaQ9BvBUYZ6YTXsRBgfqWFWP7BZBaoMJO/I3G5OFzvTuWNlZC3aPjins1F+TNrLKsGbH4rfsRQ==}
    engines: {node: '>=10'}

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  robust-predicates@2.0.4:
    resolution: {integrity: sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg==}

  rollup@3.29.4:
    resolution: {integrity: sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rw@1.3.3:
    resolution: {integrity: sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==}

  safe-area-insets@1.4.1:
    resolution: {integrity: sha512-r/nRWTjFGhhm3w1Z6Kd/jY11srN+lHt2mNl1E/emQGW8ic7n3Avu4noibklfSM+Y34peNphHD/BSZecav0sXYQ==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sass-loader@10.5.0:
    resolution: {integrity: sha512-VsU71W7VR6SChMJZUqtrfLeMSA8ns7QTHbnA7cfevtjb3c392mX93lr0Dmr4uU1ch5uIbEmfmHjdrDYcXXkQ7w==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
      sass: ^1.3.0
      webpack: ^4.36.0 || ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true

  sass@1.69.5:
    resolution: {integrity: sha512-qg2+UCJibLr2LCVOt3OlPhr/dqVHWOa9XtZf2OjbLs/T4VPSJ00udtgJxH3neXZm+QqX8B+3cU7RaLqp1iVfcQ==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.3.0:
    resolution: {integrity: sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==}

  saxes@5.0.1:
    resolution: {integrity: sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==}
    engines: {node: '>=10'}

  schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.18.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@6.0.1:
    resolution: {integrity: sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==}

  serve-static@1.15.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==}
    engines: {node: '>= 0.8.0'}

  set-function-length@1.1.1:
    resolution: {integrity: sha512-VoaqjbBJKiWtg4yRcKBQ7g7wnGnLV3M8oLvVWwOk2PdYY6PEFegR1vezXR0tw6fZGF9csVakIRjrJiy2veSBFQ==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.1:
    resolution: {integrity: sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA==}
    engines: {node: '>= 0.4'}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel@1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}

  skmeans@0.9.7:
    resolution: {integrity: sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==}
    deprecated: Please use @jridgewell/sourcemap-codec instead

  splaytree@3.1.2:
    resolution: {integrity: sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==}

  split-on-first@3.0.0:
    resolution: {integrity: sha512-qxQJTx2ryR0Dw0ITYyekNQWpz6f8dGd7vffGNflQQ3Iqj9NJ6qiZ7ELpZsJ/QBhIVAiDfXdag3+Gp8RvWa62AA==}
    engines: {node: '>=12'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  stack-utils@2.0.6:
    resolution: {integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==}
    engines: {node: '>=10'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  string-hash@1.1.3:
    resolution: {integrity: sha512-kJUvRUFK49aub+a7T1nNE66EJbZBMnBgoC1UbCZ5n6bsZKBRga4KgBRTMn/pFkeCZSYtNeSyMxPDM0AXWELk2A==}

  string-length@4.0.2:
    resolution: {integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==}
    engines: {node: '>=10'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-bom@4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  sucrase@3.34.0:
    resolution: {integrity: sha512-70/LQEZ07TEcxiU2dz51FKaE6hCTWC6vr7FOk3Gr0U60C3shtAN+H+BFr9XlYe5xqf3RA8nrc+VIwzCfnxuXJw==}
    engines: {node: '>=8'}
    hasBin: true

  supercluster@8.0.1:
    resolution: {integrity: sha512-IiOea5kJ9iqzD2t7QJq/cREyLHTtSmUT6gQsweojg9WH2sYJqZK9SswTu6jrscO6D1G5v5vYZ9ru/eq85lXeZQ==}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-hyperlinks@2.3.0:
    resolution: {integrity: sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  svg-tags@1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}

  symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}

  systemjs@6.14.2:
    resolution: {integrity: sha512-1TlOwvKWdXxAY9vba+huLu99zrQURDWA8pUTYsRIYDZYQbGyK+pyEP4h4dlySsqo7ozyJBmYD20F+iUHhAltEg==}

  tailwindcss@3.3.6:
    resolution: {integrity: sha512-AKjF7qbbLvLaPieoKeTjG1+FyNZT6KaJMJPFeQyLfIp7l82ggH1fbHJSsYIvnbTFQOlkh+gBYpyby5GT1LIdLw==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  terminal-link@2.1.1:
    resolution: {integrity: sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==}
    engines: {node: '>=8'}

  terser-webpack-plugin@5.3.9:
    resolution: {integrity: sha512-ZuXsqE07EcggTWQjXUj+Aot/OMcD0bMKGgF63f7UxYcu5/AJF53aIpK1YoP5xR9l6s/Hy2b+t1AM0bLNPRuhwA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.26.0:
    resolution: {integrity: sha512-dytTGoE2oHgbNV9nTzgBEPaqAWvcJNl66VZ0BkJqlvp71IjO8CxdBx/ykCNb47cLnCmCvRZ6ZR0tLkqvZCdVBQ==}
    engines: {node: '>=10'}
    hasBin: true

  test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  throat@6.0.2:
    resolution: {integrity: sha512-WKexMoJj3vEuK0yFEapj8y64V0A6xcuPuK9Gt1d0R+dzCSJc0lHqQytAbSB4cDAK0dWh4T0E2ETkoLE2WZ41OQ==}

  timm@1.7.1:
    resolution: {integrity: sha512-IjZc9KIotudix8bMaBW6QvMuq64BrJWFs1+4V0lXwWGQZwH+LnX87doAYhem4caOEusRP9/g6jVDQmZ8XOk1nw==}

  tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}

  tinyqueue@2.0.3:
    resolution: {integrity: sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==}

  tmpl@1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  topojson-client@3.1.0:
    resolution: {integrity: sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==}
    hasBin: true

  topojson-server@3.0.1:
    resolution: {integrity: sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==}
    hasBin: true

  tough-cookie@4.1.3:
    resolution: {integrity: sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw==}
    engines: {node: '>=6'}

  tr46@2.1.0:
    resolution: {integrity: sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw==}
    engines: {node: '>=8'}

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}

  turf-jsts@1.2.3:
    resolution: {integrity: sha512-Ja03QIJlPuHt4IQ2FfGex4F4JAr8m3jpaHbFbQrgwr7s7L6U8ocrHiF3J1+wf9jzhGKxvDeaCAnGDot8OjGFyA==}

  type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}

  type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typedarray-to-buffer@3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}

  typescript@4.9.5:
    resolution: {integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==}
    engines: {node: '>=4.2.0'}
    hasBin: true

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  uni-mini-router@0.1.5:
    resolution: {integrity: sha512-pjeKTkiv1ryR7ANLUGJ0YScvOLTYUBqxUSch+PMJPKZnkQYd79Rg4qmEavwDLp9nOZJl9xrAX22b99FpOgZ/Sw==}

  uni-parse-pages@0.0.1:
    resolution: {integrity: sha512-OyuNFo/nCw7mnZnrtTYi/C12uWxmK7FczrxRATZETCh7j23uk1kOxEWVD4BXRSjif7YJg0ZPYQE4xBWqxUMHAw==}

  uniapp-axios-adapter@0.3.2:
    resolution: {integrity: sha512-Wbq8tkjxTw80KaWqpBbrzB575FlJ0YZ+i/EhPFqJmP8iL/x8yzf04RgdrKP7KlI9VArTpEO5PcSe44ciRzTJ8Q==}
    peerDependencies:
      axios: '*'

  unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.1.0:
    resolution: {integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  unquote@1.1.1:
    resolution: {integrity: sha512-vRCqFv6UhXpWxZPyGDh/F3ZpNv8/qo7w6iufLpQg9aKnQ71qM4B5KiI7Mia9COcjEhrO9LueHpMYjYzsWH3OIg==}

  update-browserslist-db@1.0.13:
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  utif@2.0.1:
    resolution: {integrity: sha512-Z/S1fNKCicQTf375lIP9G8Sa1H/phcysstNrrSdZKj1f9g58J4NMgb5IgiEZN9/nLMPDwF0W7hdOe9Qq2IYoLg==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  v8-to-istanbul@8.1.1:
    resolution: {integrity: sha512-FGtKtv3xIpR6BYhvgH8MI/y78oT7d8Au3ww4QIxymrCtZEh5b8gCw2siywE+puhEmuWKDtmfrvF5UlB298ut3w==}
    engines: {node: '>=10.12.0'}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vite-plugin-require-transform@1.0.21:
    resolution: {integrity: sha512-A3SrHhVg9tCW35O7E8kcuB71YTEdVd3EaM1zh6gbH4zxy4WzXSfcNf0UiWmaHHhr6wdFhiiAGdpR6S0SUxXkGQ==}

  vite@4.1.4:
    resolution: {integrity: sha512-3knk/HsbSTKEin43zHu7jTwYWv81f8kgAL99G5NWBcA1LKvtvcVAC4JjBH1arBunO9kQka+1oGbrMKOjk4ZrBg==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vk-uview-ui@1.5.2:
    resolution: {integrity: sha512-RRZgiEAc8qaUerSi7lSbrkCoLBgRUwFQHwP1V44pJO7Js+7HeHFEkkpPrtkOi14hl4CntR4qIhIDvaKmIJqVsw==}
    engines: {HBuilderX: ^3.1.0}

  vt-pbf@3.1.3:
    resolution: {integrity: sha512-2LzDFzt0mZKZ9IpVF2r69G9bXaP2Q2sArJCmcCgvfTdCCZzSyz4aCLoQyUilu37Ll56tCblIZrXFIjNUpGIlmA==}

  vue-demi@0.14.6:
    resolution: {integrity: sha512-8QA7wrYSHKaYgUxDA5ZC24w+eHm3sYCbp0EzcDwKqN3p6HqtTCGR/GVsPyZW92unff4UlcSh++lmqDWN3ZIq4w==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-router@4.2.5:
    resolution: {integrity: sha512-DIUpKcyg4+PTQKfFPX88UWhlagBEBEfJ5A8XDXRJLUnZOvcpMF8o/dnL90vpVkGaPbjvXazV/rC1qBKrZlFugw==}
    peerDependencies:
      vue: ^3.2.0

  vue-template-compiler@2.7.15:
    resolution: {integrity: sha512-yQxjxMptBL7UAog00O8sANud99C6wJF+7kgbcwqkvA38vCGF7HWE66w0ZFnS/kX5gSoJr/PQ4/oS3Ne2pW37Og==}

  vue-tsc@1.8.25:
    resolution: {integrity: sha512-lHsRhDc/Y7LINvYhZ3pv4elflFADoEOo67vfClAfF2heVHpHmVquLSjojgCSIwzA4F0Pc4vowT/psXCYcfk+iQ==}
    hasBin: true
    peerDependencies:
      typescript: '*'

  vue@3.2.45:
    resolution: {integrity: sha512-9Nx/Mg2b2xWlXykmCwiTUCWHbWIj53bnkizBxKai1g61f2Xit700A1ljowpTIM11e3uipOeiPcSqnmBg6gyiaA==}

  w3c-hr-time@1.0.2:
    resolution: {integrity: sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==}
    deprecated: Use your platform's native performance.now() and performance.timeOrigin.

  w3c-xmlserializer@2.0.0:
    resolution: {integrity: sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA==}
    engines: {node: '>=10'}

  walker@1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==}

  watchpack@2.4.0:
    resolution: {integrity: sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==}
    engines: {node: '>=10.13.0'}

  webidl-conversions@5.0.0:
    resolution: {integrity: sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==}
    engines: {node: '>=8'}

  webidl-conversions@6.1.0:
    resolution: {integrity: sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w==}
    engines: {node: '>=10.4'}

  webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  webpack@5.89.0:
    resolution: {integrity: sha512-qyfIC10pOr70V+jkmud8tMfajraGCZMBWJtrmuBymQKCrLTRejBI8STDp1MCyZu/QTdZSeacCQYpYNQVOzX5kw==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  whatwg-encoding@1.0.5:
    resolution: {integrity: sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==}

  whatwg-mimetype@2.3.0:
    resolution: {integrity: sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==}

  whatwg-url@8.7.0:
    resolution: {integrity: sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg==}
    engines: {node: '>=10'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  write-file-atomic@3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}

  ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.15.0:
    resolution: {integrity: sha512-H/Z3H55mrcrgjFwI+5jKavgXvwQLtfPCUEp6pi35VhoB0pfcHnSoyuTzkBEZpzq49g1193CUEwIvmsjcotenYw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xhr@2.6.0:
    resolution: {integrity: sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA==}

  xml-name-validator@3.0.0:
    resolution: {integrity: sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==}

  xml-parse-from-string@1.0.1:
    resolution: {integrity: sha512-ErcKwJTF54uRzzNMXq2X5sMIy88zJvfN2DmdoQvy7PAFJ+tPRU6ydWuOKNMyfmOjdyBQTFREi60s0Y0SyI0G0g==}

  xml2js@0.4.23:
    resolution: {integrity: sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}

  xmlhttprequest@1.8.0:
    resolution: {integrity: sha512-58Im/U0mlVBLM38NdZjHyhuMtCqa61469k2YP/AaPbvCoV9aQGUpbJBj1QRm2ytRiVQBD/fsw7L2bJGDVQswBA==}
    engines: {node: '>=0.4.0'}

  xregexp@3.1.0:
    resolution: {integrity: sha512-4Y1x6DyB8xRoxosooa6PlGWqmmSKatbzhrftZ7Purmm4B8R4qIEJG1A2hZsdz5DhmIqS0msC0I7KEq93GphEVg==}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yaml@2.3.4:
    resolution: {integrity: sha512-8aAvwVUSHpfEqTQ4w/KMlf3HcRdt50E5ODIQJBw1fQ5RL34xabzxtUlzTXVqc4rkZsPbvrXKWnABCD7kWSmocA==}
    engines: {node: '>= 14'}

  yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}

  z-paging@2.6.3:
    resolution: {integrity: sha512-8SdyASRPhjxvklPKSIuxt8i4PdAkidR8i+55lK8clqak3GFPom4uuWWeY9EgzXMq2Q+je6FzJpZtLqs5To75ug==}
    engines: {HBuilderX: ^3.0.7}

  zrender@5.4.4:
    resolution: {integrity: sha512-0VxCNJ7AGOMCWeHVyTrGzUgrK4asT4ml9PEkeGirAkKNYXYzoPJCLvmyfdoOXcjTHPs10OZVMfD1Rwg16AZyYw==}

snapshots:

  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.2.1':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20

  '@babel/code-frame@7.23.5':
    dependencies:
      '@babel/highlight': 7.23.4
      chalk: 2.4.2

  '@babel/compat-data@7.23.5': {}

  '@babel/core@7.23.5':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.23.5
      '@babel/generator': 7.23.5
      '@babel/helper-compilation-targets': 7.22.15
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.5)
      '@babel/helpers': 7.23.5
      '@babel/parser': 7.23.5
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.5
      '@babel/types': 7.23.5
      convert-source-map: 2.0.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.23.5':
    dependencies:
      '@babel/types': 7.23.5
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.22.5':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.15':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-compilation-targets@7.22.15':
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/helper-validator-option': 7.23.5
      browserslist: 4.22.2
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.23.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-replace-supers': 7.22.20(@babel/core@7.23.5)
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      semver: 6.3.1

  '@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-annotate-as-pure': 7.22.5
      regexpu-core: 5.3.2
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.4.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-compilation-targets': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.22.20': {}

  '@babel/helper-function-name@7.23.0':
    dependencies:
      '@babel/template': 7.22.15
      '@babel/types': 7.23.5

  '@babel/helper-hoist-variables@7.22.5':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-member-expression-to-functions@7.23.0':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-module-imports@7.22.15':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-module-transforms@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/helper-optimise-call-expression@7.22.5':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-plugin-utils@7.22.5': {}

  '@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-wrap-function': 7.22.20

  '@babel/helper-replace-supers@7.22.20(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5

  '@babel/helper-simple-access@7.22.5':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-split-export-declaration@7.22.6':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/helper-string-parser@7.23.4': {}

  '@babel/helper-validator-identifier@7.22.20': {}

  '@babel/helper-validator-option@7.23.5': {}

  '@babel/helper-wrap-function@7.22.20':
    dependencies:
      '@babel/helper-function-name': 7.23.0
      '@babel/template': 7.22.15
      '@babel/types': 7.23.5

  '@babel/helpers@7.23.5':
    dependencies:
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.5
      '@babel/types': 7.23.5
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.23.4':
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0

  '@babel/parser@7.23.5':
    dependencies:
      '@babel/types': 7.23.5

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-transform-optional-chaining': 7.23.4(@babel/core@7.23.5)

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-import-assertions@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-import-attributes@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-jsx@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-typescript@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-arrow-functions@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-async-generator-functions@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.23.5)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.23.5)

  '@babel/plugin-transform-async-to-generator@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.23.5)

  '@babel/plugin-transform-block-scoped-functions@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-block-scoping@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-class-properties@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-class-features-plugin': 7.23.5(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-class-static-block@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-class-features-plugin': 7.23.5(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.23.5)

  '@babel/plugin-transform-classes@7.23.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-compilation-targets': 7.22.15
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.20(@babel/core@7.23.5)
      '@babel/helper-split-export-declaration': 7.22.6
      globals: 11.12.0

  '@babel/plugin-transform-computed-properties@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/template': 7.22.15

  '@babel/plugin-transform-destructuring@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-dotall-regex@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-duplicate-keys@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-dynamic-import@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.23.5)

  '@babel/plugin-transform-exponentiation-operator@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-export-namespace-from@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.23.5)

  '@babel/plugin-transform-for-of@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-function-name@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-compilation-targets': 7.22.15
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-json-strings@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.23.5)

  '@babel/plugin-transform-literals@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-logical-assignment-operators@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.23.5)

  '@babel/plugin-transform-member-expression-literals@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-modules-amd@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-modules-commonjs@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-simple-access': 7.22.5

  '@babel/plugin-transform-modules-systemjs@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/plugin-transform-modules-umd@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-new-target@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-nullish-coalescing-operator@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.23.5)

  '@babel/plugin-transform-numeric-separator@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.23.5)

  '@babel/plugin-transform-object-rest-spread@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/core': 7.23.5
      '@babel/helper-compilation-targets': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-transform-parameters': 7.23.3(@babel/core@7.23.5)

  '@babel/plugin-transform-object-super@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.20(@babel/core@7.23.5)

  '@babel/plugin-transform-optional-catch-binding@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.23.5)

  '@babel/plugin-transform-optional-chaining@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.23.5)

  '@babel/plugin-transform-parameters@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-private-methods@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-class-features-plugin': 7.23.5(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-private-property-in-object@7.23.4(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.23.5(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.23.5)

  '@babel/plugin-transform-property-literals@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-regenerator@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-reserved-words@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-shorthand-properties@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-spread@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5

  '@babel/plugin-transform-sticky-regex@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-template-literals@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-typeof-symbol@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-typescript@7.23.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.23.5(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-typescript': 7.23.3(@babel/core@7.23.5)

  '@babel/plugin-transform-unicode-escapes@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-property-regex@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-regex@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-sets-regex@7.23.3(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.23.5)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/preset-env@7.23.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/core': 7.23.5
      '@babel/helper-compilation-targets': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-option': 7.23.5
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.23.5)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.23.5)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.23.5)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-import-assertions': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-import-attributes': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.23.5)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.23.5)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.23.5)
      '@babel/plugin-transform-arrow-functions': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-async-generator-functions': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-async-to-generator': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-block-scoped-functions': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-block-scoping': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-class-properties': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-class-static-block': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-classes': 7.23.5(@babel/core@7.23.5)
      '@babel/plugin-transform-computed-properties': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-destructuring': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-dotall-regex': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-duplicate-keys': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-dynamic-import': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-exponentiation-operator': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-export-namespace-from': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-for-of': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-function-name': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-json-strings': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-literals': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-logical-assignment-operators': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-member-expression-literals': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-modules-amd': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-modules-commonjs': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-modules-systemjs': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-modules-umd': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.22.5(@babel/core@7.23.5)
      '@babel/plugin-transform-new-target': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-numeric-separator': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-object-rest-spread': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-object-super': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-optional-catch-binding': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-optional-chaining': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-parameters': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-private-methods': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-private-property-in-object': 7.23.4(@babel/core@7.23.5)
      '@babel/plugin-transform-property-literals': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-regenerator': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-reserved-words': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-shorthand-properties': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-spread': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-sticky-regex': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-template-literals': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-typeof-symbol': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-unicode-escapes': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-unicode-property-regex': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-unicode-regex': 7.23.3(@babel/core@7.23.5)
      '@babel/plugin-transform-unicode-sets-regex': 7.23.3(@babel/core@7.23.5)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.23.5)
      babel-plugin-polyfill-corejs2: 0.4.6(@babel/core@7.23.5)
      babel-plugin-polyfill-corejs3: 0.8.6(@babel/core@7.23.5)
      babel-plugin-polyfill-regenerator: 0.5.3(@babel/core@7.23.5)
      core-js-compat: 3.34.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/types': 7.23.5
      esutils: 2.0.3

  '@babel/regjsgen@0.8.0': {}

  '@babel/runtime@7.23.5':
    dependencies:
      regenerator-runtime: 0.14.0

  '@babel/template@7.22.15':
    dependencies:
      '@babel/code-frame': 7.23.5
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5

  '@babel/traverse@7.23.5':
    dependencies:
      '@babel/code-frame': 7.23.5
      '@babel/generator': 7.23.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.23.5':
    dependencies:
      '@babel/helper-string-parser': 7.23.4
      '@babel/helper-validator-identifier': 7.22.20
      to-fast-properties: 2.0.0

  '@bcoe/v8-coverage@0.2.3': {}

  '@csstools/cascade-layer-name-parser@1.0.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)':
    dependencies:
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1

  '@csstools/color-helpers@3.0.2': {}

  '@csstools/css-calc@1.1.4(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)':
    dependencies:
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1

  '@csstools/css-color-parser@1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)':
    dependencies:
      '@csstools/color-helpers': 3.0.2
      '@csstools/css-calc': 1.1.4(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1

  '@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1)':
    dependencies:
      '@csstools/css-tokenizer': 2.2.1

  '@csstools/css-tokenizer@2.2.1': {}

  '@csstools/media-query-list-parser@2.1.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)':
    dependencies:
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1

  '@csstools/postcss-cascade-layers@4.0.1(postcss@8.4.32)':
    dependencies:
      '@csstools/selector-specificity': 3.0.0(postcss-selector-parser@6.0.13)
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  '@csstools/postcss-color-function@3.0.7(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32

  '@csstools/postcss-color-mix-function@2.0.7(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32

  '@csstools/postcss-exponential-functions@1.0.1(postcss@8.4.32)':
    dependencies:
      '@csstools/css-calc': 1.1.4(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32

  '@csstools/postcss-font-format-keywords@3.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-gamut-mapping@1.0.0(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32

  '@csstools/postcss-gradients-interpolation-method@4.0.7(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32

  '@csstools/postcss-hwb-function@3.0.6(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32

  '@csstools/postcss-ic-unit@3.0.2(postcss@8.4.32)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-initial@1.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32

  '@csstools/postcss-is-pseudo-class@4.0.3(postcss@8.4.32)':
    dependencies:
      '@csstools/selector-specificity': 3.0.0(postcss-selector-parser@6.0.13)
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  '@csstools/postcss-logical-float-and-clear@2.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32

  '@csstools/postcss-logical-overflow@1.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32

  '@csstools/postcss-logical-overscroll-behavior@1.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32

  '@csstools/postcss-logical-resize@2.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-logical-viewport-units@2.0.3(postcss@8.4.32)':
    dependencies:
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32

  '@csstools/postcss-media-minmax@1.1.0(postcss@8.4.32)':
    dependencies:
      '@csstools/css-calc': 1.1.4(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/media-query-list-parser': 2.1.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      postcss: 8.4.32

  '@csstools/postcss-media-queries-aspect-ratio-number-values@2.0.3(postcss@8.4.32)':
    dependencies:
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/media-query-list-parser': 2.1.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      postcss: 8.4.32

  '@csstools/postcss-nested-calc@3.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-normalize-display-values@3.0.1(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-oklab-function@3.0.7(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32

  '@csstools/postcss-progressive-custom-properties@3.0.2(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-relative-color-syntax@2.0.7(postcss@8.4.32)':
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32

  '@csstools/postcss-scope-pseudo-class@3.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  '@csstools/postcss-stepped-value-functions@3.0.2(postcss@8.4.32)':
    dependencies:
      '@csstools/css-calc': 1.1.4(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32

  '@csstools/postcss-text-decoration-shorthand@3.0.3(postcss@8.4.32)':
    dependencies:
      '@csstools/color-helpers': 3.0.2
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  '@csstools/postcss-trigonometric-functions@3.0.2(postcss@8.4.32)':
    dependencies:
      '@csstools/css-calc': 1.1.4(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32

  '@csstools/postcss-unset-value@3.0.0(postcss@8.4.32)':
    dependencies:
      postcss: 8.4.32

  '@csstools/selector-specificity@3.0.0(postcss-selector-parser@6.0.13)':
    dependencies:
      postcss-selector-parser: 6.0.13

  '@dcloudio/types@3.4.3': {}

  '@dcloudio/uni-app-plus@3.0.0-3081220230817001(postcss@8.4.32)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-app-uts': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-app-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@dcloudio/uni-app-vue': 3.0.0-3081220230817001
      debug: 4.3.4
      fs-extra: 10.1.0
      licia: 1.39.1
      postcss-selector-parser: 6.0.13
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vite
      - vue

  '@dcloudio/uni-app-uts@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-nvue-styler': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@rollup/pluginutils': 4.2.1
      '@vue/compiler-core': 3.2.47
      '@vue/compiler-sfc': 3.2.47
      '@vue/shared': 3.2.47
      debug: 4.3.4
      es-module-lexer: 1.4.1
      fs-extra: 10.1.0
      picocolors: 1.0.0
      source-map: 0.6.1
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-app-vite@3.0.0-3081220230817001(postcss@8.4.32)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-nvue-styler': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@rollup/pluginutils': 4.2.1
      '@vitejs/plugin-vue': 4.5.2(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@vue/compiler-dom': 3.2.47
      '@vue/compiler-sfc': 3.2.47
      debug: 4.3.4
      fs-extra: 10.1.0
      picocolors: 1.0.0
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vite
      - vue

  '@dcloudio/uni-app-vue@3.0.0-3081220230817001': {}

  '@dcloudio/uni-app@3.0.0-3081220230817001(@dcloudio/types@3.4.3)(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/types': 3.4.3
      '@dcloudio/uni-cloud': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-components': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-push': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@dcloudio/uni-stat': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-automator@3.0.0-3081220230817001(jest-environment-node@27.5.1)(jest@27.0.4)(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      address: 1.2.2
      cross-env: 7.0.3
      debug: 4.3.4
      default-gateway: 6.0.3
      fs-extra: 10.1.0
      jest: 27.0.4
      jest-environment-node: 27.5.1
      jsonc-parser: 3.2.0
      licia: 1.39.1
      qrcode-reader: 1.0.4
      qrcode-terminal: 0.12.0
      ws: 8.15.0
    transitivePeerDependencies:
      - bufferutil
      - postcss
      - supports-color
      - ts-node
      - utf-8-validate
      - vue

  '@dcloudio/uni-cli-shared@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/core': 7.23.5
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@intlify/core-base': 9.1.9
      '@intlify/shared': 9.1.9
      '@intlify/vue-devtools': 9.1.9
      '@rollup/pluginutils': 4.2.1
      '@vue/compiler-core': 3.2.47
      '@vue/compiler-dom': 3.2.47
      '@vue/compiler-sfc': 3.2.47
      '@vue/server-renderer': 3.2.47(vue@3.2.45)
      '@vue/shared': 3.2.47
      autoprefixer: 10.4.16(postcss@8.4.32)
      base64url: 3.0.1
      chokidar: 3.5.3
      compare-versions: 3.6.0
      debug: 4.3.4
      es-module-lexer: 1.4.1
      esbuild: 0.17.19
      estree-walker: 2.0.2
      fast-glob: 3.3.2
      fs-extra: 10.1.0
      hash-sum: 2.0.0
      jsonc-parser: 3.2.0
      magic-string: 0.30.5
      merge: 2.1.1
      mime: 3.0.0
      module-alias: 2.2.3
      os-locale-s-fix: 1.0.8-fix-1
      picocolors: 1.0.0
      postcss-import: 14.1.0(postcss@8.4.32)
      postcss-load-config: 3.1.4(postcss@8.4.32)
      postcss-modules: 4.3.1(postcss@8.4.32)
      postcss-selector-parser: 6.0.13
      resolve: 1.22.8
      tapable: 2.2.1
      xregexp: 3.1.0
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-cloud@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/shared': 3.2.47
      fast-glob: 3.3.2
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-components@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cloud': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-h5': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-h5-vite@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@rollup/pluginutils': 4.2.1
      '@vue/compiler-dom': 3.2.47
      '@vue/compiler-sfc': 3.2.47
      '@vue/server-renderer': 3.2.47(vue@3.2.45)
      '@vue/shared': 3.2.47
      debug: 4.3.4
      fs-extra: 10.1.0
      mime: 3.0.0
      module-alias: 2.2.3
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-h5-vue@3.0.0-3081220230817001(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/server-renderer': 3.2.47(vue@3.2.45)
    transitivePeerDependencies:
      - vue

  '@dcloudio/uni-h5@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-h5-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-h5-vue': 3.0.0-3081220230817001(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/server-renderer': 3.2.47(vue@3.2.45)
      '@vue/shared': 3.2.47
      debug: 4.3.4
      localstorage-polyfill: 1.0.1
      postcss-selector-parser: 6.0.13
      safe-area-insets: 1.4.1
      vue-router: 4.2.5(vue@3.2.45)
      xmlhttprequest: 1.8.0
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-i18n@3.0.0-3081220230817001': {}

  '@dcloudio/uni-mp-alipay@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-baidu@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-compiler': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-mp-weixin': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47
      jimp: 0.10.3
      licia: 1.39.1
      qrcode-reader: 1.0.4
      qrcode-terminal: 0.12.0
      ws: 8.15.0
    transitivePeerDependencies:
      - bufferutil
      - postcss
      - supports-color
      - ts-node
      - utf-8-validate
      - vue

  '@dcloudio/uni-mp-compiler@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@babel/generator': 7.23.5
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-core': 3.2.47
      '@vue/compiler-dom': 3.2.47
      '@vue/shared': 3.2.47
      estree-walker: 2.0.2
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-jd@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-compiler': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-kuaishou@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-compiler': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-mp-weixin': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - bufferutil
      - postcss
      - supports-color
      - ts-node
      - utf-8-validate
      - vue

  '@dcloudio/uni-mp-lark@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-compiler': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-toutiao': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-qq@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/shared': 3.2.47
      fs-extra: 10.1.0
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-toutiao@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-compiler': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-vite@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-i18n': 3.0.0-3081220230817001
      '@dcloudio/uni-mp-compiler': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/compiler-sfc': 3.2.47
      '@vue/shared': 3.2.47
      debug: 4.3.4
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-mp-vue@3.0.0-3081220230817001':
    dependencies:
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/shared': 3.2.47

  '@dcloudio/uni-mp-weixin@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/shared': 3.2.47
      jimp: 0.10.3
      licia: 1.39.1
      qrcode-reader: 1.0.4
      qrcode-terminal: 0.12.0
      ws: 8.15.0
    transitivePeerDependencies:
      - bufferutil
      - postcss
      - supports-color
      - ts-node
      - utf-8-validate
      - vue

  '@dcloudio/uni-nvue-styler@3.0.0-3081220230817001':
    dependencies:
      '@vue/shared': 3.2.47
      parse-css-font: 4.0.0
      postcss: 8.4.32

  '@dcloudio/uni-push@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-quickapp-webview@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vite': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-mp-vue': 3.0.0-3081220230817001
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@vue/shared': 3.2.47
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/uni-shared@3.0.0-3081220230817001':
    dependencies:
      '@vue/shared': 3.2.47

  '@dcloudio/uni-stacktracey@3.0.0-3081220230817001': {}

  '@dcloudio/uni-stat@3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)':
    dependencies:
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      debug: 4.3.4
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@dcloudio/vite-plugin-uni@3.0.0-3081220230817001(postcss@8.4.32)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-transform-typescript': 7.23.5(@babel/core@7.23.5)
      '@dcloudio/uni-cli-shared': 3.0.0-3081220230817001(postcss@8.4.32)(vue@3.2.45)
      '@dcloudio/uni-shared': 3.0.0-3081220230817001
      '@rollup/pluginutils': 4.2.1
      '@vitejs/plugin-legacy': 4.1.1(terser@5.26.0)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))
      '@vitejs/plugin-vue': 4.5.2(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@vitejs/plugin-vue-jsx': 3.1.0(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)
      '@vue/compiler-core': 3.2.47
      '@vue/compiler-dom': 3.2.47
      '@vue/compiler-sfc': 3.2.47
      '@vue/shared': 3.2.47
      cac: 6.7.9
      debug: 4.3.4
      estree-walker: 2.0.2
      express: 4.18.2
      fast-glob: 3.3.2
      fs-extra: 10.1.0
      hash-sum: 2.0.0
      jsonc-parser: 3.2.0
      magic-string: 0.30.5
      picocolors: 1.0.0
      terser: 5.26.0
      vite: 4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0)
    transitivePeerDependencies:
      - postcss
      - supports-color
      - ts-node
      - vue

  '@esbuild/android-arm64@0.16.17':
    optional: true

  '@esbuild/android-arm64@0.17.19':
    optional: true

  '@esbuild/android-arm@0.16.17':
    optional: true

  '@esbuild/android-arm@0.17.19':
    optional: true

  '@esbuild/android-x64@0.16.17':
    optional: true

  '@esbuild/android-x64@0.17.19':
    optional: true

  '@esbuild/darwin-arm64@0.16.17':
    optional: true

  '@esbuild/darwin-arm64@0.17.19':
    optional: true

  '@esbuild/darwin-arm64@0.19.12': {}

  '@esbuild/darwin-x64@0.16.17': {}

  '@esbuild/darwin-x64@0.17.19':
    optional: true

  '@esbuild/freebsd-arm64@0.16.17':
    optional: true

  '@esbuild/freebsd-arm64@0.17.19':
    optional: true

  '@esbuild/freebsd-x64@0.16.17':
    optional: true

  '@esbuild/freebsd-x64@0.17.19':
    optional: true

  '@esbuild/linux-arm64@0.16.17':
    optional: true

  '@esbuild/linux-arm64@0.17.19':
    optional: true

  '@esbuild/linux-arm@0.16.17':
    optional: true

  '@esbuild/linux-arm@0.17.19':
    optional: true

  '@esbuild/linux-ia32@0.16.17':
    optional: true

  '@esbuild/linux-ia32@0.17.19':
    optional: true

  '@esbuild/linux-loong64@0.16.17':
    optional: true

  '@esbuild/linux-loong64@0.17.19':
    optional: true

  '@esbuild/linux-mips64el@0.16.17':
    optional: true

  '@esbuild/linux-mips64el@0.17.19':
    optional: true

  '@esbuild/linux-ppc64@0.16.17':
    optional: true

  '@esbuild/linux-ppc64@0.17.19':
    optional: true

  '@esbuild/linux-riscv64@0.16.17':
    optional: true

  '@esbuild/linux-riscv64@0.17.19':
    optional: true

  '@esbuild/linux-s390x@0.16.17':
    optional: true

  '@esbuild/linux-s390x@0.17.19':
    optional: true

  '@esbuild/linux-x64@0.16.17':
    optional: true

  '@esbuild/linux-x64@0.17.19':
    optional: true

  '@esbuild/netbsd-x64@0.16.17':
    optional: true

  '@esbuild/netbsd-x64@0.17.19':
    optional: true

  '@esbuild/openbsd-x64@0.16.17':
    optional: true

  '@esbuild/openbsd-x64@0.17.19':
    optional: true

  '@esbuild/sunos-x64@0.16.17':
    optional: true

  '@esbuild/sunos-x64@0.17.19':
    optional: true

  '@esbuild/win32-arm64@0.16.17':
    optional: true

  '@esbuild/win32-arm64@0.17.19':
    optional: true

  '@esbuild/win32-ia32@0.16.17':
    optional: true

  '@esbuild/win32-ia32@0.17.19':
    optional: true

  '@esbuild/win32-x64@0.16.17':
    optional: true

  '@esbuild/win32-x64@0.17.19':
    optional: true

  '@intlify/core-base@9.1.9':
    dependencies:
      '@intlify/devtools-if': 9.1.9
      '@intlify/message-compiler': 9.1.9
      '@intlify/message-resolver': 9.1.9
      '@intlify/runtime': 9.1.9
      '@intlify/shared': 9.1.9
      '@intlify/vue-devtools': 9.1.9

  '@intlify/devtools-if@9.1.9':
    dependencies:
      '@intlify/shared': 9.1.9

  '@intlify/message-compiler@9.1.9':
    dependencies:
      '@intlify/message-resolver': 9.1.9
      '@intlify/shared': 9.1.9
      source-map: 0.6.1

  '@intlify/message-resolver@9.1.9': {}

  '@intlify/runtime@9.1.9':
    dependencies:
      '@intlify/message-compiler': 9.1.9
      '@intlify/message-resolver': 9.1.9
      '@intlify/shared': 9.1.9

  '@intlify/shared@9.1.9': {}

  '@intlify/vue-devtools@9.1.9':
    dependencies:
      '@intlify/message-resolver': 9.1.9
      '@intlify/runtime': 9.1.9
      '@intlify/shared': 9.1.9

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/console@27.5.1':
    dependencies:
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      chalk: 4.1.2
      jest-message-util: 27.5.1
      jest-util: 27.5.1
      slash: 3.0.0

  '@jest/core@27.5.1':
    dependencies:
      '@jest/console': 27.5.1
      '@jest/reporters': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.8.1
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 27.5.1
      jest-config: 27.5.1
      jest-haste-map: 27.5.1
      jest-message-util: 27.5.1
      jest-regex-util: 27.5.1
      jest-resolve: 27.5.1
      jest-resolve-dependencies: 27.5.1
      jest-runner: 27.5.1
      jest-runtime: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      jest-validate: 27.5.1
      jest-watcher: 27.5.1
      micromatch: 4.0.5
      rimraf: 3.0.2
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - ts-node
      - utf-8-validate

  '@jest/environment@27.5.1':
    dependencies:
      '@jest/fake-timers': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      jest-mock: 27.5.1

  '@jest/fake-timers@27.5.1':
    dependencies:
      '@jest/types': 27.5.1
      '@sinonjs/fake-timers': 8.1.0
      '@types/node': 20.10.4
      jest-message-util: 27.5.1
      jest-mock: 27.5.1
      jest-util: 27.5.1

  '@jest/globals@27.5.1':
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/types': 27.5.1
      expect: 27.5.1

  '@jest/reporters@27.5.1':
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 5.2.1
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.6
      jest-haste-map: 27.5.1
      jest-resolve: 27.5.1
      jest-util: 27.5.1
      jest-worker: 27.5.1
      slash: 3.0.0
      source-map: 0.6.1
      string-length: 4.0.2
      terminal-link: 2.1.1
      v8-to-istanbul: 8.1.1
    transitivePeerDependencies:
      - supports-color

  '@jest/source-map@27.5.1':
    dependencies:
      callsites: 3.1.0
      graceful-fs: 4.2.11
      source-map: 0.6.1

  '@jest/test-result@27.5.1':
    dependencies:
      '@jest/console': 27.5.1
      '@jest/types': 27.5.1
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-sequencer@27.5.1':
    dependencies:
      '@jest/test-result': 27.5.1
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-runtime: 27.5.1
    transitivePeerDependencies:
      - supports-color

  '@jest/transform@27.5.1':
    dependencies:
      '@babel/core': 7.23.5
      '@jest/types': 27.5.1
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 1.9.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-regex-util: 27.5.1
      jest-util: 27.5.1
      micromatch: 4.0.5
      pirates: 4.0.6
      slash: 3.0.0
      source-map: 0.6.1
      write-file-atomic: 3.0.3
    transitivePeerDependencies:
      - supports-color

  '@jest/types@27.5.1':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 20.10.4
      '@types/yargs': 16.0.9
      chalk: 4.1.2

  '@jimp/bmp@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      bmp-js: 0.1.0
      core-js: 3.34.0

  '@jimp/core@0.10.3':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/utils': 0.10.3
      any-base: 1.1.0
      buffer: 5.7.1
      core-js: 3.34.0
      exif-parser: 0.1.12
      file-type: 9.0.0
      load-bmfont: 1.4.1
      mkdirp: 0.5.6
      phin: 2.9.3
      pixelmatch: 4.0.2
      tinycolor2: 1.6.0

  '@jimp/custom@0.10.3':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/core': 0.10.3
      core-js: 3.34.0

  '@jimp/gif@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0
      omggif: 1.0.10

  '@jimp/jpeg@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0
      jpeg-js: 0.3.7

  '@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-blur@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-circle@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-color@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0
      tinycolor2: 1.6.0

  '@jimp/plugin-contain@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-scale@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-blit': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-scale': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-cover@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-scale@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-crop': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-scale': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-displace@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-dither@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-fisheye@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-flip@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-rotate@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-rotate': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-gaussian@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-invert@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-mask@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-normalize@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-print@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-blit': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/utils': 0.10.3
      core-js: 3.34.0
      load-bmfont: 1.4.1

  '@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-rotate@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-blit': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-crop': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-scale@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-shadow@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blur@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-blur': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugin-threshold@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-color@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-color': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/utils': 0.10.3
      core-js: 3.34.0

  '@jimp/plugins@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugin-blit': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-blur': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-circle': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-color': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-contain': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-scale@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)))
      '@jimp/plugin-cover': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-scale@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)))
      '@jimp/plugin-crop': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-displace': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-dither': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-fisheye': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-flip': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-rotate@0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3)))
      '@jimp/plugin-gaussian': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-invert': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-mask': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-normalize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-print': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))
      '@jimp/plugin-resize': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/plugin-rotate': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blit@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-crop@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      '@jimp/plugin-scale': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      '@jimp/plugin-shadow': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-blur@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      '@jimp/plugin-threshold': 0.10.3(@jimp/custom@0.10.3)(@jimp/plugin-color@0.10.3(@jimp/custom@0.10.3))(@jimp/plugin-resize@0.10.3(@jimp/custom@0.10.3))
      core-js: 3.34.0
      timm: 1.7.1

  '@jimp/png@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/utils': 0.10.3
      core-js: 3.34.0
      pngjs: 3.4.0

  '@jimp/tiff@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      core-js: 3.34.0
      utif: 2.0.1

  '@jimp/types@0.10.3(@jimp/custom@0.10.3)':
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/bmp': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/custom': 0.10.3
      '@jimp/gif': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/jpeg': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/png': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/tiff': 0.10.3(@jimp/custom@0.10.3)
      core-js: 3.34.0
      timm: 1.7.1

  '@jimp/utils@0.10.3':
    dependencies:
      '@babel/runtime': 7.23.5
      core-js: 3.34.0
      regenerator-runtime: 0.13.11

  '@jridgewell/gen-mapping@0.3.3':
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.20

  '@jridgewell/resolve-uri@3.1.1': {}

  '@jridgewell/set-array@1.1.2': {}

  '@jridgewell/source-map@0.3.5':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.20':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15

  '@mapbox/geojson-rewind@0.5.2':
    dependencies:
      get-stream: 6.0.1
      minimist: 1.2.8

  '@mapbox/jsonlint-lines-primitives@2.0.2': {}

  '@mapbox/mapbox-gl-supported@2.0.1': {}

  '@mapbox/point-geometry@0.1.0': {}

  '@mapbox/tiny-sdf@2.0.6': {}

  '@mapbox/unitbezier@0.0.1': {}

  '@mapbox/vector-tile@1.3.1':
    dependencies:
      '@mapbox/point-geometry': 0.1.0

  '@mapbox/whoots-js@3.1.0': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0

  '@rollup/pluginutils@4.2.1':
    dependencies:
      estree-walker: 2.0.2
      picomatch: 2.3.1

  '@sinonjs/commons@1.8.6':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@8.1.0':
    dependencies:
      '@sinonjs/commons': 1.8.6

  '@tootallnate/once@1.1.2': {}

  '@turf/along@6.5.0':
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/angle@6.5.0':
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/rhumb-bearing': 6.5.0

  '@turf/area@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/bbox-clip@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/bbox-polygon@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/bbox@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/bearing@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/bezier-spline@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/boolean-clockwise@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/boolean-contains@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/boolean-point-on-line': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/boolean-crosses@6.5.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/polygon-to-line': 6.5.0

  '@turf/boolean-disjoint@6.5.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/polygon-to-line': 6.5.0

  '@turf/boolean-equal@6.5.0':
    dependencies:
      '@turf/clean-coords': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      geojson-equality: 0.1.6

  '@turf/boolean-intersects@6.5.0':
    dependencies:
      '@turf/boolean-disjoint': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/boolean-overlap@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/line-overlap': 6.5.0
      '@turf/meta': 6.5.0
      geojson-equality: 0.1.6

  '@turf/boolean-parallel@6.5.0':
    dependencies:
      '@turf/clean-coords': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/rhumb-bearing': 6.5.0

  '@turf/boolean-point-in-polygon@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/boolean-point-on-line@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/boolean-within@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/boolean-point-on-line': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/buffer@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/center': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/projection': 6.5.0
      d3-geo: 1.7.1
      turf-jsts: 1.2.3

  '@turf/center-mean@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/center-median@6.5.0':
    dependencies:
      '@turf/center-mean': 6.5.0
      '@turf/centroid': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/center-of-mass@6.5.0':
    dependencies:
      '@turf/centroid': 6.5.0
      '@turf/convex': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/center@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/centroid@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/circle@6.5.0':
    dependencies:
      '@turf/destination': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/clean-coords@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/clone@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/clusters-dbscan@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      density-clustering: 1.3.0

  '@turf/clusters-kmeans@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      skmeans: 0.9.7

  '@turf/clusters@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/collect@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      rbush: 2.0.2

  '@turf/combine@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/concave@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/tin': 6.5.0
      topojson-client: 3.1.0
      topojson-server: 3.0.1

  '@turf/convex@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      concaveman: 1.2.1

  '@turf/destination@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/difference@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      polygon-clipping: 0.15.3

  '@turf/dissolve@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      polygon-clipping: 0.15.3

  '@turf/distance-weight@6.5.0':
    dependencies:
      '@turf/centroid': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/distance@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/ellipse@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/rhumb-destination': 6.5.0
      '@turf/transform-rotate': 6.5.0

  '@turf/envelope@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/bbox-polygon': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/explode@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/flatten@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/flip@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/great-circle@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/helpers@6.5.0': {}

  '@turf/hex-grid@6.5.0':
    dependencies:
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/intersect': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/interpolate@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/centroid': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/hex-grid': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/point-grid': 6.5.0
      '@turf/square-grid': 6.5.0
      '@turf/triangle-grid': 6.5.0

  '@turf/intersect@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      polygon-clipping: 0.15.3

  '@turf/invariant@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/isobands@6.5.0':
    dependencies:
      '@turf/area': 6.5.0
      '@turf/bbox': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/explode': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      object-assign: 4.1.1

  '@turf/isolines@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      object-assign: 4.1.1

  '@turf/kinks@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/length@6.5.0':
    dependencies:
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/line-arc@6.5.0':
    dependencies:
      '@turf/circle': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/line-chunk@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/length': 6.5.0
      '@turf/line-slice-along': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/line-intersect@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/meta': 6.5.0
      geojson-rbush: 3.2.0

  '@turf/line-offset@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/line-overlap@6.5.0':
    dependencies:
      '@turf/boolean-point-on-line': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/nearest-point-on-line': 6.5.0
      deep-equal: 1.1.2
      geojson-rbush: 3.2.0

  '@turf/line-segment@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/line-slice-along@6.5.0':
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/line-slice@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/nearest-point-on-line': 6.5.0

  '@turf/line-split@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/nearest-point-on-line': 6.5.0
      '@turf/square': 6.5.0
      '@turf/truncate': 6.5.0
      geojson-rbush: 3.2.0

  '@turf/line-to-polygon@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/mask@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      polygon-clipping: 0.15.3

  '@turf/meta@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/midpoint@6.5.0':
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/moran-index@6.5.0':
    dependencies:
      '@turf/distance-weight': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/nearest-point-on-line@6.5.0':
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/nearest-point-to-line@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/point-to-line-distance': 6.5.0
      object-assign: 4.1.1

  '@turf/nearest-point@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/planepoint@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/point-grid@6.5.0':
    dependencies:
      '@turf/boolean-within': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/point-on-feature@6.5.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/center': 6.5.0
      '@turf/explode': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/nearest-point': 6.5.0

  '@turf/point-to-line-distance@6.5.0':
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/projection': 6.5.0
      '@turf/rhumb-bearing': 6.5.0
      '@turf/rhumb-distance': 6.5.0

  '@turf/points-within-polygon@6.5.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/polygon-smooth@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/polygon-tangents@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/boolean-within': 6.5.0
      '@turf/explode': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/nearest-point': 6.5.0

  '@turf/polygon-to-line@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/polygonize@6.5.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/envelope': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/projection@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/random@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/rectangle-grid@6.5.0':
    dependencies:
      '@turf/boolean-intersects': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/rewind@6.5.0':
    dependencies:
      '@turf/boolean-clockwise': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/rhumb-bearing@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/rhumb-destination@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/rhumb-distance@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0

  '@turf/sample@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/sector@6.5.0':
    dependencies:
      '@turf/circle': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/line-arc': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/shortest-path@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/bbox-polygon': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/clean-coords': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/transform-scale': 6.5.0

  '@turf/simplify@6.5.0':
    dependencies:
      '@turf/clean-coords': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/square-grid@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/rectangle-grid': 6.5.0

  '@turf/square@6.5.0':
    dependencies:
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0

  '@turf/standard-deviational-ellipse@6.5.0':
    dependencies:
      '@turf/center-mean': 6.5.0
      '@turf/ellipse': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/points-within-polygon': 6.5.0

  '@turf/tag@6.5.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/tesselate@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      earcut: 2.2.4

  '@turf/tin@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0

  '@turf/transform-rotate@6.5.0':
    dependencies:
      '@turf/centroid': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/rhumb-bearing': 6.5.0
      '@turf/rhumb-destination': 6.5.0
      '@turf/rhumb-distance': 6.5.0

  '@turf/transform-scale@6.5.0':
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/center': 6.5.0
      '@turf/centroid': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/rhumb-bearing': 6.5.0
      '@turf/rhumb-destination': 6.5.0
      '@turf/rhumb-distance': 6.5.0

  '@turf/transform-translate@6.5.0':
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/rhumb-destination': 6.5.0

  '@turf/triangle-grid@6.5.0':
    dependencies:
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/intersect': 6.5.0

  '@turf/truncate@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0

  '@turf/turf@6.5.0':
    dependencies:
      '@turf/along': 6.5.0
      '@turf/angle': 6.5.0
      '@turf/area': 6.5.0
      '@turf/bbox': 6.5.0
      '@turf/bbox-clip': 6.5.0
      '@turf/bbox-polygon': 6.5.0
      '@turf/bearing': 6.5.0
      '@turf/bezier-spline': 6.5.0
      '@turf/boolean-clockwise': 6.5.0
      '@turf/boolean-contains': 6.5.0
      '@turf/boolean-crosses': 6.5.0
      '@turf/boolean-disjoint': 6.5.0
      '@turf/boolean-equal': 6.5.0
      '@turf/boolean-intersects': 6.5.0
      '@turf/boolean-overlap': 6.5.0
      '@turf/boolean-parallel': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/boolean-point-on-line': 6.5.0
      '@turf/boolean-within': 6.5.0
      '@turf/buffer': 6.5.0
      '@turf/center': 6.5.0
      '@turf/center-mean': 6.5.0
      '@turf/center-median': 6.5.0
      '@turf/center-of-mass': 6.5.0
      '@turf/centroid': 6.5.0
      '@turf/circle': 6.5.0
      '@turf/clean-coords': 6.5.0
      '@turf/clone': 6.5.0
      '@turf/clusters': 6.5.0
      '@turf/clusters-dbscan': 6.5.0
      '@turf/clusters-kmeans': 6.5.0
      '@turf/collect': 6.5.0
      '@turf/combine': 6.5.0
      '@turf/concave': 6.5.0
      '@turf/convex': 6.5.0
      '@turf/destination': 6.5.0
      '@turf/difference': 6.5.0
      '@turf/dissolve': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/distance-weight': 6.5.0
      '@turf/ellipse': 6.5.0
      '@turf/envelope': 6.5.0
      '@turf/explode': 6.5.0
      '@turf/flatten': 6.5.0
      '@turf/flip': 6.5.0
      '@turf/great-circle': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/hex-grid': 6.5.0
      '@turf/interpolate': 6.5.0
      '@turf/intersect': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/isobands': 6.5.0
      '@turf/isolines': 6.5.0
      '@turf/kinks': 6.5.0
      '@turf/length': 6.5.0
      '@turf/line-arc': 6.5.0
      '@turf/line-chunk': 6.5.0
      '@turf/line-intersect': 6.5.0
      '@turf/line-offset': 6.5.0
      '@turf/line-overlap': 6.5.0
      '@turf/line-segment': 6.5.0
      '@turf/line-slice': 6.5.0
      '@turf/line-slice-along': 6.5.0
      '@turf/line-split': 6.5.0
      '@turf/line-to-polygon': 6.5.0
      '@turf/mask': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/midpoint': 6.5.0
      '@turf/moran-index': 6.5.0
      '@turf/nearest-point': 6.5.0
      '@turf/nearest-point-on-line': 6.5.0
      '@turf/nearest-point-to-line': 6.5.0
      '@turf/planepoint': 6.5.0
      '@turf/point-grid': 6.5.0
      '@turf/point-on-feature': 6.5.0
      '@turf/point-to-line-distance': 6.5.0
      '@turf/points-within-polygon': 6.5.0
      '@turf/polygon-smooth': 6.5.0
      '@turf/polygon-tangents': 6.5.0
      '@turf/polygon-to-line': 6.5.0
      '@turf/polygonize': 6.5.0
      '@turf/projection': 6.5.0
      '@turf/random': 6.5.0
      '@turf/rewind': 6.5.0
      '@turf/rhumb-bearing': 6.5.0
      '@turf/rhumb-destination': 6.5.0
      '@turf/rhumb-distance': 6.5.0
      '@turf/sample': 6.5.0
      '@turf/sector': 6.5.0
      '@turf/shortest-path': 6.5.0
      '@turf/simplify': 6.5.0
      '@turf/square': 6.5.0
      '@turf/square-grid': 6.5.0
      '@turf/standard-deviational-ellipse': 6.5.0
      '@turf/tag': 6.5.0
      '@turf/tesselate': 6.5.0
      '@turf/tin': 6.5.0
      '@turf/transform-rotate': 6.5.0
      '@turf/transform-scale': 6.5.0
      '@turf/transform-translate': 6.5.0
      '@turf/triangle-grid': 6.5.0
      '@turf/truncate': 6.5.0
      '@turf/union': 6.5.0
      '@turf/unkink-polygon': 6.5.0
      '@turf/voronoi': 6.5.0

  '@turf/union@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      polygon-clipping: 0.15.3

  '@turf/unkink-polygon@6.5.0':
    dependencies:
      '@turf/area': 6.5.0
      '@turf/boolean-point-in-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      rbush: 2.0.2

  '@turf/voronoi@6.5.0':
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      d3-voronoi: 1.1.2

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5
      '@types/babel__generator': 7.6.7
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.4

  '@types/babel__generator@7.6.7':
    dependencies:
      '@babel/types': 7.23.5

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.23.5
      '@babel/types': 7.23.5

  '@types/babel__traverse@7.20.4':
    dependencies:
      '@babel/types': 7.23.5

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 8.44.8
      '@types/estree': 1.0.5

  '@types/eslint@8.44.8':
    dependencies:
      '@types/estree': 1.0.5
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.5': {}

  '@types/geojson@7946.0.8': {}

  '@types/graceful-fs@4.1.9':
    dependencies:
      '@types/node': 20.10.4

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/json-schema@7.0.15': {}

  '@types/node@20.10.4':
    dependencies:
      undici-types: 5.26.5

  '@types/prettier@2.7.3': {}

  '@types/stack-utils@2.0.3': {}

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@16.0.9':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@uni-helper/uni-env@0.0.3': {}

  '@uni-helper/vite-plugin-uni-tailwind@0.13.1(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))':
    dependencies:
      '@babel/core': 7.23.5
      '@uni-helper/uni-env': 0.0.3
      '@vivaxy/wxml': 2.1.0
      postcss: 8.4.32
      vite: 4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-legacy@4.1.1(terser@5.26.0)(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/preset-env': 7.23.5(@babel/core@7.23.5)
      browserslist: 4.22.2
      core-js: 3.34.0
      magic-string: 0.30.5
      regenerator-runtime: 0.13.11
      systemjs: 6.14.2
      terser: 5.26.0
      vite: 4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue-jsx@3.1.0(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/plugin-transform-typescript': 7.23.5(@babel/core@7.23.5)
      '@vue/babel-plugin-jsx': 1.1.5(@babel/core@7.23.5)
      vite: 4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0)
      vue: 3.2.45
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@4.5.2(vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0))(vue@3.2.45)':
    dependencies:
      vite: 4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0)
      vue: 3.2.45

  '@vivaxy/wxml@2.1.0': {}

  '@volar/language-core@1.11.1':
    dependencies:
      '@volar/source-map': 1.11.1

  '@volar/source-map@1.11.1':
    dependencies:
      muggle-string: 0.3.1

  '@volar/typescript@1.11.1':
    dependencies:
      '@volar/language-core': 1.11.1
      path-browserify: 1.0.1

  '@vue/babel-helper-vue-transform-on@1.1.5': {}

  '@vue/babel-plugin-jsx@1.1.5(@babel/core@7.23.5)':
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-module-imports': 7.22.15
      '@babel/plugin-syntax-jsx': 7.23.3(@babel/core@7.23.5)
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.5
      '@babel/types': 7.23.5
      '@vue/babel-helper-vue-transform-on': 1.1.5
      camelcase: 6.3.0
      html-tags: 3.3.1
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color

  '@vue/compiler-core@3.2.45':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/shared': 3.2.45
      estree-walker: 2.0.2
      source-map: 0.6.1

  '@vue/compiler-core@3.2.47':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/shared': 3.2.47
      estree-walker: 2.0.2
      source-map: 0.6.1

  '@vue/compiler-core@3.3.11':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/shared': 3.3.11
      estree-walker: 2.0.2
      source-map-js: 1.0.2

  '@vue/compiler-dom@3.2.45':
    dependencies:
      '@vue/compiler-core': 3.2.45
      '@vue/shared': 3.2.45

  '@vue/compiler-dom@3.2.47':
    dependencies:
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47

  '@vue/compiler-dom@3.3.11':
    dependencies:
      '@vue/compiler-core': 3.3.11
      '@vue/shared': 3.3.11

  '@vue/compiler-sfc@3.2.45':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/compiler-core': 3.2.45
      '@vue/compiler-dom': 3.2.45
      '@vue/compiler-ssr': 3.2.45
      '@vue/reactivity-transform': 3.2.45
      '@vue/shared': 3.2.45
      estree-walker: 2.0.2
      magic-string: 0.25.9
      postcss: 8.4.32
      source-map: 0.6.1

  '@vue/compiler-sfc@3.2.47':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/compiler-core': 3.2.47
      '@vue/compiler-dom': 3.2.47
      '@vue/compiler-ssr': 3.2.47
      '@vue/reactivity-transform': 3.2.47
      '@vue/shared': 3.2.47
      estree-walker: 2.0.2
      magic-string: 0.25.9
      postcss: 8.4.32
      source-map: 0.6.1

  '@vue/compiler-ssr@3.2.45':
    dependencies:
      '@vue/compiler-dom': 3.2.45
      '@vue/shared': 3.2.45

  '@vue/compiler-ssr@3.2.47':
    dependencies:
      '@vue/compiler-dom': 3.2.47
      '@vue/shared': 3.2.47

  '@vue/devtools-api@6.5.1': {}

  '@vue/language-core@1.8.25(typescript@4.9.5)':
    dependencies:
      '@volar/language-core': 1.11.1
      '@volar/source-map': 1.11.1
      '@vue/compiler-dom': 3.3.11
      '@vue/shared': 3.3.11
      computeds: 0.0.1
      minimatch: 9.0.3
      muggle-string: 0.3.1
      path-browserify: 1.0.1
      vue-template-compiler: 2.7.15
    optionalDependencies:
      typescript: 4.9.5

  '@vue/reactivity-transform@3.2.45':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/compiler-core': 3.2.45
      '@vue/shared': 3.2.45
      estree-walker: 2.0.2
      magic-string: 0.25.9

  '@vue/reactivity-transform@3.2.47':
    dependencies:
      '@babel/parser': 7.23.5
      '@vue/compiler-core': 3.2.47
      '@vue/shared': 3.2.47
      estree-walker: 2.0.2
      magic-string: 0.25.9

  '@vue/reactivity@3.2.45':
    dependencies:
      '@vue/shared': 3.2.45

  '@vue/reactivity@3.3.11':
    dependencies:
      '@vue/shared': 3.3.11

  '@vue/runtime-core@3.2.45':
    dependencies:
      '@vue/reactivity': 3.2.45
      '@vue/shared': 3.2.45

  '@vue/runtime-core@3.3.11':
    dependencies:
      '@vue/reactivity': 3.3.11
      '@vue/shared': 3.3.11

  '@vue/runtime-dom@3.2.45':
    dependencies:
      '@vue/runtime-core': 3.2.45
      '@vue/shared': 3.2.45
      csstype: 2.6.21

  '@vue/server-renderer@3.2.45(vue@3.2.45)':
    dependencies:
      '@vue/compiler-ssr': 3.2.45
      '@vue/shared': 3.2.45
      vue: 3.2.45

  '@vue/server-renderer@3.2.47(vue@3.2.45)':
    dependencies:
      '@vue/compiler-ssr': 3.2.47
      '@vue/shared': 3.2.47
      vue: 3.2.45

  '@vue/shared@3.2.45': {}

  '@vue/shared@3.2.47': {}

  '@vue/shared@3.3.11': {}

  '@vue/tsconfig@0.1.3(@types/node@20.10.4)':
    optionalDependencies:
      '@types/node': 20.10.4

  '@webassemblyjs/ast@1.11.6':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6

  '@webassemblyjs/floating-point-hex-parser@1.11.6': {}

  '@webassemblyjs/helper-api-error@1.11.6': {}

  '@webassemblyjs/helper-buffer@1.11.6': {}

  '@webassemblyjs/helper-numbers@1.11.6':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.11.6
      '@webassemblyjs/helper-api-error': 1.11.6
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.11.6': {}

  '@webassemblyjs/helper-wasm-section@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-buffer': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/wasm-gen': 1.11.6

  '@webassemblyjs/ieee754@1.11.6':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.11.6':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.11.6': {}

  '@webassemblyjs/wasm-edit@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-buffer': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/helper-wasm-section': 1.11.6
      '@webassemblyjs/wasm-gen': 1.11.6
      '@webassemblyjs/wasm-opt': 1.11.6
      '@webassemblyjs/wasm-parser': 1.11.6
      '@webassemblyjs/wast-printer': 1.11.6

  '@webassemblyjs/wasm-gen@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6

  '@webassemblyjs/wasm-opt@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-buffer': 1.11.6
      '@webassemblyjs/wasm-gen': 1.11.6
      '@webassemblyjs/wasm-parser': 1.11.6

  '@webassemblyjs/wasm-parser@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-api-error': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6

  '@webassemblyjs/wast-printer@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@xtuc/long': 4.2.2

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  abab@2.0.6: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-globals@6.0.0:
    dependencies:
      acorn: 7.4.1
      acorn-walk: 7.2.0

  acorn-import-assertions@1.9.0(acorn@8.11.2):
    dependencies:
      acorn: 8.11.2

  acorn-walk@7.2.0: {}

  acorn@7.4.1: {}

  acorn@8.11.2: {}

  address@1.2.2: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  any-base@1.1.0: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  array-flatten@1.1.1: {}

  asynckit@0.4.0: {}

  autoprefixer@10.4.16(postcss@8.4.32):
    dependencies:
      browserslist: 4.22.2
      caniuse-lite: 1.0.30001568
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  axios@1.6.2:
    dependencies:
      follow-redirects: 1.15.3
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-jest@27.5.1(@babel/core@7.23.5):
    dependencies:
      '@babel/core': 7.23.5
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 27.5.1(@babel/core@7.23.5)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1:
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@27.5.1:
    dependencies:
      '@babel/template': 7.22.15
      '@babel/types': 7.23.5
      '@types/babel__core': 7.20.5
      '@types/babel__traverse': 7.20.4

  babel-plugin-polyfill-corejs2@0.4.6(@babel/core@7.23.5):
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/core': 7.23.5
      '@babel/helper-define-polyfill-provider': 0.4.3(@babel/core@7.23.5)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.8.6(@babel/core@7.23.5):
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-define-polyfill-provider': 0.4.3(@babel/core@7.23.5)
      core-js-compat: 3.34.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.5.3(@babel/core@7.23.5):
    dependencies:
      '@babel/core': 7.23.5
      '@babel/helper-define-polyfill-provider': 0.4.3(@babel/core@7.23.5)
    transitivePeerDependencies:
      - supports-color

  babel-preset-current-node-syntax@1.0.1(@babel/core@7.23.5):
    dependencies:
      '@babel/core': 7.23.5
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-bigint': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.23.5)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.23.5)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.23.5)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.23.5)

  babel-preset-jest@27.5.1(@babel/core@7.23.5):
    dependencies:
      '@babel/core': 7.23.5
      babel-plugin-jest-hoist: 27.5.1
      babel-preset-current-node-syntax: 1.0.1(@babel/core@7.23.5)

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  base64url@3.0.1: {}

  big.js@5.2.2: {}

  binary-extensions@2.2.0: {}

  bmp-js@0.1.0: {}

  body-parser@1.20.1:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browser-process-hrtime@1.0.0: {}

  browserslist@4.22.2:
    dependencies:
      caniuse-lite: 1.0.30001568
      electron-to-chromium: 1.4.609
      node-releases: 2.0.14
      update-browserslist-db: 1.0.13(browserslist@4.22.2)

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-equal@0.0.1: {}

  buffer-from@1.1.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  bytes@3.1.2: {}

  cac@6.7.9: {}

  call-bind@1.0.5:
    dependencies:
      function-bind: 1.1.2
      get-intrinsic: 1.2.2
      set-function-length: 1.1.1

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001568: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  char-regex@1.0.2: {}

  charenc@0.0.2: {}

  chokidar@3.5.3:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chrome-trace-event@1.0.3: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.2.3: {}

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  co@4.6.0: {}

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  commander@4.1.1: {}

  compare-versions@3.6.0: {}

  computeds@0.0.1: {}

  concat-map@0.0.1: {}

  concaveman@1.2.1:
    dependencies:
      point-in-polygon: 1.1.0
      rbush: 3.0.1
      robust-predicates: 2.0.4
      tinyqueue: 2.0.3

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.5.0: {}

  core-js-compat@3.34.0:
    dependencies:
      browserslist: 4.22.2

  core-js@3.34.0: {}

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.3

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypt@0.0.2: {}

  css-blank-pseudo@6.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  css-font-size-keywords@1.0.0: {}

  css-font-stretch-keywords@1.0.1: {}

  css-font-style-keywords@1.0.1: {}

  css-font-weight-keywords@1.0.0: {}

  css-has-pseudo@6.0.0(postcss@8.4.32):
    dependencies:
      '@csstools/selector-specificity': 3.0.0(postcss-selector-parser@6.0.13)
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13
      postcss-value-parser: 4.2.0

  css-list-helpers@2.0.0: {}

  css-prefers-color-scheme@9.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  css-system-font-keywords@1.0.0: {}

  csscolorparser@1.0.3: {}

  cssdb@7.9.0: {}

  cssesc@3.0.0: {}

  cssom@0.3.8: {}

  cssom@0.4.4: {}

  cssstyle@2.3.0:
    dependencies:
      cssom: 0.3.8

  csstype@2.6.21: {}

  d3-array@1.2.4: {}

  d3-geo@1.7.1:
    dependencies:
      d3-array: 1.2.4

  d3-voronoi@1.1.2: {}

  data-urls@2.0.0:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0

  dayjs@1.11.10: {}

  de-indent@1.0.2: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.3.4:
    dependencies:
      ms: 2.1.2

  decimal.js@10.4.3: {}

  decode-uri-component@0.4.1: {}

  dedent@0.7.0: {}

  deep-equal@1.1.2:
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.5
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.1

  deepmerge@4.3.1: {}

  default-gateway@6.0.3:
    dependencies:
      execa: 5.1.1

  define-data-property@1.1.1:
    dependencies:
      get-intrinsic: 1.2.2
      gopd: 1.0.1
      has-property-descriptors: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.1
      has-property-descriptors: 1.0.1
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  density-clustering@1.3.0: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-newline@3.1.0: {}

  didyoumean@1.2.2: {}

  diff-sequences@27.5.1: {}

  dlv@1.1.3: {}

  dom-walk@0.1.2: {}

  domexception@2.0.1:
    dependencies:
      webidl-conversions: 5.0.0

  earcut@2.2.4: {}

  echarts@5.4.3:
    dependencies:
      tslib: 2.3.0
      zrender: 5.4.4

  ee-first@1.1.1: {}

  electron-to-chromium@1.4.609: {}

  emittery@0.8.1: {}

  emoji-regex@8.0.0: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  enhanced-resolve@5.15.0:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-module-lexer@1.4.1: {}

  esbuild@0.16.17:
    optionalDependencies:
      '@esbuild/android-arm': 0.16.17
      '@esbuild/android-arm64': 0.16.17
      '@esbuild/android-x64': 0.16.17
      '@esbuild/darwin-arm64': 0.16.17
      '@esbuild/darwin-x64': 0.16.17
      '@esbuild/freebsd-arm64': 0.16.17
      '@esbuild/freebsd-x64': 0.16.17
      '@esbuild/linux-arm': 0.16.17
      '@esbuild/linux-arm64': 0.16.17
      '@esbuild/linux-ia32': 0.16.17
      '@esbuild/linux-loong64': 0.16.17
      '@esbuild/linux-mips64el': 0.16.17
      '@esbuild/linux-ppc64': 0.16.17
      '@esbuild/linux-riscv64': 0.16.17
      '@esbuild/linux-s390x': 0.16.17
      '@esbuild/linux-x64': 0.16.17
      '@esbuild/netbsd-x64': 0.16.17
      '@esbuild/openbsd-x64': 0.16.17
      '@esbuild/sunos-x64': 0.16.17
      '@esbuild/win32-arm64': 0.16.17
      '@esbuild/win32-ia32': 0.16.17
      '@esbuild/win32-x64': 0.16.17

  esbuild@0.17.19:
    optionalDependencies:
      '@esbuild/android-arm': 0.17.19
      '@esbuild/android-arm64': 0.17.19
      '@esbuild/android-x64': 0.17.19
      '@esbuild/darwin-arm64': 0.17.19
      '@esbuild/darwin-x64': 0.17.19
      '@esbuild/freebsd-arm64': 0.17.19
      '@esbuild/freebsd-x64': 0.17.19
      '@esbuild/linux-arm': 0.17.19
      '@esbuild/linux-arm64': 0.17.19
      '@esbuild/linux-ia32': 0.17.19
      '@esbuild/linux-loong64': 0.17.19
      '@esbuild/linux-mips64el': 0.17.19
      '@esbuild/linux-ppc64': 0.17.19
      '@esbuild/linux-riscv64': 0.17.19
      '@esbuild/linux-s390x': 0.17.19
      '@esbuild/linux-x64': 0.17.19
      '@esbuild/netbsd-x64': 0.17.19
      '@esbuild/openbsd-x64': 0.17.19
      '@esbuild/sunos-x64': 0.17.19
      '@esbuild/win32-arm64': 0.17.19
      '@esbuild/win32-ia32': 0.17.19
      '@esbuild/win32-x64': 0.17.19

  escalade@3.1.1: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  escodegen@2.1.0:
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  esprima@4.0.1: {}

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exif-parser@0.1.12: {}

  exit@0.1.2: {}

  expect@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      jest-get-type: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1

  express@4.18.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0
      serve-static: 1.15.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.2:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  fast-json-stable-stringify@2.1.0: {}

  fastq@1.15.0:
    dependencies:
      reusify: 1.0.4

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  file-type@9.0.0: {}

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  filter-obj@5.1.0: {}

  finalhandler@1.2.0:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  follow-redirects@1.15.3: {}

  form-data@3.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fraction.js@4.3.7: {}

  fresh@0.5.2: {}

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  functions-have-names@1.2.3: {}

  gcoord@1.0.5: {}

  generic-names@4.0.0:
    dependencies:
      loader-utils: 3.2.1

  gensync@1.0.0-beta.2: {}

  geojson-equality@0.1.6:
    dependencies:
      deep-equal: 1.1.2

  geojson-rbush@3.2.0:
    dependencies:
      '@turf/bbox': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      '@types/geojson': 7946.0.8
      rbush: 3.0.1

  geojson-vt@3.2.1: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.2:
    dependencies:
      function-bind: 1.1.2
      has-proto: 1.0.1
      has-symbols: 1.0.3
      hasown: 2.0.0

  get-package-type@0.1.0: {}

  get-stream@6.0.1: {}

  gl-matrix@3.4.3: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@7.1.6:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  global@4.4.0:
    dependencies:
      min-document: 2.19.0
      process: 0.11.10

  globals@11.12.0: {}

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.2

  graceful-fs@4.2.11: {}

  grid-index@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.1:
    dependencies:
      get-intrinsic: 1.2.2

  has-proto@1.0.1: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.0:
    dependencies:
      has-symbols: 1.0.3

  hash-sum@2.0.0: {}

  hasown@2.0.0:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  html-encoding-sniffer@2.0.1:
    dependencies:
      whatwg-encoding: 1.0.5

  html-escaper@2.0.2: {}

  html-tags@3.3.1: {}

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@4.0.1:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  icss-replace-symbols@1.1.0: {}

  icss-utils@5.1.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  ieee754@1.2.1: {}

  immutable@4.3.4: {}

  import-local@3.1.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  invert-kv@3.0.1: {}

  ipaddr.js@1.9.1: {}

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.5
      has-tostringtag: 1.0.0

  is-arrayish@0.2.1: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.2.0

  is-buffer@1.1.6: {}

  is-core-module@2.13.1:
    dependencies:
      hasown: 2.0.0

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.0

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-function@1.0.2: {}

  is-generator-fn@2.1.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.5
      has-tostringtag: 1.0.0

  is-stream@2.0.1: {}

  is-typedarray@1.0.0: {}

  isexe@2.0.0: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      '@babel/core': 7.23.5
      '@babel/parser': 7.23.5
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.3.4
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.6:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jest-changed-files@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      execa: 5.1.1
      throat: 6.0.2

  jest-circus@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      chalk: 4.1.2
      co: 4.6.0
      dedent: 0.7.0
      expect: 27.5.1
      is-generator-fn: 2.1.0
      jest-each: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1
      jest-runtime: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      pretty-format: 27.5.1
      slash: 3.0.0
      stack-utils: 2.0.6
      throat: 6.0.2
    transitivePeerDependencies:
      - supports-color

  jest-cli@27.5.1:
    dependencies:
      '@jest/core': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      import-local: 3.1.0
      jest-config: 27.5.1
      jest-util: 27.5.1
      jest-validate: 27.5.1
      prompts: 2.4.2
      yargs: 16.2.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - ts-node
      - utf-8-validate

  jest-config@27.5.1:
    dependencies:
      '@babel/core': 7.23.5
      '@jest/test-sequencer': 27.5.1
      '@jest/types': 27.5.1
      babel-jest: 27.5.1(@babel/core@7.23.5)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 27.5.1
      jest-environment-jsdom: 27.5.1
      jest-environment-node: 27.5.1
      jest-get-type: 27.5.1
      jest-jasmine2: 27.5.1
      jest-regex-util: 27.5.1
      jest-resolve: 27.5.1
      jest-runner: 27.5.1
      jest-util: 27.5.1
      jest-validate: 27.5.1
      micromatch: 4.0.5
      parse-json: 5.2.0
      pretty-format: 27.5.1
      slash: 3.0.0
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  jest-diff@27.5.1:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 27.5.1
      jest-get-type: 27.5.1
      pretty-format: 27.5.1

  jest-docblock@27.5.1:
    dependencies:
      detect-newline: 3.1.0

  jest-each@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      chalk: 4.1.2
      jest-get-type: 27.5.1
      jest-util: 27.5.1
      pretty-format: 27.5.1

  jest-environment-jsdom@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/fake-timers': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      jest-mock: 27.5.1
      jest-util: 27.5.1
      jsdom: 16.7.0
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  jest-environment-node@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/fake-timers': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      jest-mock: 27.5.1
      jest-util: 27.5.1

  jest-get-type@27.5.1: {}

  jest-haste-map@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      '@types/graceful-fs': 4.1.9
      '@types/node': 20.10.4
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 27.5.1
      jest-serializer: 27.5.1
      jest-util: 27.5.1
      jest-worker: 27.5.1
      micromatch: 4.0.5
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-jasmine2@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/source-map': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      chalk: 4.1.2
      co: 4.6.0
      expect: 27.5.1
      is-generator-fn: 2.1.0
      jest-each: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1
      jest-runtime: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      pretty-format: 27.5.1
      throat: 6.0.2
    transitivePeerDependencies:
      - supports-color

  jest-leak-detector@27.5.1:
    dependencies:
      jest-get-type: 27.5.1
      pretty-format: 27.5.1

  jest-matcher-utils@27.5.1:
    dependencies:
      chalk: 4.1.2
      jest-diff: 27.5.1
      jest-get-type: 27.5.1
      pretty-format: 27.5.1

  jest-message-util@27.5.1:
    dependencies:
      '@babel/code-frame': 7.23.5
      '@jest/types': 27.5.1
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.5
      pretty-format: 27.5.1
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      '@types/node': 20.10.4

  jest-pnp-resolver@1.2.3(jest-resolve@27.5.1):
    optionalDependencies:
      jest-resolve: 27.5.1

  jest-regex-util@27.5.1: {}

  jest-resolve-dependencies@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      jest-regex-util: 27.5.1
      jest-snapshot: 27.5.1
    transitivePeerDependencies:
      - supports-color

  jest-resolve@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-pnp-resolver: 1.2.3(jest-resolve@27.5.1)
      jest-util: 27.5.1
      jest-validate: 27.5.1
      resolve: 1.22.8
      resolve.exports: 1.1.1
      slash: 3.0.0

  jest-runner@27.5.1:
    dependencies:
      '@jest/console': 27.5.1
      '@jest/environment': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      chalk: 4.1.2
      emittery: 0.8.1
      graceful-fs: 4.2.11
      jest-docblock: 27.5.1
      jest-environment-jsdom: 27.5.1
      jest-environment-node: 27.5.1
      jest-haste-map: 27.5.1
      jest-leak-detector: 27.5.1
      jest-message-util: 27.5.1
      jest-resolve: 27.5.1
      jest-runtime: 27.5.1
      jest-util: 27.5.1
      jest-worker: 27.5.1
      source-map-support: 0.5.21
      throat: 6.0.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  jest-runtime@27.5.1:
    dependencies:
      '@jest/environment': 27.5.1
      '@jest/fake-timers': 27.5.1
      '@jest/globals': 27.5.1
      '@jest/source-map': 27.5.1
      '@jest/test-result': 27.5.1
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      chalk: 4.1.2
      cjs-module-lexer: 1.2.3
      collect-v8-coverage: 1.0.2
      execa: 5.1.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 27.5.1
      jest-message-util: 27.5.1
      jest-mock: 27.5.1
      jest-regex-util: 27.5.1
      jest-resolve: 27.5.1
      jest-snapshot: 27.5.1
      jest-util: 27.5.1
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-serializer@27.5.1:
    dependencies:
      '@types/node': 20.10.4
      graceful-fs: 4.2.11

  jest-snapshot@27.5.1:
    dependencies:
      '@babel/core': 7.23.5
      '@babel/generator': 7.23.5
      '@babel/plugin-syntax-typescript': 7.23.3(@babel/core@7.23.5)
      '@babel/traverse': 7.23.5
      '@babel/types': 7.23.5
      '@jest/transform': 27.5.1
      '@jest/types': 27.5.1
      '@types/babel__traverse': 7.20.4
      '@types/prettier': 2.7.3
      babel-preset-current-node-syntax: 1.0.1(@babel/core@7.23.5)
      chalk: 4.1.2
      expect: 27.5.1
      graceful-fs: 4.2.11
      jest-diff: 27.5.1
      jest-get-type: 27.5.1
      jest-haste-map: 27.5.1
      jest-matcher-utils: 27.5.1
      jest-message-util: 27.5.1
      jest-util: 27.5.1
      natural-compare: 1.4.0
      pretty-format: 27.5.1
      semver: 7.5.4
    transitivePeerDependencies:
      - supports-color

  jest-util@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@27.5.1:
    dependencies:
      '@jest/types': 27.5.1
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 27.5.1
      leven: 3.1.0
      pretty-format: 27.5.1

  jest-watcher@27.5.1:
    dependencies:
      '@jest/test-result': 27.5.1
      '@jest/types': 27.5.1
      '@types/node': 20.10.4
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      jest-util: 27.5.1
      string-length: 4.0.2

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 20.10.4
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@27.0.4:
    dependencies:
      '@jest/core': 27.5.1
      import-local: 3.1.0
      jest-cli: 27.5.1
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - ts-node
      - utf-8-validate

  jimp@0.10.3:
    dependencies:
      '@babel/runtime': 7.23.5
      '@jimp/custom': 0.10.3
      '@jimp/plugins': 0.10.3(@jimp/custom@0.10.3)
      '@jimp/types': 0.10.3(@jimp/custom@0.10.3)
      core-js: 3.34.0
      regenerator-runtime: 0.13.11

  jiti@1.21.0: {}

  jpeg-js@0.3.7: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  jsdom@16.7.0:
    dependencies:
      abab: 2.0.6
      acorn: 8.11.2
      acorn-globals: 6.0.0
      cssom: 0.4.4
      cssstyle: 2.3.0
      data-urls: 2.0.0
      decimal.js: 10.4.3
      domexception: 2.0.1
      escodegen: 2.1.0
      form-data: 3.0.1
      html-encoding-sniffer: 2.0.1
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.7
      parse5: 6.0.1
      saxes: 5.0.1
      symbol-tree: 3.2.4
      tough-cookie: 4.1.3
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 2.0.0
      webidl-conversions: 6.1.0
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0
      ws: 7.5.9
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json5@2.2.3: {}

  jsonc-parser@3.2.0: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  kdbush@4.0.2: {}

  kleur@3.0.3: {}

  klona@2.0.6: {}

  lcid@3.1.1:
    dependencies:
      invert-kv: 3.0.1

  leven@3.1.0: {}

  licia@1.39.1: {}

  lilconfig@2.1.0: {}

  lilconfig@3.0.0: {}

  lines-and-columns@1.2.4: {}

  load-bmfont@1.4.1:
    dependencies:
      buffer-equal: 0.0.1
      mime: 1.6.0
      parse-bmfont-ascii: 1.0.6
      parse-bmfont-binary: 1.0.6
      parse-bmfont-xml: 1.1.4
      phin: 2.9.3
      xhr: 2.6.0
      xtend: 4.0.2

  loader-runner@4.3.0: {}

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  loader-utils@3.2.1: {}

  localstorage-polyfill@1.0.1: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash.camelcase@4.3.0: {}

  lodash.debounce@4.0.8: {}

  lodash@4.17.21: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  magic-string@0.25.9:
    dependencies:
      sourcemap-codec: 1.4.8

  magic-string@0.30.5:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15

  make-dir@4.0.0:
    dependencies:
      semver: 7.5.4

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  mapbox-gl@2.15.0:
    dependencies:
      '@mapbox/geojson-rewind': 0.5.2
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/mapbox-gl-supported': 2.0.1
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/tiny-sdf': 2.0.6
      '@mapbox/unitbezier': 0.0.1
      '@mapbox/vector-tile': 1.3.1
      '@mapbox/whoots-js': 3.1.0
      csscolorparser: 1.0.3
      earcut: 2.2.4
      geojson-vt: 3.2.1
      gl-matrix: 3.4.3
      grid-index: 1.1.0
      kdbush: 4.0.2
      murmurhash-js: 1.0.0
      pbf: 3.2.1
      potpack: 2.0.0
      quickselect: 2.0.0
      rw: 1.3.3
      supercluster: 8.0.1
      tinyqueue: 2.0.3
      vt-pbf: 3.1.3

  md5@2.3.0:
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6

  media-typer@0.3.0: {}

  merge-descriptors@1.0.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  merge@2.1.1: {}

  methods@1.1.2: {}

  micromatch@4.0.5:
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mime@3.0.0: {}

  mimic-fn@2.1.0: {}

  min-document@2.19.0:
    dependencies:
      dom-walk: 0.1.2

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.3:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  module-alias@2.2.3: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  muggle-string@0.3.1: {}

  murmurhash-js@1.0.0: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.7: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  neo-async@2.6.2: {}

  node-int64@0.4.0: {}

  node-releases@2.0.14: {}

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  nwsapi@2.2.7: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.1: {}

  object-is@1.1.5:
    dependencies:
      call-bind: 1.0.5
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  omggif@1.0.10: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  os-locale-s-fix@1.0.8-fix-1:
    dependencies:
      lcid: 3.1.1

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-try@2.2.0: {}

  pako@1.0.11: {}

  parse-bmfont-ascii@1.0.6: {}

  parse-bmfont-binary@1.0.6: {}

  parse-bmfont-xml@1.1.4:
    dependencies:
      xml-parse-from-string: 1.0.1
      xml2js: 0.4.23

  parse-css-font@4.0.0:
    dependencies:
      css-font-size-keywords: 1.0.0
      css-font-stretch-keywords: 1.0.1
      css-font-style-keywords: 1.0.1
      css-font-weight-keywords: 1.0.0
      css-list-helpers: 2.0.0
      css-system-font-keywords: 1.0.0
      unquote: 1.1.1

  parse-headers@2.0.5: {}

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.23.5
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5@6.0.1: {}

  parseurl@1.3.3: {}

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-to-regexp@0.1.7: {}

  pbf@3.2.1:
    dependencies:
      ieee754: 1.2.1
      resolve-protobuf-schema: 2.1.0

  phin@2.9.3: {}

  picocolors@1.0.0: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pinia-plugin-unistorage@0.0.17(typescript@4.9.5)(vue@3.2.45):
    dependencies:
      pinia: 2.0.33(typescript@4.9.5)(vue@3.2.45)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - typescript
      - vue

  pinia@2.0.33(typescript@4.9.5)(vue@3.2.45):
    dependencies:
      '@vue/devtools-api': 6.5.1
      vue: 3.2.45
      vue-demi: 0.14.6(vue@3.2.45)
    optionalDependencies:
      typescript: 4.9.5

  pirates@4.0.6: {}

  pixelmatch@4.0.2:
    dependencies:
      pngjs: 3.4.0

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  pngjs@3.4.0: {}

  point-in-polygon@1.1.0: {}

  polygon-clipping@0.15.3:
    dependencies:
      splaytree: 3.1.2

  postcss-attribute-case-insensitive@6.0.2(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-clamp@4.1.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-color-functional-notation@6.0.2(postcss@8.4.32):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-color-hex-alpha@9.0.2(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-color-rebeccapurple@9.0.1(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-custom-media@10.0.2(postcss@8.4.32):
    dependencies:
      '@csstools/cascade-layer-name-parser': 1.0.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/media-query-list-parser': 2.1.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      postcss: 8.4.32

  postcss-custom-properties@13.3.2(postcss@8.4.32):
    dependencies:
      '@csstools/cascade-layer-name-parser': 1.0.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-custom-selectors@7.1.6(postcss@8.4.32):
    dependencies:
      '@csstools/cascade-layer-name-parser': 1.0.5(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-dir-pseudo-class@8.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-double-position-gradients@5.0.2(postcss@8.4.32):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-focus-visible@9.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-focus-within@8.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-font-variant@5.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  postcss-gap-properties@5.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  postcss-image-set-function@6.0.1(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-import@14.1.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8

  postcss-import@15.1.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8

  postcss-js@4.0.1(postcss@8.4.32):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.32

  postcss-lab-function@6.0.7(postcss@8.4.32):
    dependencies:
      '@csstools/css-color-parser': 1.4.0(@csstools/css-parser-algorithms@2.3.2(@csstools/css-tokenizer@2.2.1))(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-parser-algorithms': 2.3.2(@csstools/css-tokenizer@2.2.1)
      '@csstools/css-tokenizer': 2.2.1
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      postcss: 8.4.32

  postcss-load-config@3.1.4(postcss@8.4.32):
    dependencies:
      lilconfig: 2.1.0
      yaml: 1.10.2
    optionalDependencies:
      postcss: 8.4.32

  postcss-load-config@4.0.2(postcss@8.4.32):
    dependencies:
      lilconfig: 3.0.0
      yaml: 2.3.4
    optionalDependencies:
      postcss: 8.4.32

  postcss-logical@7.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-modules-extract-imports@3.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  postcss-modules-local-by-default@4.0.3(postcss@8.4.32):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.32)
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-modules-values@4.0.0(postcss@8.4.32):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.32)
      postcss: 8.4.32

  postcss-modules@4.3.1(postcss@8.4.32):
    dependencies:
      generic-names: 4.0.0
      icss-replace-symbols: 1.1.0
      lodash.camelcase: 4.3.0
      postcss: 8.4.32
      postcss-modules-extract-imports: 3.0.0(postcss@8.4.32)
      postcss-modules-local-by-default: 4.0.3(postcss@8.4.32)
      postcss-modules-scope: 3.0.0(postcss@8.4.32)
      postcss-modules-values: 4.0.0(postcss@8.4.32)
      string-hash: 1.1.3

  postcss-nested@6.0.1(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-nesting@12.0.1(postcss@8.4.32):
    dependencies:
      '@csstools/selector-specificity': 3.0.0(postcss-selector-parser@6.0.13)
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-opacity-percentage@2.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  postcss-overflow-shorthand@5.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-page-break@3.0.4(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  postcss-place@9.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-value-parser: 4.2.0

  postcss-preset-env@9.3.0(postcss@8.4.32):
    dependencies:
      '@csstools/postcss-cascade-layers': 4.0.1(postcss@8.4.32)
      '@csstools/postcss-color-function': 3.0.7(postcss@8.4.32)
      '@csstools/postcss-color-mix-function': 2.0.7(postcss@8.4.32)
      '@csstools/postcss-exponential-functions': 1.0.1(postcss@8.4.32)
      '@csstools/postcss-font-format-keywords': 3.0.0(postcss@8.4.32)
      '@csstools/postcss-gamut-mapping': 1.0.0(postcss@8.4.32)
      '@csstools/postcss-gradients-interpolation-method': 4.0.7(postcss@8.4.32)
      '@csstools/postcss-hwb-function': 3.0.6(postcss@8.4.32)
      '@csstools/postcss-ic-unit': 3.0.2(postcss@8.4.32)
      '@csstools/postcss-initial': 1.0.0(postcss@8.4.32)
      '@csstools/postcss-is-pseudo-class': 4.0.3(postcss@8.4.32)
      '@csstools/postcss-logical-float-and-clear': 2.0.0(postcss@8.4.32)
      '@csstools/postcss-logical-overflow': 1.0.0(postcss@8.4.32)
      '@csstools/postcss-logical-overscroll-behavior': 1.0.0(postcss@8.4.32)
      '@csstools/postcss-logical-resize': 2.0.0(postcss@8.4.32)
      '@csstools/postcss-logical-viewport-units': 2.0.3(postcss@8.4.32)
      '@csstools/postcss-media-minmax': 1.1.0(postcss@8.4.32)
      '@csstools/postcss-media-queries-aspect-ratio-number-values': 2.0.3(postcss@8.4.32)
      '@csstools/postcss-nested-calc': 3.0.0(postcss@8.4.32)
      '@csstools/postcss-normalize-display-values': 3.0.1(postcss@8.4.32)
      '@csstools/postcss-oklab-function': 3.0.7(postcss@8.4.32)
      '@csstools/postcss-progressive-custom-properties': 3.0.2(postcss@8.4.32)
      '@csstools/postcss-relative-color-syntax': 2.0.7(postcss@8.4.32)
      '@csstools/postcss-scope-pseudo-class': 3.0.0(postcss@8.4.32)
      '@csstools/postcss-stepped-value-functions': 3.0.2(postcss@8.4.32)
      '@csstools/postcss-text-decoration-shorthand': 3.0.3(postcss@8.4.32)
      '@csstools/postcss-trigonometric-functions': 3.0.2(postcss@8.4.32)
      '@csstools/postcss-unset-value': 3.0.0(postcss@8.4.32)
      autoprefixer: 10.4.16(postcss@8.4.32)
      browserslist: 4.22.2
      css-blank-pseudo: 6.0.0(postcss@8.4.32)
      css-has-pseudo: 6.0.0(postcss@8.4.32)
      css-prefers-color-scheme: 9.0.0(postcss@8.4.32)
      cssdb: 7.9.0
      postcss: 8.4.32
      postcss-attribute-case-insensitive: 6.0.2(postcss@8.4.32)
      postcss-clamp: 4.1.0(postcss@8.4.32)
      postcss-color-functional-notation: 6.0.2(postcss@8.4.32)
      postcss-color-hex-alpha: 9.0.2(postcss@8.4.32)
      postcss-color-rebeccapurple: 9.0.1(postcss@8.4.32)
      postcss-custom-media: 10.0.2(postcss@8.4.32)
      postcss-custom-properties: 13.3.2(postcss@8.4.32)
      postcss-custom-selectors: 7.1.6(postcss@8.4.32)
      postcss-dir-pseudo-class: 8.0.0(postcss@8.4.32)
      postcss-double-position-gradients: 5.0.2(postcss@8.4.32)
      postcss-focus-visible: 9.0.0(postcss@8.4.32)
      postcss-focus-within: 8.0.0(postcss@8.4.32)
      postcss-font-variant: 5.0.0(postcss@8.4.32)
      postcss-gap-properties: 5.0.0(postcss@8.4.32)
      postcss-image-set-function: 6.0.1(postcss@8.4.32)
      postcss-lab-function: 6.0.7(postcss@8.4.32)
      postcss-logical: 7.0.0(postcss@8.4.32)
      postcss-nesting: 12.0.1(postcss@8.4.32)
      postcss-opacity-percentage: 2.0.0(postcss@8.4.32)
      postcss-overflow-shorthand: 5.0.0(postcss@8.4.32)
      postcss-page-break: 3.0.4(postcss@8.4.32)
      postcss-place: 9.0.0(postcss@8.4.32)
      postcss-pseudo-class-any-link: 9.0.0(postcss@8.4.32)
      postcss-replace-overflow-wrap: 4.0.0(postcss@8.4.32)
      postcss-selector-not: 7.0.1(postcss@8.4.32)
      postcss-value-parser: 4.2.0

  postcss-pseudo-class-any-link@9.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-replace-overflow-wrap@4.0.0(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32

  postcss-selector-not@7.0.1(postcss@8.4.32):
    dependencies:
      postcss: 8.4.32
      postcss-selector-parser: 6.0.13

  postcss-selector-parser@6.0.13:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.32:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.0.2

  potpack@2.0.0: {}

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  process@0.11.10: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  protocol-buffers-schema@3.6.0: {}

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-from-env@1.1.0: {}

  psl@1.9.0: {}

  punycode@2.3.1: {}

  qrcode-reader@1.0.4: {}

  qrcode-terminal@0.12.0: {}

  qs@6.11.0:
    dependencies:
      side-channel: 1.0.4

  query-string@8.1.0:
    dependencies:
      decode-uri-component: 0.4.1
      filter-obj: 5.1.0
      split-on-first: 3.0.0

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  quickselect@1.1.1: {}

  quickselect@2.0.0: {}

  qweather-icons@1.6.0: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.1:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  rbush@2.0.2:
    dependencies:
      quickselect: 1.1.1

  rbush@3.0.1:
    dependencies:
      quickselect: 2.0.0

  react-is@17.0.2: {}

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  regenerate-unicode-properties@10.1.1:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.13.11: {}

  regenerator-runtime@0.14.0: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.23.5

  regexp.prototype.flags@1.5.1:
    dependencies:
      call-bind: 1.0.5
      define-properties: 1.2.1
      set-function-name: 2.0.1

  regexpu-core@5.3.2:
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.1
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0

  regjsparser@0.9.1:
    dependencies:
      jsesc: 0.5.0

  require-directory@2.1.1: {}

  requires-port@1.0.0: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@5.0.0: {}

  resolve-protobuf-schema@2.1.0:
    dependencies:
      protocol-buffers-schema: 3.6.0

  resolve.exports@1.1.1: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  reusify@1.0.4: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  robust-predicates@2.0.4: {}

  rollup@3.29.4:
    optionalDependencies:
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rw@1.3.3: {}

  safe-area-insets@1.4.1: {}

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  sass-loader@10.5.0(sass@1.69.5)(webpack@5.89.0):
    dependencies:
      klona: 2.0.6
      loader-utils: 2.0.4
      neo-async: 2.6.2
      schema-utils: 3.3.0
      semver: 7.5.4
      webpack: 5.89.0
    optionalDependencies:
      sass: 1.69.5

  sass@1.69.5:
    dependencies:
      chokidar: 3.5.3
      immutable: 4.3.4
      source-map-js: 1.0.2

  sax@1.3.0: {}

  saxes@5.0.1:
    dependencies:
      xmlchars: 2.2.0

  schema-utils@3.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  semver@6.3.1: {}

  semver@7.5.4:
    dependencies:
      lru-cache: 6.0.0

  send@0.18.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.1:
    dependencies:
      randombytes: 2.1.0

  serve-static@1.15.0:
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0
    transitivePeerDependencies:
      - supports-color

  set-function-length@1.1.1:
    dependencies:
      define-data-property: 1.1.1
      get-intrinsic: 1.2.2
      gopd: 1.0.1
      has-property-descriptors: 1.0.1

  set-function-name@2.0.1:
    dependencies:
      define-data-property: 1.1.1
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.1

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel@1.0.4:
    dependencies:
      call-bind: 1.0.5
      get-intrinsic: 1.2.2
      object-inspect: 1.13.1

  signal-exit@3.0.7: {}

  sisteransi@1.0.5: {}

  skmeans@0.9.7: {}

  slash@3.0.0: {}

  source-map-js@1.0.2: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  sourcemap-codec@1.4.8: {}

  splaytree@3.1.2: {}

  split-on-first@3.0.0: {}

  sprintf-js@1.0.3: {}

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  statuses@2.0.1: {}

  string-hash@1.1.3: {}

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-json-comments@3.1.1: {}

  sucrase@3.34.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      commander: 4.1.1
      glob: 7.1.6
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supercluster@8.0.1:
    dependencies:
      kdbush: 4.0.2

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-hyperlinks@2.3.0:
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-tags@1.0.0: {}

  symbol-tree@3.2.4: {}

  systemjs@6.14.2: {}

  tailwindcss@3.3.6:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.5.3
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.2
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.0
      lilconfig: 2.1.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.0.0
      postcss: 8.4.32
      postcss-import: 15.1.0(postcss@8.4.32)
      postcss-js: 4.0.1(postcss@8.4.32)
      postcss-load-config: 4.0.2(postcss@8.4.32)
      postcss-nested: 6.0.1(postcss@8.4.32)
      postcss-selector-parser: 6.0.13
      resolve: 1.22.8
      sucrase: 3.34.0
    transitivePeerDependencies:
      - ts-node

  tapable@2.2.1: {}

  terminal-link@2.1.1:
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.3.0

  terser-webpack-plugin@5.3.9(webpack@5.89.0):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.20
      jest-worker: 27.5.1
      schema-utils: 3.3.0
      serialize-javascript: 6.0.1
      terser: 5.26.0
      webpack: 5.89.0

  terser@5.26.0:
    dependencies:
      '@jridgewell/source-map': 0.3.5
      acorn: 8.11.2
      commander: 2.20.3
      source-map-support: 0.5.21

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throat@6.0.2: {}

  timm@1.7.1: {}

  tinycolor2@1.6.0: {}

  tinyqueue@2.0.3: {}

  tmpl@1.0.5: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  topojson-client@3.1.0:
    dependencies:
      commander: 2.20.3

  topojson-server@3.0.1:
    dependencies:
      commander: 2.20.3

  tough-cookie@4.1.3:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@2.1.0:
    dependencies:
      punycode: 2.3.1

  ts-interface-checker@0.1.13: {}

  tslib@2.3.0: {}

  turf-jsts@1.2.3: {}

  type-detect@4.0.8: {}

  type-fest@0.21.3: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typedarray-to-buffer@3.1.5:
    dependencies:
      is-typedarray: 1.0.0

  typescript@4.9.5: {}

  undici-types@5.26.5: {}

  uni-mini-router@0.1.5: {}

  uni-parse-pages@0.0.1: {}

  uniapp-axios-adapter@0.3.2(axios@1.6.2):
    dependencies:
      axios: 1.6.2

  unicode-canonical-property-names-ecmascript@2.0.0: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.1.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  universalify@0.2.0: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  unquote@1.1.1: {}

  update-browserslist-db@1.0.13(browserslist@4.22.2):
    dependencies:
      browserslist: 4.22.2
      escalade: 3.1.1
      picocolors: 1.0.0

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  utif@2.0.1:
    dependencies:
      pako: 1.0.11

  util-deprecate@1.0.2: {}

  utils-merge@1.0.1: {}

  v8-to-istanbul@8.1.1:
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 1.9.0
      source-map: 0.7.4

  vary@1.1.2: {}

  vite-plugin-require-transform@1.0.21:
    dependencies:
      '@babel/generator': 7.23.5
      '@babel/parser': 7.23.5
      '@babel/traverse': 7.23.5
      '@babel/types': 7.23.5
    transitivePeerDependencies:
      - supports-color

  vite@4.1.4(@types/node@20.10.4)(sass@1.69.5)(terser@5.26.0):
    dependencies:
      esbuild: 0.16.17
      postcss: 8.4.32
      resolve: 1.22.8
      rollup: 3.29.4
    optionalDependencies:
      '@types/node': 20.10.4
      fsevents: 2.3.3
      sass: 1.69.5
      terser: 5.26.0

  vk-uview-ui@1.5.2: {}

  vt-pbf@3.1.3:
    dependencies:
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/vector-tile': 1.3.1
      pbf: 3.2.1

  vue-demi@0.14.6(vue@3.2.45):
    dependencies:
      vue: 3.2.45

  vue-router@4.2.5(vue@3.2.45):
    dependencies:
      '@vue/devtools-api': 6.5.1
      vue: 3.2.45

  vue-template-compiler@2.7.15:
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  vue-tsc@1.8.25(typescript@4.9.5):
    dependencies:
      '@volar/typescript': 1.11.1
      '@vue/language-core': 1.8.25(typescript@4.9.5)
      semver: 7.5.4
      typescript: 4.9.5

  vue@3.2.45:
    dependencies:
      '@vue/compiler-dom': 3.2.45
      '@vue/compiler-sfc': 3.2.45
      '@vue/runtime-dom': 3.2.45
      '@vue/server-renderer': 3.2.45(vue@3.2.45)
      '@vue/shared': 3.2.45

  w3c-hr-time@1.0.2:
    dependencies:
      browser-process-hrtime: 1.0.0

  w3c-xmlserializer@2.0.0:
    dependencies:
      xml-name-validator: 3.0.0

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  watchpack@2.4.0:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  webidl-conversions@5.0.0: {}

  webidl-conversions@6.1.0: {}

  webpack-sources@3.2.3: {}

  webpack@5.89.0:
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.5
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/wasm-edit': 1.11.6
      '@webassemblyjs/wasm-parser': 1.11.6
      acorn: 8.11.2
      acorn-import-assertions: 1.9.0(acorn@8.11.2)
      browserslist: 4.22.2
      chrome-trace-event: 1.0.3
      enhanced-resolve: 5.15.0
      es-module-lexer: 1.4.1
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 3.3.0
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.9(webpack@5.89.0)
      watchpack: 2.4.0
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  whatwg-encoding@1.0.5:
    dependencies:
      iconv-lite: 0.4.24

  whatwg-mimetype@2.3.0: {}

  whatwg-url@8.7.0:
    dependencies:
      lodash: 4.17.21
      tr46: 2.1.0
      webidl-conversions: 6.1.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  write-file-atomic@3.0.3:
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5

  ws@7.5.9: {}

  ws@8.15.0: {}

  xhr@2.6.0:
    dependencies:
      global: 4.4.0
      is-function: 1.0.2
      parse-headers: 2.0.5
      xtend: 4.0.2

  xml-name-validator@3.0.0: {}

  xml-parse-from-string@1.0.1: {}

  xml2js@0.4.23:
    dependencies:
      sax: 1.3.0
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xmlchars@2.2.0: {}

  xmlhttprequest@1.8.0: {}

  xregexp@3.1.0: {}

  xtend@4.0.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@1.10.2: {}

  yaml@2.3.4: {}

  yargs-parser@20.2.9: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  z-paging@2.6.3: {}

  zrender@5.4.4:
    dependencies:
      tslib: 2.3.0
