<template>
  <view class="page-nav-top-common-bg1-white">
    <NavBar title="用水调度" :background="{ backgroundColor: 'transparent' }"></NavBar>
    <u-top-tips ref="uNotifyRef" navbar-height="44"></u-top-tips>

    <view class="relative drop-down">
      <u-dropdown
        ref="dropdownRef"
        height="80"
        title-size="28"
        menu-icon="arrow-down-fill"
        menu-icon-size="20"
        style="margin: 0"
        @open="onDropdownOpen"
      >
        <u-dropdown-item
          height="700"
          v-model="state.schedulingType.value"
          :title="state.schedulingType.title"
          :options="schedulingTypeOptions"
          @change="onSchedulingTypeChange"
        ></u-dropdown-item>

        <u-dropdown-item :title="state.timePicker.title"></u-dropdown-item>

        <u-dropdown-item
          height="700"
          v-model="state.schedulingStatus.value"
          :title="state.schedulingStatus.title"
          :options="schedulingPhaseOptions"
          @change="onSchedulingStatusChange"
        ></u-dropdown-item>
      </u-dropdown>
    </view>
  </view>

  <ZPaging
    ref="pagingRef"
    :style="{ marginTop: `calc(44px + ${userStore.statusBarHeight}px + 100rpx)`, paddingTop: '28rpx', zIndex: 10 }"
    v-model="state.list"
    @query="getList"
  >
    <view class="card-item" v-for="(el, idx) in state.list" :key="idx">
      <text
        class="status"
        :class="['scheduling-status-bg' + el?.schedulingStatus, el?.schedulingStatus === 1 ? 'padL40' : 'padL56']"
      >
        {{ schedulingPhaseOptions.find(ele => ele.value == el.schedulingStatus)?.label }}
      </text>
      <view class="top">
        <view class="type" :class="['type' + el?.schedulingType]">
          {{ schedulingTypeOptions.find(ele => ele.value == el.schedulingType)?.label }}
        </view>
        <view class="number">{{ el.serialNumber }}</view>
      </view>
      <view class="small-card">
        <view class="col">
          <text class="label">计划调度水量</text>
          <text class="value">{{ el.waterValue == null ? '-' : el.waterValue + '万m³' }}</text>
        </view>
        <view class="col">
          <text class="label">水库放水量</text>
          <text class="value">{{ el.waterFlow == null ? '-' : el.waterFlow + '万m³' }}</text>
        </view>
        <view class="col">
          <text class="label">田间总用水量</text>
          <text class="value">{{ el.fieldWaterFlow == null ? '-' : el.fieldWaterFlow + '万m³' }}</text>
        </view>
        <view class="col">
          <text class="label">实际调度时长</text>
          <text class="value">{{ el.hour == null ? '-' : el.hour + 'h' }}</text>
        </view>
      </view>
      <view class="bottom">
        <text class="time">{{ el.waterDate?.substring(0, 11) }}</text>
        <view class="btn-group">
          <u-button class="btn-default" @click="onClickDetails(el)">详情</u-button>
          <u-button
            class="btn-primary"
            v-if="state.curDeptId == 10021 && el.schedulingStatus == 1"
            @click="onClickScheduling('start', el)"
          >
            开始调度
          </u-button>
          <u-button
            class="btn-primary"
            v-if="state.curDeptId == 10021 && el.schedulingStatus == 2"
            @click="onClickScheduling('end', el)"
          >
            结束调度
          </u-button>
          <u-button
            v-if="el.deptIds?.length > 0 && el?.deptIds?.includes(state.curDeptId) && el.schedulingStatus == 3"
            class="btn-primary"
            @click="onClickScheduling('review', el)"
          >
            水量复核
          </u-button>
        </view>
      </view>
    </view>
  </ZPaging>

  <u-calendar
    :max-date="dayjs().add(100, 'year').format('YYYY-MM-DD')"
    :key="state.calendarKey"
    v-model="state.timePicker.showPicker"
    mode="range"
    @change="onDateRangeChange"
    @close="onTimeClose"
  >
    <template #tooltip>
      <view class="btn-reset" @click="onDateReset">重置</view>
    </template>
  </u-calendar>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue'
  import { useRouter } from 'uni-mini-router'
  import { useUserStore } from '@/store/modules/user'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import * as _ from 'lodash'
  import { getWaterLedgerPage } from './services'
  import dayjs from 'dayjs'

  const userStore = useUserStore()
  const router = useRouter()

  const uNotifyRef = ref()
  const pagingRef = ref(null)
  const dropdownRef = ref(null)

  const schedulingTypeOptions = [
    { label: '灌溉调度', value: 1 },
    { label: '防汛调度', value: 2 },
  ]
  const schedulingPhaseOptions = [
    { label: '下发调度单', value: 1 },
    { label: '用水调度', value: 2 },
    { label: '水量复核', value: 3 },
    { label: '调度完成', value: 4 },
  ]

  const state = reactive({
    searchVal: undefined,
    curDeptId: JSON.parse(uni.getStorageSync('user'))?.loginOrg?.loginOrgId,
    schedulingType: { value: undefined, title: '调度类型', preVal: undefined },
    groupList: [],
    schedulingStatus: { value: undefined, title: '调度阶段', preVal: undefined },

    calendarKey: 1,
    timePicker: {
      showPicker: false,
      defaultTime: dayjs().format('YYYY-MM-DD'),
      title: '调度时间',
      startTime: undefined,
      endTime: undefined,
    },
    list: [],
  })

  onShow(() => {
    getList(1)
  })

  const getList = (pageNo, pageSize) => {
    getWaterLedgerPage({
      serialNumber: undefined,
      schedulingType: state.schedulingType.value,
      schedulingStatus: state.schedulingStatus.value,

      startTime: state.timePicker.startTime,
      endTime: state.timePicker.endTime,
      pageNum: pageNo || 1,
      pageSize: 10,
    }).then(res => {
      state.list = res.data?.data || []
      pagingRef.value.complete(res.data?.data || [])
      if (pageNo && pageNo == 1) {
        pagingRef.value.scrollToTop()
      }
    })
  }

  function onSchedulingTypeChange(value) {
    if (value === state.schedulingType.preVal) {
      state.schedulingType = { title: '调度类型', value: undefined, preVal: undefined }
    } else {
      state.schedulingType = {
        title: schedulingTypeOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }

    pagingRef.value.reload()
  }

  function onSchedulingStatusChange(value) {
    if (value === state.schedulingStatus.preVal) {
      state.schedulingStatus = { title: '调度阶段', value: undefined, preVal: undefined }
    } else {
      state.schedulingStatus = {
        title: schedulingPhaseOptions.find(el => el.value == value)?.label,
        value: value,
        preVal: value,
      }
    }
    pagingRef.value.reload()
  }

  const onDropdownOpen = index => {
    if (index === 1) {
      nextTick(() => {
        state.timePicker.showPicker = true
      })
    }
  }

  const onDateRangeChange = val => {
    state.timePicker = {
      ...state.timePicker,
      startTime: val.startDate + ' 00:00:00',
      endTime: val.endDate + ' 23:59:59',
    }

    pagingRef.value.reload()
    dropdownRef.value.close()
    dropdownRef.value.highlight(1)
  }

  const onDateReset = () => {
    state.calendarKey += 1
    state.timePicker = {
      ...state.timePicker,
      startTime: undefined,
      endTime: undefined,
    }

    pagingRef.value.reload()
    state.timePicker.showPicker = false
    dropdownRef.value.close()
    dropdownRef.value.highlight()
  }
  const onTimeClose = () => {
    dropdownRef.value?.close()
  }
  //详情
  const onClickDetails = el => {
    router.push({
      path: '/pages/water-scheduling/details/index',
      query: {
        consumptionId: el.consumptionId,
      },
    })
  }
  const onClickScheduling = (source, el) => {
    router.push({
      path: '/pages/water-scheduling/to-review/index',
      query: {
        consumptionId: el.consumptionId,
        source: source,
        startDate: el.startDate,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .search-input {
    padding: 10rpx 34rpx 15rpx;
    position: relative;
    .icon-search {
      position: absolute;
      left: 60rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    :deep(.u-input.u-input--border) {
      padding-left: 70rpx !important;
      border-radius: 100rpx;
    }
  }
  .drop-down {
    .dropdown-time {
      width: 53%;
      flex: inherit;
    }
  }
  .card-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 30rpx 30rpx;
    overflow: hidden;
    position: relative;
    .status {
      position: absolute;
      right: 0;
      width: 194.21rpx;
      height: 52rpx;
      line-height: 52rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      &.padL56 {
        padding-left: 56rpx;
      }
      &.padL40 {
        padding-left: 40rpx;
      }
      &.scheduling-status-bg1 {
        background: url('@/static/images/scheduling-status-bg1.png') no-repeat 0 0 / 100% 100%;
      }
      &.scheduling-status-bg2 {
        background: url('@/static/images/scheduling-status-bg2.png') no-repeat 0 0 / 100% 100%;
      }
      &.scheduling-status-bg3 {
        background: url('@/static/images/scheduling-status-bg3.png') no-repeat 0 0 / 100% 100%;
      }
      &.scheduling-status-bg4 {
        background: url('@/static/images/scheduling-status-bg4.png') no-repeat 0 0 / 100% 100%;
      }
    }
    .top {
      display: flex;
      padding-top: 40rpx;
      padding-left: 38rpx;
      .type {
        width: 128rpx;
        height: 44rpx;
        line-height: 40rpx;
        font-weight: 400;
        font-size: 28rpx;
        text-align: center;
        border-radius: 4rpx;

        &.type1 {
          color: #165dff;
          background: #e8f3ff;
          border: 2rpx solid #94bfff;
        }
        &.type2 {
          color: #f53f3f;
          background: #ffece8;
          border: 2rpx solid #fbaca3;
        }
      }
      .number {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 30rpx;
        color: #1d2129;
        margin-left: 8rpx;
        padding-top: 4rpx;
      }
    }
    .small-card {
      width: 90%;
      margin: 20rpx auto;
      background: #f7f8fa;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 2rpx solid #f2f3f5;
      display: flex;
      flex-wrap: wrap;
      padding-bottom: 15rpx;
      .col {
        width: 50%;
        padding: 20rpx 18rpx 0;
      }
      .label {
        font-weight: 400;
        font-size: 28rpx;
        color: #4e5969;
        display: block;
        margin-bottom: 6rpx;
      }
      .value {
        font-weight: 500;
        font-size: 28rpx;
        color: #1d2129;
      }
    }
    .bottom {
      display: flex;
      padding: 10rpx 30rpx 30rpx;
      .time {
        font-weight: 400;
        font-size: 24rpx;
        color: #86909c;
        line-height: 60rpx;
      }
      :deep(.u-hairline-border:after) {
        border-color: transparent;
      }
      .btn-group {
        display: flex;
        margin-left: auto;
      }
      .btn-default {
        width: 196rpx;
        height: 72rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #1d2129;
        background: #f7f8fa;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        border: 2rpx solid #e5e6eb;
      }
      .btn-primary {
        width: 196rpx;
        height: 72rpx;
        margin-left: 14rpx;
        line-height: 72rpx;
        text-align: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #165dff;
        background: #e8f3ff;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        border: 2rpx solid #165dff;
      }
    }
  }

  .btn-reset {
    padding: 32rpx 12rpx 12rpx 32rpx;
  }
</style>
